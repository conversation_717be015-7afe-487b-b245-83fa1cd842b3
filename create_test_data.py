#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试数据脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.client import Client
from app.models.content import Content
from app.models.template import Template, TemplateCategory
from app.models.task import Task
from app.models.batch import Batch
from app.models.user import User

def create_test_data():
    """创建测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已有测试数据
            existing_content = Content.query.filter_by(workflow_status='pending_publish').first()
            if existing_content:
                print("已存在待发布的测试数据")
                print(f"文案ID: {existing_content.id}")
                print(f"标题: {existing_content.title}")
                print(f"状态: {existing_content.workflow_status}")
                return
            
            # 获取或创建测试客户
            client = Client.query.filter_by(name='测试客户').first()
            if not client:
                client = Client(
                    name='测试客户',
                    contact='测试联系人',
                    phone='13800138000',
                    email='<EMAIL>',
                    need_review=False,  # 不需要客户审核，直接到待发布状态
                    daily_content_count=10,
                    status=True
                )
                db.session.add(client)
                db.session.commit()
                print(f"创建测试客户: {client.name} (ID: {client.id})")
            
            # 获取或创建模板分类
            category = TemplateCategory.query.filter_by(name='测试分类').first()
            if not category:
                category = TemplateCategory(name='测试分类')
                db.session.add(category)
                db.session.commit()
            
            # 获取或创建模板
            template = Template.query.filter_by(title='测试模板').first()
            if not template:
                # 获取第一个用户作为创建者
                user = User.query.first()
                if not user:
                    print("错误：数据库中没有用户，请先创建用户")
                    return
                
                template = Template(
                    title='测试模板',
                    content='这是一个测试文案模板，用于API测试。\n\n包含以下内容：\n- 产品介绍\n- 使用体验\n- 推荐理由',
                    category_id=category.id,
                    creator_id=user.id,
                    status=True
                )
                db.session.add(template)
                db.session.commit()
            
            # 获取或创建任务
            task = Task.query.filter_by(name='API测试任务').first()
            if not task:
                user = User.query.first()
                task = Task(
                    name='API测试任务',
                    description='用于API测试的任务',
                    client_id=client.id,
                    template_id=template.id,
                    target_count=5,
                    actual_count=0,
                    status='active',
                    created_by=user.id
                )
                db.session.add(task)
                db.session.commit()
            
            # 获取或创建批次
            batch = Batch.query.filter_by(name='API测试批次').first()
            if not batch:
                user = User.query.first()
                batch = Batch(
                    name='API测试批次',
                    description='用于API测试的批次',
                    task_id=task.id,
                    target_count=5,
                    actual_count=0,
                    status='active',
                    created_by=user.id
                )
                db.session.add(batch)
                db.session.commit()
            
            # 创建测试文案
            test_contents = [
                {
                    'title': '小红书种草神器推荐',
                    'content': '最近发现了一个超好用的产品！真的是太惊喜了～\n\n用了一段时间，效果真的很棒，强烈推荐给大家！\n\n#种草分享 #好物推荐',
                    'topics': ['种草分享', '好物推荐', '生活分享'],
                    'location': '上海·静安区',
                    'priority': 'high'
                },
                {
                    'title': '今日穿搭分享',
                    'content': '今天的穿搭分享来啦～\n\n这套搭配简约又时尚，很适合日常出街！\n\n大家觉得怎么样呢？\n\n#穿搭分享 #时尚搭配',
                    'topics': ['穿搭分享', '时尚搭配', '日常穿搭'],
                    'location': '北京·朝阳区',
                    'priority': 'normal'
                },
                {
                    'title': '美食探店记录',
                    'content': '今天去了一家超棒的餐厅！\n\n环境很好，菜品也很精致，服务态度也很棒～\n\n推荐给喜欢美食的朋友们！\n\n#美食探店 #餐厅推荐',
                    'topics': ['美食探店', '餐厅推荐', '生活记录'],
                    'location': '广州·天河区',
                    'priority': 'normal'
                }
            ]
            
            user = User.query.first()
            created_count = 0
            
            for i, content_data in enumerate(test_contents):
                content = Content(
                    title=content_data['title'],
                    content=content_data['content'],
                    topics=json.dumps(content_data['topics']),
                    location=content_data['location'],
                    client_id=client.id,
                    task_id=task.id,
                    batch_id=batch.id,
                    template_id=template.id,
                    workflow_status='pending_publish',  # 直接设置为待发布状态
                    publish_status='unpublished',
                    publish_priority=content_data['priority'],
                    created_by=user.id if user else None,
                    created_at=datetime.now()
                )
                db.session.add(content)
                created_count += 1
            
            db.session.commit()
            
            print(f"成功创建 {created_count} 篇测试文案")
            print("文案状态: pending_publish (待发布)")
            print("现在可以使用API测试页面进行测试了！")
            
            # 显示创建的文案信息
            contents = Content.query.filter_by(workflow_status='pending_publish').all()
            print("\n创建的测试文案列表:")
            for content in contents:
                print(f"- ID: {content.id}, 标题: {content.title}, 优先级: {content.publish_priority}")
            
        except Exception as e:
            print(f"创建测试数据失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_test_data()
