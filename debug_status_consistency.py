#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试状态显示不一致的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.content import Content
from app.models.client import ClientShareLink
from app.utils.share_link import ShareLinkGenerator

def debug_status_consistency():
    """调试状态显示不一致的问题"""
    print("🔍 调试状态显示不一致的问题...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看具体文案的状态
            print("1. 查看具体文案的状态:")
            print("-" * 40)
            
            share_key = '4dbc790d5015faeca985cb74da6f43fb'
            share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
            
            if not share_link:
                print("❌ 分享链接不存在")
                return
                
            client_id = share_link.client_id
            print(f"🔍 客户ID: {client_id}")
            
            # 查看该客户的所有文案
            contents = Content.query.filter_by(client_id=client_id, is_deleted=False).all()
            
            print(f"\n📋 客户的所有文案 (共{len(contents)}篇):")
            for content in contents:
                print(f"  文案ID: {content.id}")
                print(f"    标题: {content.title[:30]}...")
                print(f"    工作流状态: {content.workflow_status}")
                print(f"    客户审核状态: {content.client_review_status}")
                print(f"    发布状态: {content.publish_status or 'None'}")
                
                # 模拟前端状态判断逻辑
                if content.publish_status == 'published':
                    display_status = "已发布"
                elif content.client_review_status == 'approved':
                    if content.workflow_status == 'ready_to_publish':
                        display_status = "待发布"
                    elif content.workflow_status == 'pending_publish':
                        display_status = "待发布"
                    elif content.workflow_status == 'publishing':
                        display_status = "发布中"
                    else:
                        display_status = "已通过"
                elif content.client_review_status == 'pending' and content.workflow_status == 'pending_client_review':
                    display_status = "待审核"
                else:
                    display_status = "待审核"
                
                print(f"    前端显示状态: {display_status}")
                print()
            
            # 2. 检查统计逻辑
            print("2. 检查统计逻辑:")
            print("-" * 40)
            
            stats = ShareLinkGenerator.get_share_link_stats(share_key)
            
            if stats:
                print(f"📊 API统计结果:")
                print(f"  - 总文案数量: {stats['total_count']}")
                print(f"  - 待审核数量: {stats['pending_count']}")
                print(f"  - 已通过数量: {stats['approved_count']}")
                print(f"  - 已审核数量: {stats['reviewed_count']}")
                print(f"  - 已发布数量: {stats['published_count']}")
                print(f"  - 已驳回数量: {stats['rejected_count']}")
            
            # 3. 手动验证统计逻辑
            print(f"\n3. 手动验证统计逻辑:")
            print("-" * 40)
            
            # 待审核文案数量（必须是终审通过且客户审核状态为pending）
            pending_contents = Content.query.filter(
                Content.client_id == client_id,
                Content.is_deleted == False,
                Content.workflow_status == 'pending_client_review',
                Content.client_review_status == 'pending'
            ).all()
            
            # 已通过文案数量（只计算未删除的）
            approved_contents = Content.query.filter(
                Content.client_id == client_id,
                Content.is_deleted == False,
                Content.client_review_status == 'approved'
            ).all()
            
            # 已发布文案数量
            published_contents = Content.query.filter(
                Content.client_id == client_id,
                Content.is_deleted == False,
                Content.publish_status == 'published'
            ).all()
            
            # 已审核文案数量（只计算通过的）
            reviewed_contents = approved_contents
            
            print(f"📊 手动统计结果:")
            print(f"  - 待审核数量: {len(pending_contents)}")
            print(f"  - 已通过数量: {len(approved_contents)}")
            print(f"  - 已发布数量: {len(published_contents)}")
            print(f"  - 已审核数量: {len(reviewed_contents)}")
            
            # 4. 分析问题
            print(f"\n4. 问题分析:")
            print("-" * 40)
            
            print("🔍 问题分析:")
            
            for content in contents:
                print(f"\n📄 文案 {content.id}:")
                print(f"  - workflow_status: {content.workflow_status}")
                print(f"  - client_review_status: {content.client_review_status}")
                print(f"  - publish_status: {content.publish_status or 'None'}")
                
                # 判断应该计入哪个统计
                is_pending = (content.workflow_status == 'pending_client_review' and 
                            content.client_review_status == 'pending')
                is_approved = (content.client_review_status == 'approved')
                is_published = (content.publish_status == 'published')
                
                print(f"  - 计入待审核: {'✅' if is_pending else '❌'}")
                print(f"  - 计入已通过: {'✅' if is_approved else '❌'}")
                print(f"  - 计入已发布: {'✅' if is_published else '❌'}")
                
                # 前端显示状态
                if content.publish_status == 'published':
                    frontend_status = "已发布"
                elif content.client_review_status == 'approved':
                    if content.workflow_status in ['ready_to_publish', 'pending_publish']:
                        frontend_status = "待发布"
                    elif content.workflow_status == 'publishing':
                        frontend_status = "发布中"
                    else:
                        frontend_status = "已通过"
                else:
                    frontend_status = "待审核"
                
                print(f"  - 前端显示: {frontend_status}")
            
            # 5. 问题总结
            print(f"\n5. 问题总结:")
            print("-" * 40)
            
            print("❌ 发现的问题:")
            print("  1. 文案的 publish_status 可能不是 'published'")
            print("  2. 但前端可能基于其他条件显示为'已发布'")
            print("  3. 统计逻辑只计算 publish_status = 'published' 的文案")
            print("  4. 导致统计和显示不一致")
            
            print(f"\n✅ 解决方案:")
            print("  1. 统一状态判断逻辑")
            print("  2. 确保前端显示和统计使用相同的条件")
            print("  3. 修复状态不一致的数据")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 状态一致性调试完成！")

if __name__ == '__main__':
    debug_status_consistency()
