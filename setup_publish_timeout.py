#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置发布超时配置
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.system_setting import SystemSetting

def setup_publish_timeout():
    """设置发布超时配置"""
    app = create_app()
    
    with app.app_context():
        try:
            # 设置发布超时时间为2分钟（用于测试）
            timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
            if timeout_setting:
                timeout_setting.value = '120'  # 2分钟 = 120秒
                timeout_setting.description = '发布超时时间（秒）- 测试设置为2分钟'
                print("更新发布超时设置为2分钟")
            else:
                timeout_setting = SystemSetting(
                    key='PUBLISH_TIMEOUT',
                    value='120',  # 2分钟
                    description='发布超时时间（秒）- 测试设置为2分钟'
                )
                db.session.add(timeout_setting)
                print("创建发布超时设置为2分钟")
            
            # 设置超时处理策略
            action_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
            if action_setting:
                action_setting.value = 'keep_timeout'
                action_setting.description = '超时处理策略：保持超时状态'
                print("更新超时处理策略")
            else:
                action_setting = SystemSetting(
                    key='PUBLISH_TIMEOUT_ACTION',
                    value='keep_timeout',
                    description='超时处理策略：保持超时状态'
                )
                db.session.add(action_setting)
                print("创建超时处理策略")
            
            db.session.commit()
            
            print("✅ 发布超时配置设置成功！")
            print("- 超时时间：2分钟（120秒）")
            print("- 处理策略：保持超时状态")
            print("\n现在可以测试API超时功能：")
            print("1. 调用API获取文案（状态变为publishing）")
            print("2. 等待2分钟不返回状态")
            print("3. 系统会自动将状态标记为publish_timeout")
            
        except Exception as e:
            print(f"设置发布超时配置失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    setup_publish_timeout()
