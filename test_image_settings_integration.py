#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片设置完整集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting
from app.utils.image_handler import ImageUploadHandler

def test_image_settings_integration():
    """测试图片设置完整集成"""
    print("📸 测试图片设置完整集成...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 验证系统设置
            print("1. 验证系统设置:")
            print("-" * 40)
            
            settings_to_check = [
                ('IMAGE_UPLOAD_ALLOWED_TYPES', '允许的图片格式'),
                ('IMAGE_UPLOAD_MAX_SIZE', '图片最大大小'),
                ('MAX_IMAGES_PER_CONTENT', '每篇文案最大图片数量')
            ]
            
            for key, description in settings_to_check:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"✅ {description}: {setting.value}")
                else:
                    print(f"❌ {description}: 未设置")
            
            # 2. 测试ImageUploadHandler
            print(f"\n2. 测试ImageUploadHandler:")
            print("-" * 40)
            
            try:
                handler = ImageUploadHandler()
                print(f"✅ ImageUploadHandler初始化成功")
                print(f"   允许的格式: {handler.allowed_extensions}")
                print(f"   最大文件大小: {handler.max_file_size} 字节 ({handler.max_file_size / (1024*1024):.1f}MB)")
                
                # 测试格式验证
                test_files = [
                    'test.jpg',
                    'test.png', 
                    'test.gif',
                    'test.webp',
                    'test.bmp',
                    'test.txt'  # 不支持的格式
                ]
                
                print(f"\n   格式验证测试:")
                for filename in test_files:
                    is_allowed = handler._allowed_file(filename)
                    status = "✅" if is_allowed else "❌"
                    print(f"     {status} {filename}: {'允许' if is_allowed else '不允许'}")
                    
            except Exception as e:
                print(f"❌ ImageUploadHandler测试失败: {e}")
            
            # 3. 测试API接口
            print(f"\n3. 测试API接口:")
            print("-" * 40)
            
            with app.test_client() as client:
                # 先登录
                login_response = client.post('/auth/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=True)
                
                if login_response.status_code == 200:
                    print("✅ 登录成功")
                    
                    # 测试图片限制API
                    api_response = client.get('/simple/api/system/image-limits')
                    print(f"   图片限制API状态码: {api_response.status_code}")
                    
                    if api_response.status_code == 200:
                        try:
                            data = api_response.get_json()
                            if data and data.get('success'):
                                limits = data.get('limits', {})
                                print(f"✅ 图片限制API返回成功")
                                print(f"   最大数量: {limits.get('max_images')} 张")
                                print(f"   最大大小: {limits.get('max_size_mb')} MB")
                                print(f"   允许格式: {limits.get('allowed_types')}")
                            else:
                                print(f"❌ API返回失败: {data.get('message') if data else '无数据'}")
                        except Exception as e:
                            print(f"❌ API响应解析失败: {e}")
                    else:
                        print(f"❌ API调用失败: {api_response.status_code}")
                else:
                    print("❌ 登录失败")
            
            # 4. 验证前端集成
            print(f"\n4. 验证前端集成:")
            print("-" * 40)
            
            try:
                with open('app/templates/image/upload.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键修改
                checks = [
                    ('allowedImageTypes', '允许的图片格式变量'),
                    ('maxImageSizeMB', '最大图片大小变量'),
                    ('validateFiles', '文件验证函数'),
                    ('支持格式：', '格式提示文字'),
                    ('不支持的文件格式', '格式验证错误信息'),
                    ('超过.*MB的限制', '大小验证错误信息')
                ]
                
                for check_text, description in checks:
                    if check_text in content:
                        print(f"✅ {description}")
                    else:
                        print(f"❌ 缺少{description}")
                        
            except Exception as e:
                print(f"❌ 前端文件检查失败: {e}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 图片设置完整集成测试完成！")
    print("\n集成内容:")
    print("1. ✅ 系统设置页面：3个图片相关设置独立分组")
    print("2. ✅ ImageUploadHandler：自动从系统设置读取配置")
    print("3. ✅ 后端API：使用系统设置进行验证")
    print("4. ✅ 前端页面：动态显示限制信息和验证")
    print("\n应用效果:")
    print("- 🎯 修改系统设置后，所有相关功能自动使用新配置")
    print("- 🔧 图片格式限制：根据IMAGE_UPLOAD_ALLOWED_TYPES设置")
    print("- 📏 图片大小限制：根据IMAGE_UPLOAD_MAX_SIZE设置")
    print("- 📊 图片数量限制：根据MAX_IMAGES_PER_CONTENT设置")
    print("- 💡 前端提示：动态显示当前限制信息")
    print("- 🚫 上传验证：前后端都会验证格式、大小、数量")

if __name__ == '__main__':
    test_image_settings_integration()
