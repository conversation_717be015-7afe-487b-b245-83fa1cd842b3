# -*- coding: utf-8 -*-
"""
认证相关视图
"""

from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app.models.user import User
from app.forms.auth import LoginForm, ChangePasswordForm
from app.utils.decorators import ajax_aware

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
@ajax_aware
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main_simple.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is not None and user.verify_password(form.password.data):
            if user.is_active:  # 检查用户是否启用
                login_user(user, remember=form.remember_me.data)
                # 更新最后登录时间
                user.last_login = datetime.now()
                from app.models import db
                db.session.commit()
                
                next_url = request.args.get('next')
                if next_url is None or not next_url.startswith('/'):
                    next_url = url_for('main_simple.index')
                
                # 检查是否是AJAX请求
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    # 对于AJAX请求，返回一个特殊的响应，指示客户端执行完整页面加载
                    return {
                        'redirect': next_url,
                        'force_reload': True  # 添加标记，指示前端进行完整页面加载
                    }
                else:
                    # 对于非AJAX请求，正常重定向
                    return redirect(next_url)
            else:
                flash('账号已被禁用，请联系管理员。', 'danger')
        else:
            flash('用户名或密码错误。', 'danger')
    
    return render_template('auth/login.html', form=form, now=datetime.now())


@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('您已成功登出。', 'success')
    return redirect(url_for('auth.login'))


@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if current_user.verify_password(form.old_password.data):
            current_user.password = form.new_password.data
            from app.models import db
            db.session.commit()
            flash('密码已成功修改。', 'success')
            return redirect(url_for('main_simple.index'))
        else:
            flash('当前密码错误。', 'danger')
    
    return render_template('auth/change_password.html', form=form, now=datetime.now()) 