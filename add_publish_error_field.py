#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加publish_error字段到contents表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def add_publish_error_field():
    """添加publish_error字段到contents表"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 添加publish_error字段到contents表...")
            
            # 检查字段是否已存在
            result = db.session.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'contents' 
                AND COLUMN_NAME = 'publish_error'
            """))
            
            if result.fetchone():
                print("✅ publish_error字段已存在，无需添加")
                return
            
            # 添加字段
            db.session.execute(text("""
                ALTER TABLE contents 
                ADD COLUMN publish_error TEXT COMMENT '发布提示信息（成功或失败的详细信息）'
            """))
            
            db.session.commit()
            print("✅ publish_error字段添加成功！")
            
            # 验证字段是否添加成功
            result = db.session.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'contents' 
                AND COLUMN_NAME = 'publish_error'
            """))
            
            row = result.fetchone()
            if row:
                print(f"字段信息: {row}")
            else:
                print("❌ 字段添加失败")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ 添加字段失败: {e}")

if __name__ == '__main__':
    add_publish_error_field()
