#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试提示信息修复
"""

import requests
import json

def test_message_fix():
    """测试提示信息修复"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔧 测试提示信息修复...")
    print("=" * 50)
    
    # 测试成功状态的提示信息
    print("\n1. 测试成功状态的提示信息...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                
                print(f"获取文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                
                # 发送成功状态和自定义提示信息
                success_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/test/success',
                    'platform': '小红书',
                    'account': '测试账号',
                    'message': '这是一个成功的提示信息测试！获得了很好的反响 🎉'
                }
                
                print(f"发送成功提示: {success_data['message']}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=success_data,
                    timeout=10
                )
                
                if update_response.status_code == 200:
                    result = update_response.json()
                    if result.get('success'):
                        print("✅ 成功状态更新完成！")
                        print(f"文案ID {content_id} 应该在页面显示:")
                        print(f"  时间戳 + '{success_data['message']}'")
                    else:
                        print(f"❌ 状态更新失败: {result.get('error')}")
                else:
                    print(f"❌ 请求失败: {update_response.status_code}")
                
                # 测试失败状态的提示信息
                print(f"\n2. 测试失败状态的提示信息...")
                
                response2 = requests.get(f'{base_url}/content', headers=headers, timeout=10)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        content2 = data2.get('content')
                        content_id2 = content2.get('id')
                        
                        print(f"获取文案ID: {content_id2}")
                        
                        # 发送失败状态和自定义提示信息
                        failed_data = {
                            'status': 'failed',
                            'platform': '小红书',
                            'account': '测试账号',
                            'message': '这是一个失败的提示信息测试！网络连接异常 ❌'
                        }
                        
                        print(f"发送失败提示: {failed_data['message']}")
                        
                        update_response2 = requests.post(
                            f'{base_url}/content/{content_id2}/status',
                            headers=headers,
                            json=failed_data,
                            timeout=10
                        )
                        
                        if update_response2.status_code == 200:
                            result2 = update_response2.json()
                            if result2.get('success'):
                                print("✅ 失败状态更新完成！")
                                print(f"文案ID {content_id2} 应该在页面显示:")
                                print(f"  时间戳 + '{failed_data['message']}'")
                            else:
                                print(f"❌ 状态更新失败: {result2.get('error')}")
                        else:
                            print(f"❌ 请求失败: {update_response2.status_code}")
                    else:
                        print(f"ℹ️ 没有更多文案: {data2.get('message')}")
                else:
                    print(f"❌ 获取下一篇文案失败: {response2.status_code}")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 提示信息修复测试完成！")
    print("\n请查看以下页面验证修复效果：")
    print("📋 已发布: http://127.0.0.1:5000/simple/publish-status-manage?status=published")
    print("📋 发布失败: http://127.0.0.1:5000/simple/publish-status-manage?status=failed")
    print("\n现在应该能看到：")
    print("✅ 成功状态: 时间戳 + 自定义成功提示信息")
    print("✅ 失败状态: 时间戳 + 自定义失败提示信息")
    print("✅ 不再有多余的前缀文字")

if __name__ == '__main__':
    test_message_fix()
