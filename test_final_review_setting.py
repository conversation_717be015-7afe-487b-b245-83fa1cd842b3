#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试最终审核开关设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def test_final_review_setting():
    """测试最终审核开关设置"""
    print("🔧 测试最终审核开关设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查当前最终审核开关状态
            print("1. 检查当前最终审核开关状态:")
            print("-" * 40)
            
            enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')
            print(f"当前最终审核开关状态: {enable_final_review}")
            print(f"含义: {'启用最终审核' if enable_final_review == '1' else '关闭最终审核'}")
            
            # 2. 测试开关切换
            print(f"\n2. 测试开关切换:")
            print("-" * 40)
            
            # 测试关闭最终审核
            print("测试关闭最终审核开关...")
            SystemSetting.set_value('ENABLE_FINAL_REVIEW', '0', '关闭最终审核功能')
            new_value = SystemSetting.get_value('ENABLE_FINAL_REVIEW')
            print(f"设置后的值: {new_value}")
            print(f"预期效果: 图片上传后直接进入客户审核阶段")
            
            # 测试开启最终审核
            print("\n测试开启最终审核开关...")
            SystemSetting.set_value('ENABLE_FINAL_REVIEW', '1', '启用最终审核功能')
            new_value = SystemSetting.get_value('ENABLE_FINAL_REVIEW')
            print(f"设置后的值: {new_value}")
            print(f"预期效果: 图片上传后进入最终审核阶段")
            
            # 3. 测试图片上传后的状态流转逻辑
            print(f"\n3. 测试图片上传后的状态流转逻辑:")
            print("-" * 40)
            
            # 模拟图片上传后的状态判断
            def get_next_workflow_status_after_image_upload(client_need_review=True):
                enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')
                
                if enable_final_review == '1':
                    return 'final_review'
                else:
                    if client_need_review:
                        return 'pending_client_review'
                    else:
                        return 'pending_publish'
            
            # 测试启用最终审核的情况
            SystemSetting.set_value('ENABLE_FINAL_REVIEW', '1')
            status_enabled = get_next_workflow_status_after_image_upload()
            print(f"启用最终审核时，图片上传后状态: {status_enabled}")
            print(f"  -> 文案会显示在: 最终审核页面 (http://127.0.0.1:5000/simple/final-review)")
            
            # 测试关闭最终审核的情况（需要客户审核）
            SystemSetting.set_value('ENABLE_FINAL_REVIEW', '0')
            status_disabled_with_client = get_next_workflow_status_after_image_upload(client_need_review=True)
            print(f"关闭最终审核时（需要客户审核），图片上传后状态: {status_disabled_with_client}")
            print(f"  -> 文案会显示在: 客户审核页面")
            
            # 测试关闭最终审核的情况（不需要客户审核）
            status_disabled_no_client = get_next_workflow_status_after_image_upload(client_need_review=False)
            print(f"关闭最终审核时（不需要客户审核），图片上传后状态: {status_disabled_no_client}")
            print(f"  -> 文案会显示在: 待发布页面")
            
            # 4. 工作流状态说明
            print(f"\n4. 工作流状态说明:")
            print("-" * 40)
            
            workflow_states = {
                'first_reviewed': '初审通过，等待上传图片',
                'final_review': '待最终审核',
                'pending_client_review': '待客户审核',
                'pending_publish': '待发布',
                'published': '已发布'
            }
            
            for state, description in workflow_states.items():
                print(f"  {state}: {description}")
            
            # 5. 修复说明
            print(f"\n5. 修复说明:")
            print("-" * 40)
            print("修复内容:")
            print("  ✅ 在图片上传提交逻辑中添加了系统设置检查")
            print("  ✅ 根据 ENABLE_FINAL_REVIEW 设置确定图片上传后的状态")
            print("  ✅ 启用最终审核: workflow_status = 'final_review'")
            print("  ✅ 关闭最终审核: workflow_status = 'pending_client_review' 或 'pending_publish'")
            
            print("\n修复位置:")
            print("  📁 文件1: app/views/main_simple.py")
            print("  🔧 函数: submit_content_api (图片上传提交)")
            print("  📝 行数: 3794-3827, 3748-3779, 3800-3828, 3836-3867")
            print("  📁 文件2: app/views/first_review.py")
            print("  🔧 函数: approve (初审通过)")
            print("  📝 行数: 65-98")
            
            # 6. 测试步骤
            print(f"\n6. 测试步骤:")
            print("-" * 40)
            print("请按以下步骤验证修复:")
            print("  1. 🌐 打开系统设置页面")
            print("  2. 🔧 关闭'启用最终审核'开关")
            print("  3. 📝 生成新的文案内容")
            print("  4. ✅ 通过初审（如果启用了初审）")
            print("  5. 🖼️ 上传图片并提交")
            print("  6. 🔍 检查文案是否直接出现在客户审核页面")
            print("  7. ❌ 确认不再出现在最终审核页面")
            
            print("\n预期结果:")
            print("  ❌ 关闭最终审核开关后，图片上传后不会进入最终审核页面")
            print("  ✅ 关闭最终审核开关后，图片上传后直接进入客户审核或待发布")
            print("  🔄 开启最终审核开关后，图片上传后会进入最终审核页面")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 最终审核开关设置测试完成！")
    print("\n关键修复点:")
    print("1. ✅ 图片上传后检查系统设置")
    print("2. ✅ 根据设置确定下一个工作流状态")
    print("3. ✅ 确保开关生效")
    print("4. ✅ 初审通过时也检查最终审核设置")
    print("\n现在最终审核开关应该能正常工作了！")

if __name__ == '__main__':
    test_final_review_setting()
