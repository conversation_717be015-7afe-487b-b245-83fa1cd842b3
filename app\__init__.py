"""
应用初始化文件
"""

from datetime import datetime
from flask import Flask
# 移除 Admin 导入
from flask_login import LoginManager
# 修改导入方式，解决兼容性问题
# from flask_uploads import configure_uploads, IMAGES, UploadSet
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
from flask_wtf.csrf import CSRFProtect
from apscheduler.schedulers.background import BackgroundScheduler
# 使用我们的自定义SimpleMDE实现
from app.utils.simplemde_fix import SimpleMDE
from flask_migrate import Migrate

from app.config import config
from app.models import db, init_app as init_db
from app.utils.logger import setup_logger
from app.utils.errors import register_error_handlers, register_api_error_handlers
from app.utils.uploads import upload_manager
from app.utils.filters import init_app as init_filters

# 全局变量
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
# 移除 Admin 实例
csrf = CSRFProtect()
scheduler = BackgroundScheduler()
simplemde = SimpleMDE()
migrate = Migrate()


def create_app(config_name='default'):
    """创建应用实例"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    # 移除 admin 初始化
    csrf.init_app(app)
    upload_manager.init_app(app)
    simplemde.init_app(app)
    migrate.init_app(app, db)
    
    # 配置日志
    setup_logger(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    register_api_error_handlers(app)
    
    # 注册自定义过滤器
    init_filters(app)

    # 添加favicon路由
    @app.route('/favicon.ico')
    def favicon():
        from flask import send_from_directory
        return send_from_directory(app.static_folder, 'favicon.ico', mimetype='image/vnd.microsoft.icon')

    # 注册蓝图
    register_blueprints(app)
    
    # 注册命令
    register_commands(app)
    
    # 注册全局上下文处理器
    @app.context_processor
    def inject_now():
        return {'now': datetime.now()}

    # 注册模板全局函数（老后台菜单系统已删除）
    @app.context_processor
    def inject_menu_functions():
        # 新后台不需要动态菜单，直接返回空函数
        def get_user_menu_items():
            return []
        return {'get_user_menu_items': get_user_menu_items}

    # 注册模板过滤器
    @app.template_filter('beautify_marks')
    def beautify_marks_filter(text):
        """美化模板标记的过滤器"""
        if not text:
            return text

        import re
        from markupsafe import Markup

        # 将 {标记名} 转换为带样式的HTML
        def replace_mark(match):
            mark_name = match.group(1)
            return f'<span class="template-mark">{{{mark_name}}}</span>'

        # 使用正则表达式替换标记
        beautified = re.sub(r'\{([^}]+)\}', replace_mark, text)

        # 返回安全的HTML标记
        return Markup(beautified)
    
    # 启动定时任务（只在非测试环境下启动）
    if not app.config.get('TESTING', False):
        try:
            scheduler.start()
        except Exception as e:
            print(f"调度器启动失败: {e}")
    
    return app


def register_blueprints(app):
    """注册蓝图"""
    from app.views import register_blueprints as register_view_blueprints
    register_view_blueprints(app)


def register_commands(app):
    """注册自定义命令"""
    from app.commands import register_commands as register_app_commands
    register_app_commands(app)


@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    from app.models.user import User
    user = User.query.get(int(user_id))
    if user:
        # 强制加载角色和权限，确保每次请求都会加载
        print(f"DEBUG - Loading user {user.username} with ID {user_id}")
        # 强制加载角色
        roles = list(user.roles)
        print(f"DEBUG - User roles: {[role.name for role in roles]}")
        # 强制加载每个角色的权限
        for role in roles:
            perms = list(role.permissions)
            print(f"DEBUG - Role {role.name} permissions: {[perm.name for perm in perms]}")
    return user 