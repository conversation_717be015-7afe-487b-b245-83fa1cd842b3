#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一开关逻辑并设置为自动发布
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def unify_switch_logic():
    """统一开关逻辑并设置为自动发布"""
    print("🔧 统一开关逻辑并设置为自动发布...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看当前设置状态
            print("1. 当前设置状态:")
            print("-" * 40)
            
            switches = [
                ('ENABLE_FIRST_REVIEW', '启用最初审核'),
                ('ENABLE_FINAL_REVIEW', '启用初审'),
                ('auto_publish_enabled', '需要手动发布')
            ]
            
            for key, name in switches:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"🔍 {name}: {setting.value}")
                else:
                    print(f"❌ 未找到设置: {key}")
            
            # 2. 设置自动发布（关闭手动发布开关）
            print(f"\n2. 设置自动发布:")
            print("-" * 40)
            
            auto_publish_setting = SystemSetting.query.filter_by(key='auto_publish_enabled').first()
            if auto_publish_setting:
                old_value = auto_publish_setting.value
                auto_publish_setting.value = 'false'  # false = 自动发布
                
                try:
                    db.session.commit()
                    print(f"✅ 已设置为自动发布")
                    print(f"  设置值: {old_value} → {auto_publish_setting.value}")
                    print(f"  逻辑: 关闭手动发布开关 = 自动发布")
                except Exception as e:
                    print(f"❌ 设置失败: {e}")
                    db.session.rollback()
                    return
            else:
                print("❌ 未找到自动发布设置")
                return
            
            # 3. 验证统一后的逻辑
            print(f"\n3. 统一后的逻辑:")
            print("-" * 40)
            
            print("✅ 现在所有开关都遵循'关闭 = 自动化'的逻辑:")
            print()
            print("🔍 启用最初审核:")
            print("  - 开启(1): 需要最初审核")
            print("  - 关闭(0): 自动通过最初审核")
            print()
            print("🔍 启用初审:")
            print("  - 开启(1): 需要初审")
            print("  - 关闭(0): 自动通过初审")
            print()
            print("🔍 需要手动发布:")
            print("  - 开启(true): 需要手动提交发布")
            print("  - 关闭(false): 自动发布 ✅")
            
            # 4. 当前工作流程
            print(f"\n4. 当前工作流程:")
            print("-" * 40)
            
            first_review = SystemSetting.get_value('ENABLE_FIRST_REVIEW', '1')
            final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')
            manual_publish = SystemSetting.get_value('auto_publish_enabled', 'false')
            
            print("📊 根据当前设置的完整工作流程:")
            
            workflow = []
            if first_review == '1':
                workflow.append("最初审核")
            else:
                workflow.append("跳过最初审核")
                
            if final_review == '1':
                workflow.append("初审")
            else:
                workflow.append("跳过初审")
                
            workflow.append("客户审核")
            
            if manual_publish == 'false':
                workflow.append("自动发布")
            else:
                workflow.append("手动提交发布")
            
            print(f"  {' → '.join(workflow)}")
            
            # 5. 用户界面显示
            print(f"\n5. 用户界面显示:")
            print("-" * 40)
            
            print("🎨 系统设置页面现在显示:")
            print("  📋 启用最初审核: 关闭后自动通过最初审核")
            print("  📋 启用初审: 关闭后自动通过初审")
            print("  📋 需要手动发布: 关闭后自动发布 ✅")
            print()
            print("✅ 逻辑统一性:")
            print("  - 所有开关都是'关闭 = 自动化'")
            print("  - 用户不再困惑")
            print("  - 操作逻辑一致")
            
            # 6. 测试建议
            print(f"\n6. 测试建议:")
            print("-" * 40)
            
            print("🧪 建议测试步骤:")
            print("  1. 访问系统设置页面，确认开关显示正确")
            print("  2. 创建测试文案，完成审核流程")
            print("  3. 客户审核通过后，确认自动进入待发布状态")
            print("  4. 切换开关状态，测试不同的工作流程")
            print("  5. 验证所有开关逻辑的一致性")
            
            # 7. 优势总结
            print(f"\n7. 优势总结:")
            print("-" * 40)
            
            print("✅ 统一逻辑的优势:")
            print("  🎯 逻辑一致: 所有开关行为统一")
            print("  👍 易于理解: 关闭 = 自动化")
            print("  🚀 提高效率: 减少用户困惑")
            print("  📈 降低错误: 避免误操作")
            print("  🎨 界面友好: 文案更加清晰")
            
        except Exception as e:
            print(f"❌ 统一过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 开关逻辑统一完成！")
    print("\n📋 统一结果:")
    print("1. ✅ 修改了自动发布的逻辑判断")
    print("2. ✅ 更新了界面显示文本")
    print("3. ✅ 设置为自动发布模式")
    print("4. ✅ 所有开关现在都遵循'关闭 = 自动化'")
    print("\n🚀 现在三个开关的逻辑完全一致！")

if __name__ == '__main__':
    unify_switch_logic()
