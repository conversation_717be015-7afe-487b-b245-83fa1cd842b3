#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库中的图片数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.content import Content
from app.models.image import ContentImage

def check_images():
    """检查数据库中的图片数据"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 检查数据库中的图片数据...")
            print("=" * 60)
            
            # 检查Content表中的image_urls字段
            print("\n1. 检查Content表中的image_urls字段:")
            contents_with_urls = Content.query.filter(
                Content.image_urls.isnot(None),
                Content.image_urls != '',
                Content.image_urls != '[]'
            ).all()
            
            if contents_with_urls:
                print(f"找到 {len(contents_with_urls)} 篇文案有image_urls数据:")
                for content in contents_with_urls:
                    print(f"  ID: {content.id}, 标题: {content.title[:30]}...")
                    print(f"  image_urls: {content.image_urls}")
                    print(f"  workflow_status: {content.workflow_status}")
                    print(f"  image_urls_list: {content.image_urls_list}")
                    print("-" * 40)
            else:
                print("❌ 没有找到有image_urls数据的文案")
            
            # 检查ContentImage表
            print("\n2. 检查ContentImage表:")
            content_images = ContentImage.query.filter_by(is_deleted=False).all()
            
            if content_images:
                print(f"找到 {len(content_images)} 张图片记录:")
                for img in content_images:
                    content = Content.query.get(img.content_id)
                    print(f"  图片ID: {img.id}, 文案ID: {img.content_id}")
                    print(f"  文案标题: {content.title[:30] if content else '未知'}...")
                    print(f"  图片路径: {img.image_path}")
                    print(f"  文案状态: {content.workflow_status if content else '未知'}")
                    print("-" * 40)
            else:
                print("❌ 没有找到ContentImage记录")
            
            # 检查待发布状态的文案
            print("\n3. 检查待发布状态的文案:")
            pending_contents = Content.query.filter(
                Content.workflow_status == 'pending_publish',
                Content.is_deleted == False
            ).all()
            
            if pending_contents:
                print(f"找到 {len(pending_contents)} 篇待发布文案:")
                for content in pending_contents:
                    # 检查两种图片存储方式
                    content_images = ContentImage.get_by_content(content.id)
                    image_urls = content.image_urls_list if hasattr(content, 'image_urls_list') else []
                    
                    total_images = len(content_images) + len(image_urls)
                    
                    print(f"  ID: {content.id}, 标题: {content.title[:30]}...")
                    print(f"  ContentImage数量: {len(content_images)}")
                    print(f"  image_urls数量: {len(image_urls)}")
                    print(f"  总图片数量: {total_images}")
                    
                    if content_images:
                        print(f"  ContentImage路径: {[img.image_path for img in content_images]}")
                    if image_urls:
                        print(f"  image_urls: {image_urls}")
                    print("-" * 40)
            else:
                print("❌ 没有找到待发布状态的文案")
            
        except Exception as e:
            print(f"检查失败: {e}")

if __name__ == '__main__':
    check_images()
