#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试批量优先级弹窗功能
"""

import requests

def test_batch_priority_modal():
    """测试批量优先级弹窗功能"""
    print("🎯 测试批量优先级弹窗功能...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查批量设置按钮
            if 'batch-set-priority-btn' in response.text:
                print("✅ 找到批量设置按钮")
            else:
                print("❌ 未找到批量设置按钮")
            
            # 检查批量优先级弹窗
            if 'batchPriorityModal' in response.text:
                print("✅ 找到批量优先级弹窗")
            else:
                print("❌ 未找到批量优先级弹窗")
            
            # 检查批量弹窗显示函数
            if 'showBatchPriorityModal' in response.text:
                print("✅ 找到批量弹窗显示函数")
            else:
                print("❌ 未找到批量弹窗显示函数")
            
            # 检查批量优先级选项按钮
            if 'batch-priority-option' in response.text:
                print("✅ 找到批量优先级选项按钮")
            else:
                print("❌ 未找到批量优先级选项按钮")
            
            # 检查选中文案数量显示
            if 'batchSelectedCount' in response.text:
                print("✅ 找到选中文案数量显示")
            else:
                print("❌ 未找到选中文案数量显示")
            
            # 检查单个优先级弹窗（应该保留）
            if 'priorityModal' in response.text:
                print("✅ 保留单个优先级弹窗")
            else:
                print("❌ 单个优先级弹窗丢失")
            
            # 检查单个设置图标
            if 'bi-gear' in response.text:
                print("✅ 保留单个设置图标")
            else:
                print("❌ 单个设置图标丢失")
            
            # 检查弹窗标题
            if '批量设置优先级' in response.text:
                print("✅ 找到批量弹窗标题")
            else:
                print("❌ 未找到批量弹窗标题")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 批量优先级弹窗功能测试完成！")
    print("\n新的批量设置方式：")
    print("1. ✅ 批量按钮 - 右上角'批量设置'按钮")
    print("2. ✅ 弹窗选择 - 点击按钮弹出优先级选择弹窗")
    print("3. ✅ 数量显示 - 弹窗中显示选中的文案数量")
    print("4. ✅ 直观选择 - 三个大按钮分别对应高/普通/低优先级")
    print("5. ✅ 确认对话框 - 选择后弹出确认对话框")
    print("\n使用方法：")
    print("【批量设置】")
    print("1. 勾选要设置的文案（复选框）")
    print("2. 点击右上角'批量设置'按钮")
    print("3. 在弹窗中查看选中文案数量")
    print("4. 点击目标优先级按钮")
    print("5. 确认对话框中点击确定")
    print("6. 系统批量更新并刷新页面")
    print("\n【单个设置】")
    print("1. 点击任意文案行的齿轮图标")
    print("2. 在弹窗中点击目标优先级")
    print("3. 系统自动保存并刷新页面")
    print("\n弹窗特点：")
    print("- 统一的弹窗风格")
    print("- 显示选中文案数量")
    print("- 三个大按钮易于点击")
    print("- 每个按钮有图标和说明文字")
    print("- 双重确认防止误操作")

if __name__ == '__main__':
    test_batch_priority_modal()
