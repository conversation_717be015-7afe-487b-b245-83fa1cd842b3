#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证默认发布间隔设置删除结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def verify_publish_interval_cleanup():
    """验证默认发布间隔设置删除结果"""
    print("✅ 验证默认发布间隔设置删除结果...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 确认设置已删除
            print("1. 确认设置已删除:")
            print("-" * 40)
            
            deleted_keys = [
                'default_publish_interval_min',
                'default_publish_interval_max'
            ]
            
            for key in deleted_keys:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"❌ {key} 仍然存在: {setting.value}")
                else:
                    print(f"✅ {key} 已成功删除")
            
            # 2. 查看当前系统设置
            print(f"\n2. 当前系统设置概览:")
            print("-" * 40)
            
            all_settings = SystemSetting.query.all()
            
            # 按类别分组
            categories = {
                '发布相关': [],
                '分享功能': [],
                '图片上传': [],
                '系统功能': [],
                '其他': []
            }
            
            for setting in all_settings:
                key_lower = setting.key.lower()
                if 'publish' in key_lower:
                    categories['发布相关'].append(setting)
                elif 'share' in key_lower:
                    categories['分享功能'].append(setting)
                elif 'image' in key_lower or 'upload' in key_lower:
                    categories['图片上传'].append(setting)
                elif any(word in key_lower for word in ['enable', 'notification', 'backup', 'api']):
                    categories['系统功能'].append(setting)
                else:
                    categories['其他'].append(setting)
            
            for category, settings in categories.items():
                if settings:
                    print(f"\n📋 {category} ({len(settings)}个):")
                    for setting in settings:
                        print(f"  ✅ {setting.key}: {setting.value}")
            
            # 3. 统计信息
            print(f"\n3. 设置统计:")
            print("-" * 40)
            
            total_count = len(all_settings)
            print(f"系统设置总数: {total_count}")
            
            # 4. 客户设置验证
            print(f"\n4. 客户设置验证:")
            print("-" * 40)
            
            from app.models.client import Client
            clients = Client.query.all()
            
            print("客户发布间隔设置（不受影响）:")
            for client in clients:
                print(f"  📋 {client.name}:")
                print(f"     最小间隔: {client.interval_min} 分钟")
                print(f"     最大间隔: {client.interval_max} 分钟")
            
            # 5. 表单默认值验证
            print(f"\n5. 表单默认值验证:")
            print("-" * 40)
            
            from app.forms.client import ClientForm
            
            print("客户表单中的默认值:")
            form = ClientForm()
            print(f"  最小间隔默认值: {form.interval_min.default} 分钟")
            print(f"  最大间隔默认值: {form.interval_max.default} 分钟")
            print("这些默认值现在直接在表单中定义，不依赖系统设置")
            
            # 6. 优化效果总结
            print(f"\n6. 优化效果总结:")
            print("-" * 40)
            
            print("✅ 删除的冗余设置:")
            print("  🗑️ default_publish_interval_min - 默认发布间隔最小值")
            print("  🗑️ default_publish_interval_max - 默认发布间隔最大值")
            
            print("\n✅ 保留的重要设置:")
            print("  ✅ PUBLISH_TIMEOUT - 发布超时时间")
            print("  ✅ client_share_enabled - 分享功能开关")
            print("  ✅ API_KEY - API访问密钥")
            print("  ✅ 图片上传相关设置")
            
            print("\n✅ 优化效果:")
            print("  🎯 系统设置页面更简洁")
            print("  🎯 客户设置更直观（在客户页面直接设置）")
            print("  🎯 减少了设置冗余和混淆")
            print("  🎯 提高了用户体验")
            
            # 7. 使用指南
            print(f"\n7. 使用指南:")
            print("-" * 40)
            
            print("现在用户应该:")
            print("  1. 🔧 在客户添加/编辑页面设置发布间隔")
            print("  2. 📋 根据客户需求选择合适的间隔时间")
            print("  3. 🎯 不再依赖系统默认值")
            print("  4. 💡 每个客户可以有不同的发布间隔设置")
            
            print("\n推荐的间隔设置:")
            print("  - 最小间隔: 10-30分钟（避免过于频繁）")
            print("  - 最大间隔: 30-90分钟（保证一天内发布完成）")
            print("  - 根据客户类型和需求调整")
            
        except Exception as e:
            print(f"❌ 验证过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 默认发布间隔设置删除验证完成！")
    print("\n✅ 验证结果:")
    print("1. 冗余的默认发布间隔设置已成功删除")
    print("2. 客户设置功能正常，不受影响")
    print("3. 系统设置页面更加简洁")
    print("4. 用户体验得到优化")
    print("\n🎯 现在用户在客户页面直接设置发布间隔，更加直观！")

if __name__ == '__main__':
    verify_publish_interval_cleanup()
