#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清理系统中冗余的审核超时设置
保留发布超时设置，删除审核超时设置（已被客户级别设置替代）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def cleanup_system_review_timeout_settings():
    """清理系统中冗余的审核超时设置"""
    print("🧹 清理系统中冗余的审核超时设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 识别需要删除的设置
            print("1. 识别需要删除的审核超时设置:")
            print("-" * 40)
            
            # 需要删除的设置键名
            settings_to_delete = [
                'review_timeout_hours',  # 审核超时时间（小时）
                'REVIEW_TIMEOUT_HOURS',  # 审核超时时间（大写版本）
                'CLIENT_REVIEW_TIMEOUT_HOURS',  # 客户审核超时时间
                'AUTO_REVIEW_DELAY',  # 自动审核延迟
                'auto_review_delay',  # 自动审核延迟（小写）
                'auto_confirm_minutes'  # 自动确认时间
            ]
            
            settings_found = []
            
            for key in settings_to_delete:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    settings_found.append(setting)
                    print(f"  🔍 找到: {setting.key} = {setting.value}")
                    print(f"      描述: {setting.description}")
                    print(f"      ID: {setting.id}")
                    print()
            
            if not settings_found:
                print("  ✅ 没有找到需要删除的审核超时设置")
                return
            
            # 2. 确认删除原因
            print("2. 删除原因说明:")
            print("-" * 40)
            print("这些设置将被删除的原因:")
            print("  ✅ 客户级别设置更灵活：每个客户可以有不同的超时规则")
            print("  ✅ 避免设置冲突：防止全局设置和客户设置之间的混淆")
            print("  ✅ 简化界面：减少系统设置页面的冗余选项")
            print("  ✅ 提高用户体验：设置更加直观和易于理解")
            print("  ✅ 功能已替代：客户级别设置完全覆盖了这些功能")
            
            # 3. 保留的设置说明
            print(f"\n3. 保留的超时设置:")
            print("-" * 40)
            
            preserved_settings = [
                'PUBLISH_TIMEOUT',
                'PUBLISH_TIMEOUT_ACTION'
            ]
            
            for key in preserved_settings:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"  ✅ 保留: {setting.key} = {setting.value}")
                    print(f"      原因: 发布超时是系统级功能，需要全局统一设置")
            
            # 4. 执行删除操作
            print(f"\n4. 执行删除操作:")
            print("-" * 40)
            
            deleted_count = 0
            for setting in settings_found:
                try:
                    print(f"  🗑️ 删除: {setting.key} (ID: {setting.id})")
                    db.session.delete(setting)
                    deleted_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败: {setting.key} - {e}")
            
            # 提交更改
            if deleted_count > 0:
                db.session.commit()
                print(f"\n✅ 成功删除 {deleted_count} 个冗余的审核超时设置")
            else:
                print(f"\n⚪ 没有设置被删除")
            
            # 5. 验证删除结果
            print(f"\n5. 验证删除结果:")
            print("-" * 40)
            
            remaining_settings = SystemSetting.query.all()
            review_related = []
            
            for setting in remaining_settings:
                if any(keyword in setting.key.lower() for keyword in ['timeout', 'review']):
                    review_related.append(setting)
            
            print("剩余的超时/审核相关设置:")
            for setting in review_related:
                if 'publish' in setting.key.lower():
                    print(f"  ✅ {setting.key}: {setting.value} (发布相关，保留)")
                else:
                    print(f"  🔍 {setting.key}: {setting.value} (需要检查)")
            
            # 6. 功能影响说明
            print(f"\n6. 功能影响说明:")
            print("-" * 40)
            print("删除这些设置后的影响:")
            print("  ✅ 客户审核超时功能正常：通过客户级别设置控制")
            print("  ✅ 发布超时功能正常：保留了系统级别设置")
            print("  ✅ 系统设置页面更简洁：减少了冗余选项")
            print("  ✅ 用户体验更好：设置逻辑更清晰")
            print("  ✅ 维护更容易：减少了设置项之间的冲突")
            
            # 7. 后续建议
            print(f"\n7. 后续建议:")
            print("-" * 40)
            print("建议的后续操作:")
            print("  1. 🔧 检查系统设置页面，确认界面显示正常")
            print("  2. 🧪 测试客户审核超时功能，确认工作正常")
            print("  3. 📝 更新相关文档，说明设置变更")
            print("  4. 👥 通知用户设置位置的变化")
            
        except Exception as e:
            print(f"❌ 清理过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 系统审核超时设置清理完成！")
    print("\n总结:")
    print("1. 🗑️ 删除了冗余的审核超时设置")
    print("2. ✅ 保留了必要的发布超时设置")
    print("3. 🎯 客户级别设置完全替代了全局审核超时设置")
    print("4. 🧹 系统设置页面更加简洁清晰")
    print("\n现在系统设置页面不再显示审核超时相关的冗余选项！")

if __name__ == '__main__':
    cleanup_system_review_timeout_settings()
