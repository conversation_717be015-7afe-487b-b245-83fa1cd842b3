#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清理重复的系统设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting
from app import db

def clean_duplicate_settings():
    """清理重复的系统设置"""
    print("🧹 清理重复的系统设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 要删除的重复设置键名
            duplicate_keys = [
                'allowed_image_types',  # 重复的图片格式设置
                'max_upload_size'       # 重复的图片大小设置
            ]
            
            print("准备删除的重复设置:")
            for key in duplicate_keys:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"  - {key}: {setting.value} ({setting.description})")
                else:
                    print(f"  - {key}: 未找到")
            
            # 确认删除
            print(f"\n将删除 {len(duplicate_keys)} 个重复设置")
            
            deleted_count = 0
            for key in duplicate_keys:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"删除设置: {key}")
                    db.session.delete(setting)
                    deleted_count += 1
                else:
                    print(f"设置不存在: {key}")
            
            # 提交更改
            if deleted_count > 0:
                db.session.commit()
                print(f"✅ 成功删除 {deleted_count} 个重复设置")
            else:
                print("❌ 没有找到需要删除的设置")
            
            # 验证清理结果
            print(f"\n验证清理结果:")
            print("-" * 40)
            
            remaining_settings = SystemSetting.get_all_settings()
            print(f"剩余设置数量: {len(remaining_settings)}")
            
            # 检查图片相关设置
            image_settings = []
            for setting in remaining_settings:
                if 'image' in setting.key.lower() or 'upload' in setting.key.lower():
                    image_settings.append(setting)
            
            print(f"\n图片相关设置 (清理后):")
            for setting in image_settings:
                print(f"  {setting.key}: {setting.value}")
            
            # 检查是否还有重复
            keys = [s.key for s in remaining_settings]
            duplicates = set([x for x in keys if keys.count(x) > 1])
            
            if duplicates:
                print(f"❌ 仍有重复设置: {duplicates}")
            else:
                print("✅ 没有重复设置")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 重复设置清理完成！")
    print("\n清理结果:")
    print("1. ✅ 删除了 allowed_image_types (保留 IMAGE_UPLOAD_ALLOWED_TYPES)")
    print("2. ✅ 删除了 max_upload_size (保留 IMAGE_UPLOAD_MAX_SIZE)")
    print("3. ✅ 系统设置页面将不再显示重复项")
    print("4. ✅ 保留了功能更完整的标准命名设置")

if __name__ == '__main__':
    clean_duplicate_settings()
