#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析默认发布间隔设置的用途和功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting
from app.models.client import Client

def analyze_publish_interval_settings():
    """分析默认发布间隔设置"""
    print("🔍 分析默认发布间隔设置的用途和功能...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看当前设置值
            print("1. 当前设置值:")
            print("-" * 40)
            
            interval_min_setting = SystemSetting.query.filter_by(key='default_publish_interval_min').first()
            interval_max_setting = SystemSetting.query.filter_by(key='default_publish_interval_max').first()
            
            if interval_min_setting:
                print(f"✅ default_publish_interval_min: {interval_min_setting.value} 分钟")
                print(f"   描述: {interval_min_setting.description}")
            else:
                print("❌ default_publish_interval_min: 未设置")
            
            if interval_max_setting:
                print(f"✅ default_publish_interval_max: {interval_max_setting.value} 分钟")
                print(f"   描述: {interval_max_setting.description}")
            else:
                print("❌ default_publish_interval_max: 未设置")
            
            # 2. 功能说明
            print(f"\n2. 功能说明:")
            print("-" * 40)
            
            print("📋 默认发布间隔设置的作用:")
            print("  🎯 用途: 为新客户提供默认的发布时间间隔设置")
            print("  📅 场景: 创建新客户时的默认值")
            print("  ⏰ 含义: 文案发布的时间间隔范围")
            print()
            print("📋 具体功能:")
            print("  - default_publish_interval_min: 发布间隔最小值（分钟）")
            print("  - default_publish_interval_max: 发布间隔最大值（分钟）")
            print("  - 系统会在这个范围内随机选择间隔时间")
            print("  - 避免发布时间过于规律，更自然")
            
            # 3. 使用场景分析
            print(f"\n3. 使用场景分析:")
            print("-" * 40)
            
            print("🔧 在以下情况下使用:")
            print("  1. 创建新客户时:")
            print("     - 客户表单中的间隔时间字段会使用这些默认值")
            print("     - 用户可以在客户设置中修改")
            print()
            print("  2. 文案展示时间计算:")
            print("     - 系统根据客户的间隔设置计算每篇文案的展示时间")
            print("     - 第一篇文案在开始时间展示")
            print("     - 后续文案按间隔时间依次展示")
            print()
            print("  3. 自动发布功能:")
            print("     - 如果启用自动发布，会按照这个间隔发布文案")
            print("     - 保证发布时间的合理分布")
            
            # 4. 查看客户实际使用情况
            print(f"\n4. 客户实际使用情况:")
            print("-" * 40)
            
            clients = Client.query.all()
            if clients:
                print("当前客户的间隔设置:")
                for client in clients:
                    print(f"  📋 {client.name}:")
                    print(f"     最小间隔: {client.interval_min} 分钟")
                    print(f"     最大间隔: {client.interval_max} 分钟")
                    print(f"     每日文案数: {client.daily_content_count}")
                    print(f"     开始时间: {client.display_start_time}")
                    print()
            else:
                print("❌ 没有找到客户数据")
            
            # 5. 计算示例
            print(f"5. 发布时间计算示例:")
            print("-" * 40)
            
            if interval_min_setting and interval_max_setting:
                min_val = int(interval_min_setting.value)
                max_val = int(interval_max_setting.value)
                
                print(f"假设设置: 最小间隔 {min_val} 分钟，最大间隔 {max_val} 分钟")
                print(f"开始时间: 09:00")
                print(f"每日文案数: 5篇")
                print()
                print("可能的发布时间安排:")
                
                import random
                from datetime import datetime, time, timedelta
                
                start_time = time(9, 0)
                current_time = datetime.combine(datetime.today(), start_time)
                
                for i in range(5):
                    if i == 0:
                        print(f"  第{i+1}篇: {current_time.strftime('%H:%M')} (开始时间)")
                    else:
                        interval = random.randint(min_val, max_val)
                        current_time += timedelta(minutes=interval)
                        print(f"  第{i+1}篇: {current_time.strftime('%H:%M')} (间隔 {interval} 分钟)")
            
            # 6. 设置建议
            print(f"\n6. 设置建议:")
            print("-" * 40)
            
            print("💡 合理的间隔设置:")
            print("  ✅ 最小间隔: 30-60分钟")
            print("     - 太短: 发布过于频繁，可能被平台限制")
            print("     - 太长: 发布间隔过大，影响曝光")
            print()
            print("  ✅ 最大间隔: 90-180分钟")
            print("     - 保证一天内能发布完所有文案")
            print("     - 避免发布时间过晚")
            print()
            print("  ✅ 当前设置评估:")
            if interval_min_setting and interval_max_setting:
                min_val = int(interval_min_setting.value)
                max_val = int(interval_max_setting.value)
                
                if min_val >= 30 and max_val <= 180:
                    print(f"     🎯 当前设置合理: {min_val}-{max_val} 分钟")
                elif min_val < 30:
                    print(f"     ⚠️ 最小间隔偏短: {min_val} 分钟，建议调整为30分钟以上")
                elif max_val > 180:
                    print(f"     ⚠️ 最大间隔偏长: {max_val} 分钟，建议调整为180分钟以下")
                else:
                    print(f"     ✅ 设置合理: {min_val}-{max_val} 分钟")
            
            # 7. 是否需要保留
            print(f"\n7. 是否需要保留这些设置:")
            print("-" * 40)
            
            print("🤔 保留的理由:")
            print("  ✅ 为新客户提供合理的默认值")
            print("  ✅ 避免每次创建客户都要手动设置")
            print("  ✅ 确保系统有统一的默认标准")
            print("  ✅ 简化客户创建流程")
            print()
            print("❌ 删除的理由:")
            print("  ❌ 每个客户的需求不同，默认值意义不大")
            print("  ❌ 用户通常会根据实际需求调整")
            print("  ❌ 增加了系统设置的复杂度")
            print()
            print("🎯 建议: 保留这些设置")
            print("   原因: 这些是真正有用的默认值，能简化客户创建流程")
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 默认发布间隔设置分析完成！")
    print("\n📋 总结:")
    print("1. 🎯 这两个设置用于新客户的默认发布间隔")
    print("2. ⏰ 控制文案发布的时间间隔范围")
    print("3. 🔧 在客户创建和文案展示时间计算中使用")
    print("4. ✅ 建议保留，这是有实际意义的系统设置")
    print("\n💡 这些设置帮助系统为新客户提供合理的默认发布间隔！")

if __name__ == '__main__':
    analyze_publish_interval_settings()
