#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通过API测试添加字段
"""

import requests
import json

def test_add_field():
    """测试添加字段"""
    print("🔧 测试数据库字段...")
    print("=" * 50)
    
    # 先测试一个简单的API调用，看看是否有数据库错误
    try:
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        if response.status_code == 200:
            print("✅ 页面访问成功，数据库连接正常")
            print("现在需要手动添加publish_error字段到数据库")
            print("\n请在MySQL中执行以下SQL语句：")
            print("ALTER TABLE contents ADD COLUMN publish_error TEXT COMMENT '发布提示信息';")
            print("\n执行完成后，重新测试API功能")
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_add_field()
