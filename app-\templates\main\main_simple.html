{% extends "base_simple.html" %}

{% block title %}小红书文案生成系统{% endblock %}

{% block content %}
{% if page_content %}
<!-- 动态页面内容 -->
<div class="page-content active" id="page-{{ current_page or 'dashboard' }}">
    {{ page_content|safe }}
</div>
{% else %}
<!-- 控制台页面 -->
<div class="page-content active" id="page-dashboard">
    <div class="container-fluid">
        <h2>控制台</h2>
        <div class="row g-3">
            {% if current_user.has_permission('template_manage') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-file-text fs-5 text-primary me-2"></i>模板管理
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-primary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-file-earmark-text text-primary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">模板总数</div>
                                    <div class="fs-4 fw-bold">{{ template_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-tags text-primary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">分类总数</div>
                                    <div class="fs-4 fw-bold">{{ category_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('client_manage') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-people fs-5 text-success me-2"></i>客户管理
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-success/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-person-badge text-success"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">客户总数</div>
                                    <div class="fs-4 fw-bold">{{ client_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-success/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-person-check text-success"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">活跃客户</div>
                                    <div class="fs-4 fw-bold">{{ active_client_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% if current_user.has_permission('content.generate') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-pencil fs-5 text-info me-2"></i>内容生成
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-info/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-file-text text-info"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">生成总数</div>
                                    <div class="fs-4 fw-bold">{{ generated_content_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-info/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-clock text-info"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">待审核</div>
                                    <div class="fs-4 fw-bold">{{ pending_review_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
              {% endif %}
              {% if current_user.has_permission('content.review') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-search fs-5 text-warning me-2"></i>初审文案
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-warning/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-clipboard-check text-warning"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">待初审</div>
                                    <div class="fs-4 fw-bold">{{ pending_first_review_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-warning/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-check2-circle text-warning"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">已初审</div>
                                    <div class="fs-4 fw-bold">{{ completed_first_review_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}



            {% if current_user.has_permission('publish.manage') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-send fs-5 text-primary me-2"></i>发布管理
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-primary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-paper-plane text-primary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">发布总数</div>
                                    <div class="fs-4 fw-bold">{{ publish_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-check2-circle text-primary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">成功发布</div>
                                    <div class="fs-4 fw-bold">{{ success_publish_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('image_upload') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        {% if current_user.has_permission('image.upload') %}
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-upload fs-5 text-success me-2"></i>图片上传
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-success/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-image text-success"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">上传总数</div>
                                    <div class="fs-4 fw-bold">{{ image_upload_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-success/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-exclamation-triangle text-success"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">待处理</div>
                                    <div class="fs-4 fw-bold">{{ pending_image_count|default(0) }}</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('final_review') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-check-square fs-5 text-danger me-2"></i>最终审核
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-danger/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-clipboard-check text-danger"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">待终审</div>
                                    <div class="fs-4 fw-bold">{{ pending_final_review_count|default(0) }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-danger/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-x-circle text-danger"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">已驳回</div>
                                    <div class="fs-4 fw-bold">{{ rejected_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('client_review') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-share fs-5 text-purple me-2"></i>客户审核
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-purple/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-link-45deg text-purple"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">分享链接</div>
                                    <div class="fs-4 fw-bold">{{ shared_link_count }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-purple/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-clock-history text-purple"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">客户未确认</div>
                                    <div class="fs-4 fw-bold">{{ client_pending_count|default(0) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('publish_status_manage') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-bar-chart-line fs-5 text-info me-2"></i>发布状态
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-info/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-hourglass-split text-info"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">发布中</div>
                                    <div class="fs-4 fw-bold">{{ publishing_count }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-info/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-exclamation-circle text-info"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">发布失败</div>
                                    <div class="fs-4 fw-bold">{{ failed_publish_count }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            {% if current_user.has_permission('user_manage') %}
            <div class="col-12 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            <i class="bi bi-people fs-5 text-secondary me-2"></i>用户管理
                        </h5>
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="d-flex align-items-center me-4">
                                <div class="bg-secondary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-person-plus text-secondary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">用户总数</div>
                                    <div class="fs-4 fw-bold">{{ user_count }}</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-secondary/10 p-3 rounded-circle me-3">
                                    <i class="bi bi-shield-lock text-secondary"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">管理员</div>
                                    <div class="fs-4 fw-bold">{{ admin_count }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}






        </div>
    </div>
</div>

<!-- 模板管理页面 -->
<div class="page-content" id="page-templates">
    <!-- 这里将通过AJAX加载模板管理内容 -->
</div>

<!-- 客户管理页面 -->
<div class="page-content" id="page-clients">
    <!-- 这里将通过AJAX加载客户管理内容 -->
</div>

<!-- 内容生成页面 -->
<div class="page-content" id="page-content">
    <!-- 这里将通过AJAX加载内容生成页面 -->
</div>

<!-- 初审文案页面 -->
<div class="page-content" id="page-review-content">
    <!-- 这里将通过AJAX加载初审文案页面 -->
</div>

<!-- 话题管理页面 -->
<div class="page-content" id="page-topics">
    <!-- 这里将通过AJAX加载话题管理页面 -->
</div>

<!-- 任务管理页面 -->
<div class="page-content" id="page-tasks">
    <!-- 这里将通过AJAX加载任务管理页面 -->
</div>

<!-- 发布管理页面 -->
<div class="page-content" id="page-publish">
    <!-- 这里将通过AJAX加载发布管理页面 -->
</div>

<!-- 文案补充页面 -->
<div class="page-content" id="page-supplement">
    <!-- 这里将通过AJAX加载文案补充页面 -->
</div>

<!-- 文案展示页面 -->
<div class="page-content" id="page-display">
    <!-- 这里将通过AJAX加载文案展示页面 -->
</div>

<!-- 通知中心页面 -->
<div class="page-content" id="page-notifications">
    <!-- 这里将通过AJAX加载通知中心页面 -->
</div>

<!-- 数据统计页面 -->
{% if current_user.has_permission('view_stats') %}
<div class="page-content" id="page-stats">
    <!-- 这里将通过AJAX加载数据统计页面 -->
</div>
{% endif %}

<!-- 导入导出页面 -->
<div class="page-content" id="page-export">
    <!-- 这里将通过AJAX加载导入导出页面 -->
</div>

<!-- 用户管理页面 -->
<div class="page-content" id="page-users">
    <!-- 这里将通过AJAX加载用户管理页面 -->
</div>

<!-- 系统设置页面 -->
<div class="page-content" id="page-system">
    <!-- 这里将通过AJAX加载系统设置页面 -->
</div>
{% endif %}
{% endif %}
{% endblock %}

  {% block scripts %}
<script>
// 页面特定的JavaScript可以在这里添加
console.log('主页面已加载');

// 图片上传相关的全局变量和函数
let currentContentId = null;

// 打开图片上传模态框
function openImageUploadModal(contentId, title) {
    currentContentId = contentId;

    // 设置模态框标题
    document.getElementById('imageUploadModalLabel').textContent = `图片管理 - ${title}`;

    // 加载文案信息
    loadContentPreview(contentId);

    // 加载已上传的图片
    loadUploadedImages(contentId);

    // 显示模态框
    const modalElement = document.getElementById('imageUploadModal');
    const modal = new bootstrap.Modal(modalElement);

    // 移除之前的事件监听器（避免重复绑定）
    modalElement.removeEventListener('hidden.bs.modal', handleModalClose);

    // 添加弹窗关闭事件监听器
    modalElement.addEventListener('hidden.bs.modal', handleModalClose);

    modal.show();
}

// 处理模态框关闭事件
function handleModalClose() {
    console.log('图片管理弹窗关闭，刷新图片数量显示');

    if (currentContentId) {
        // 刷新对应内容的图片数量显示
        refreshImageCountForContent(currentContentId);

        // 清空当前内容ID
        currentContentId = null;
    }
}

// 刷新指定内容的图片数量显示
function refreshImageCountForContent(contentId) {
    console.log('刷新内容ID为', contentId, '的图片数量');

    // 获取最新的图片数量
    fetch(`/simple/api/images/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const newCount = data.images.length;
                console.log('获取到最新图片数量:', newCount);

                // 更新页面显示
                updateImageCountInTable(contentId, newCount);

                // 更新总数量
                updateTotalImageCountInPage();
            } else {
                console.error('获取图片数量失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取图片数量请求失败:', error);
        });
}

// 更新表格中的图片数量显示
function updateImageCountInTable(contentId, newCount) {
    // 直接查找图片数量列的badge（有data-content-id属性的badge）
    const badge = document.querySelector(`span.badge[data-content-id="${contentId}"]`);
    if (badge) {
        console.log('更新表格中的图片数量:', `${newCount}张图片`);
        badge.textContent = `${newCount}张图片`;
        badge.className = `badge bg-${newCount > 0 ? 'success' : 'secondary'}`;
        console.log('图片数量更新成功');

        // 同时更新提交按钮状态
        updateSubmitButtonStateInTable(contentId, newCount);
    } else {
        console.log('没有找到对应的图片数量badge，contentId:', contentId);

        // 调试：列出所有带data-content-id的badge
        const allImageBadges = document.querySelectorAll('span.badge[data-content-id]');
        console.log('页面上所有图片数量badge:', allImageBadges);
        allImageBadges.forEach((b, index) => {
            console.log(`Badge ${index}: data-content-id=${b.getAttribute('data-content-id')}, text=${b.textContent}`);
        });
    }
}

// 更新提交按钮状态（在main_simple.html中）
function updateSubmitButtonStateInTable(contentId, imageCount) {
    console.log('更新提交按钮状态 (main_simple):', contentId, imageCount);

    // 找到对应的表格行
    const contentRow = document.querySelector(`tr[data-content-id="${contentId}"]`);
    if (contentRow) {
        // 查找提交按钮
        const submitButton = contentRow.querySelector('button[onclick*="submitContent"]');
        if (submitButton) {
            if (imageCount > 0) {
                // 有图片，激活按钮
                submitButton.disabled = false;
                submitButton.removeAttribute('title');
                submitButton.classList.remove('btn-secondary');
                submitButton.classList.add('btn-success');
                console.log('✅ 提交按钮已激活 (main_simple)');
            } else {
                // 没有图片，禁用按钮
                submitButton.disabled = true;
                submitButton.setAttribute('title', '请先上传图片');
                submitButton.classList.remove('btn-success');
                submitButton.classList.add('btn-secondary');
                console.log('❌ 提交按钮已禁用 (main_simple)');
            }
        } else {
            console.log('❌ 未找到提交按钮');
        }
    } else {
        console.log('❌ 未找到表格行');
    }
}

// 更新页面总图片数量
function updateTotalImageCountInPage() {
    let totalCount = 0;
    // 只选择图片数量列的badge（有data-content-id属性的）
    const imageBadges = document.querySelectorAll('tbody span.badge[data-content-id]');

    imageBadges.forEach(badge => {
        const count = parseInt(badge.textContent.split('/')[0]) || 0;
        totalCount += count;
    });

    // 更新总数显示
    const totalCountElement = document.querySelector('.text-warning');
    if (totalCountElement) {
        console.log('更新总图片数量:', totalCount);
        totalCountElement.textContent = totalCount;
    }
}

// 加载文案预览信息
function loadContentPreview(contentId) {
    fetch(`/simple/api/contents/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = data.content;

                // 生成话题标签
                let topicsHtml = '';
                if (content.topics && content.topics.length > 0) {
                    topicsHtml = content.topics.map(topic =>
                        `<span class="badge bg-primary me-1 mb-1">#${topic}</span>`
                    ).join('');
                } else {
                    topicsHtml = '<span class="text-muted">无</span>';
                }

                // 生成@用户标签
                let atUsersHtml = '';
                if (content.at_users && content.at_users.length > 0) {
                    atUsersHtml = content.at_users.map(user =>
                        `<span class="badge bg-warning text-dark me-1 mb-1">@${user.replace('@', '')}</span>`
                    ).join('');
                } else {
                    atUsersHtml = '<span class="text-muted">无</span>';
                }

                // 生成定位标签
                let locationHtml = '';
                if (content.location) {
                    locationHtml = `<span class="badge bg-success me-1 mb-1"><i class="bi bi-geo-alt"></i> ${content.location}</span>`;
                } else {
                    locationHtml = '<span class="text-muted">无</span>';
                }

                const previewHtml = `
                    <div class="mb-3">
                        <label class="form-label fw-bold">标题：</label>
                        <p class="mb-2">${content.title}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">内容：</label>
                        <p class="mb-2" style="max-height: 150px; overflow-y: auto; white-space: pre-wrap;">${content.content}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">客户：</label>
                        <p class="mb-2"><span class="badge bg-info">${content.client_name || '未知客户'}</span></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">话题：</label>
                        <div class="mb-2">${topicsHtml}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">@用户：</label>
                        <div class="mb-2">${atUsersHtml}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">定位：</label>
                        <div class="mb-2">${locationHtml}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">创建时间：</label>
                        <p class="mb-2">${content.created_at}</p>
                    </div>
                `;
                document.getElementById('contentPreview').innerHTML = previewHtml;
            }
        })
        .catch(error => {
            console.error('加载文案信息失败:', error);
            document.getElementById('contentPreview').innerHTML = '<p class="text-danger">加载失败</p>';
        });
}

// 加载已上传的图片
function loadUploadedImages(contentId) {
    fetch(`/simple/api/images/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const images = data.images;
                updateImageCount(images.length);
                displayImages(images);
            }
        })
        .catch(error => {
            console.error('加载图片失败:', error);
        });
}

// 更新图片数量显示
function updateImageCount(count) {
    const badge = document.getElementById('imageCount');
    if (badge) {
        badge.textContent = `${count}/9`;
        badge.className = count > 0 ? 'badge bg-success' : 'badge bg-secondary';
    }
}

// 显示图片
function displayImages(images) {
    const imageGrid = document.getElementById('imageGrid');
    if (!imageGrid) return;

    if (images.length === 0) {
        imageGrid.innerHTML = '<div class="col-12"><p class="text-muted text-center">暂无图片</p></div>';
        return;
    }

    // 按image_order排序
    images.sort((a, b) => (a.image_order || 0) - (b.image_order || 0));

    let html = '';
    images.forEach((image, index) => {
        html += `
            <div class="col-md-4 col-sm-6 image-item"
                 data-image-id="${image.id}"
                 data-image-order="${image.image_order || index + 1}"
                 draggable="true"
                 ondragstart="handleImageDragStart(event)"
                 ondragover="handleImageDragOver(event)"
                 ondrop="handleImageDrop(event)"
                 ondragend="handleImageDragEnd(event)"
                 style="cursor: move;">
                <div class="position-relative border rounded overflow-hidden" style="background: #f8f9fa;">
                    <!-- 序号标签 -->
                    <div class="position-absolute top-0 start-0 bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 24px; height: 24px; font-size: 12px; z-index: 10; margin: 4px;">
                        ${index + 1}
                    </div>

                    <!-- 删除按钮 -->
                    <button type="button"
                            class="btn btn-danger btn-sm position-absolute top-0 end-0"
                            onclick="deleteImageFromModal(${image.id})"
                            title="删除图片"
                            style="z-index: 10; margin: 4px; padding: 2px 6px;">
                        <i class="bi bi-x"></i>
                    </button>

                    <!-- 图片 -->
                    <div class="position-relative" style="height: 120px;">
                        <img src="/static/uploads/${image.thumbnail_path}"
                             class="w-100 h-100"
                             style="object-fit: cover;"
                             title="${image.original_name}">

                        <!-- 拖拽提示图标 -->
                        <div class="position-absolute top-50 start-50 translate-middle text-white"
                             style="pointer-events: none; opacity: 0.6; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                            <i class="bi bi-arrows-move fs-5"></i>
                        </div>
                    </div>

                    <!-- 文件名标签 - 固定在底部 -->
                    <div class="bg-dark bg-opacity-85 text-white px-2 py-1" style="font-size: 11px; line-height: 1.2;">
                        <div class="text-truncate" title="${image.original_name}">
                            ${image.original_name.length > 20 ? image.original_name.substring(0, 17) + '...' : image.original_name}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    imageGrid.innerHTML = html;
}



// 从模态框中删除图片
function deleteImageFromModal(imageId) {
    if (!confirm('确定要删除这张图片吗？')) {
        return;
    }

    fetch(`/simple/api/images/${imageId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 只显示右上角提示，不弹窗
            showSimpleToast('图片删除成功', 'success');
            // 重新加载图片列表
            loadUploadedImages(currentContentId);

            // 如果在图片上传页面，更新数量显示
            if (typeof updateImageCountDisplay === 'function') {
                getCurrentImageCount(currentContentId).then(count => {
                    updateImageCountDisplay(currentContentId, count);
                });
            }
        } else {
            showSimpleToast('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showSimpleToast('删除失败: ' + error.message, 'error');
    });
}

// 预览内容
function previewContent(contentId) {
    fetch(`/simple/api/contents/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = data.content;
                const topicsText = content.topics && content.topics.length > 0 ? content.topics.join(', ') : '无';
                const atUsersText = content.at_users && content.at_users.length > 0 ? content.at_users.map(u => '@' + u.replace('@', '')).join(', ') : '无';
                const previewText = `标题：${content.title}\n\n内容：${content.content}\n\n客户：${content.client_name || '未知'}\n话题：${topicsText}\n@用户：${atUsersText}\n定位：${content.location || '无'}`;
                alert(previewText);
            } else {
                alert('加载失败');
            }
        })
        .catch(error => {
            alert('加载失败');
        });
}

// 上传文件数组
function uploadFilesArray(files, contentId) {
    const modal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));
    const progressBar = document.querySelector('#uploadProgressModal .progress-bar');
    const statusDiv = document.getElementById('uploadStatus');

    modal.show();

    let uploadedCount = 0;
    const totalFiles = files.length;

    // 逐个上传文件
    Array.from(files).forEach((file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('content_id', contentId);

        fetch('/simple/api/upload/image', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            uploadedCount++;
            const progress = (uploadedCount / totalFiles) * 100;
            progressBar.style.width = progress + '%';

            if (data.success) {
                statusDiv.innerHTML += `<div class="text-success">✓ ${file.name} 上传成功</div>`;
            } else {
                statusDiv.innerHTML += `<div class="text-danger">✗ ${file.name} 上传失败: ${data.message}</div>`;
            }

            // 所有文件上传完成
            if (uploadedCount === totalFiles) {
                setTimeout(() => {
                    modal.hide();
                    // 如果在模态框中上传，刷新模态框中的图片显示
                    if (currentContentId) {
                        loadUploadedImages(currentContentId);
                        // 清空文件选择
                        const modalFileInput = document.getElementById('modalFileInput');
                        if (modalFileInput) {
                            modalFileInput.value = '';
                        }
                    } else {
                        // 否则刷新整个页面
                        location.reload();
                    }
                }, 1500);
            }
        })
        .catch(error => {
            uploadedCount++;
            const progress = (uploadedCount / totalFiles) * 100;
            progressBar.style.width = progress + '%';
            statusDiv.innerHTML += `<div class="text-danger">✗ ${file.name} 上传失败: ${error.message}</div>`;

            if (uploadedCount === totalFiles) {
                setTimeout(() => {
                    modal.hide();
                    // 如果在模态框中上传，刷新模态框中的图片显示
                    if (currentContentId) {
                        loadUploadedImages(currentContentId);
                        // 清空文件选择
                        const modalFileInput = document.getElementById('modalFileInput');
                        if (modalFileInput) {
                            modalFileInput.value = '';
                        }
                    } else {
                        // 否则刷新整个页面
                        location.reload();
                    }
                }, 1500);
            }
        });
    });
}

// 拖拽上传相关函数
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = document.getElementById('uploadDropZone');
    if (dropZone) {
        dropZone.classList.add('border-primary', 'bg-light');
    }
}

function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = document.getElementById('uploadDropZone');
    if (dropZone) {
        dropZone.classList.remove('border-primary', 'bg-light');
    }
}

function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();

    const dropZone = document.getElementById('uploadDropZone');
    if (dropZone) {
        dropZone.classList.remove('border-primary', 'bg-light');
    }

    const files = event.dataTransfer.files;
    if (files.length > 0 && currentContentId) {
        uploadFilesArray(files, currentContentId);
    }
}

function triggerFileSelect() {
    const fileInput = document.getElementById('modalFileInput');
    if (fileInput) {
        fileInput.click();
    }
}

function handleFileSelect(event) {
    const files = event.target.files;
    if (files.length > 0 && currentContentId) {
        uploadFilesArray(files, currentContentId);
    }
}

// 图片拖拽排序相关变量
let draggedImageElement = null;
let draggedImageId = null;

// 拖拽开始
function handleImageDragStart(event) {
    // 设置图片排序标志
    if (typeof isImageSorting !== 'undefined') {
        isImageSorting = true;
    } else {
        window.isImageSorting = true;
    }

    draggedImageElement = event.currentTarget;
    draggedImageId = event.currentTarget.getAttribute('data-image-id');
    event.currentTarget.style.opacity = '0.5';

    // 设置拖拽数据
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', event.currentTarget.outerHTML);
}

// 拖拽经过
function handleImageDragOver(event) {
    event.preventDefault();
    event.stopPropagation(); // 阻止事件冒泡到父级元素
    event.dataTransfer.dropEffect = 'move';

    const targetElement = event.currentTarget;
    if (targetElement !== draggedImageElement) {
        targetElement.style.borderColor = '#007bff';
        targetElement.style.borderWidth = '2px';
        targetElement.style.borderStyle = 'dashed';
    }
}

// 拖拽离开
function handleImageDragLeave(event) {
    const targetElement = event.currentTarget;
    targetElement.style.borderColor = '';
    targetElement.style.borderWidth = '';
    targetElement.style.borderStyle = '';
}

// 拖拽放下
function handleImageDrop(event) {
    event.preventDefault();
    event.stopPropagation(); // 阻止事件冒泡到父级元素

    const targetElement = event.currentTarget;
    const targetImageId = targetElement.getAttribute('data-image-id');

    // 重置样式
    targetElement.style.borderColor = '';
    targetElement.style.borderWidth = '';
    targetElement.style.borderStyle = '';

    if (draggedImageId && targetImageId && draggedImageId !== targetImageId) {
        // 交换位置
        swapImagePositions(draggedImageElement, targetElement);

        // 保存新的排序
        saveImageOrder();
    }
}

// 拖拽结束
function handleImageDragEnd(event) {
    event.currentTarget.style.opacity = '1';
    draggedImageElement = null;
    draggedImageId = null;

    // 清理所有元素的边框样式
    document.querySelectorAll('.image-item').forEach(item => {
        item.style.borderColor = '';
        item.style.borderWidth = '';
        item.style.borderStyle = '';
    });

    // 延迟重置图片排序标志，确保拖拽事件完全结束
    setTimeout(() => {
        if (typeof isImageSorting !== 'undefined') {
            isImageSorting = false;
        } else {
            window.isImageSorting = false;
        }
    }, 100);
}

// 交换图片位置
function swapImagePositions(element1, element2) {
    const parent = element1.parentNode;
    const nextSibling1 = element1.nextSibling;
    const nextSibling2 = element2.nextSibling;

    if (nextSibling1 === element2) {
        parent.insertBefore(element2, element1);
    } else if (nextSibling2 === element1) {
        parent.insertBefore(element1, element2);
    } else {
        parent.insertBefore(element1, nextSibling2);
        parent.insertBefore(element2, nextSibling1);
    }

    // 更新序号显示
    updateImageNumbers();
}

// 更新图片序号显示
function updateImageNumbers() {
    const imageItems = document.querySelectorAll('.image-item');
    imageItems.forEach((item, index) => {
        const numberElement = item.querySelector('.position-absolute.top-0.start-0');
        if (numberElement) {
            numberElement.textContent = index + 1;
        }
    });
}

// 保存图片排序
function saveImageOrder() {
    const imageItems = document.querySelectorAll('.image-item');
    const imageOrders = [];

    imageItems.forEach((item, index) => {
        const imageId = item.getAttribute('data-image-id');
        if (imageId) {
            imageOrders.push({
                id: parseInt(imageId),
                order: index + 1
            });
        }
    });

    if (imageOrders.length > 0) {
        fetch('/simple/api/images/reorder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image_orders: imageOrders
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('图片排序保存成功');
                // 显示简短的成功提示
                showSortSuccessToast();
            } else {
                console.error('图片排序保存失败:', data.message);
                showSimpleToast('排序保存失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('图片排序请求失败:', error);
            showSimpleToast('排序保存失败', 'error');
        });
    }
}

// 通用的简单toast提示函数
function showSimpleToast(message, type = 'success') {
    const toast = document.createElement('div');
    const bgClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
    const iconClass = type === 'success' ? 'bi-check-circle' : type === 'error' ? 'bi-x-circle' : 'bi-info-circle';

    toast.className = `position-fixed top-0 end-0 m-3 alert ${bgClass} alert-dismissible fade show`;
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <i class="bi ${iconClass} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 显示排序成功提示
function showSortSuccessToast() {
    showSimpleToast('图片顺序已保存', 'success');
}
</script>
{% endblock %}
