#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片修复
"""

import requests

def test_image_fixes():
    """测试图片修复"""
    print("🖼️ 测试图片显示和弹窗修复...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查图片路径修复
            if '/static/uploads/' in response.text:
                print("✅ 找到静态文件路径前缀")
            else:
                print("ℹ️ 未找到静态文件路径前缀（可能图片路径已包含完整路径）")
            
            # 检查图片放大功能
            if 'enlargeImage' in response.text:
                print("✅ 找到图片放大功能")
            else:
                print("❌ 未找到图片放大功能")
            
            # 检查图片弹窗层级修复
            if 'z-index: 2000' in response.text:
                print("✅ 找到图片弹窗层级修复")
            else:
                print("❌ 未找到图片弹窗层级修复")
            
            # 检查图片错误处理
            if 'onerror=' in response.text:
                print("✅ 找到图片错误处理")
            else:
                print("❌ 未找到图片错误处理")
            
            # 检查图片弹窗
            if 'imageModal' in response.text:
                print("✅ 找到图片放大弹窗")
            else:
                print("❌ 未找到图片放大弹窗")
            
            # 检查调试信息
            if 'console.log' in response.text and '放大图片' in response.text:
                print("✅ 找到调试信息")
            else:
                print("❌ 未找到调试信息")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 图片修复测试完成！")
    print("\n修复内容总结：")
    print("1. ✅ 图片路径修复 - 添加/static/uploads/前缀")
    print("2. ✅ 图片弹窗层级修复 - z-index: 2000")
    print("3. ✅ 图片错误处理 - 显示占位符")
    print("4. ✅ 图片悬停效果 - 缩放动画")
    print("5. ✅ 调试信息 - 控制台日志")
    print("\n现在应该可以：")
    print("1. 在详情弹窗右侧看到图片缩略图")
    print("2. 点击图片缩略图弹出放大预览")
    print("3. 图片放大弹窗在最顶层显示")
    print("4. 图片加载失败时显示占位符")

if __name__ == '__main__':
    test_image_fixes()
