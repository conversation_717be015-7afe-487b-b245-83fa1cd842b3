#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片路径修复
"""

import requests

def test_image_path_fix():
    """测试图片路径修复"""
    print("🔧 测试图片路径修复...")
    print("=" * 50)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功，image_path错误已修复")
            
            # 检查页面是否包含图片相关的功能
            if 'enlargeImage' in response.text:
                print("✅ 找到图片放大功能")
            
            if 'imageModal' in response.text:
                print("✅ 找到图片放大弹窗")
            
            if 'images-' in response.text:
                print("✅ 找到图片数据容器")
            
            # 检查是否有详情按钮
            detail_button_count = response.text.count('详情</button>')
            print(f"✅ 找到 {detail_button_count} 个详情按钮")
            
            # 检查弹窗大小
            if 'modal-xl' in response.text:
                print("✅ 详情弹窗已扩大为xl尺寸")
            
        elif response.status_code == 500:
            print("❌ 页面仍有服务器错误")
            print("可能的原因：")
            print("1. 数据库中的图片路径格式问题")
            print("2. 其他模型属性错误")
            print("3. JSON序列化问题")
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 图片路径修复测试完成！")
    print("\n修复内容：")
    print("1. ✅ 将 img.image_url 改为 img.image_path")
    print("2. ✅ 使用ContentImage模型的正确属性名")
    print("\n如果页面仍有问题，可能需要检查：")
    print("1. 数据库中的图片路径是否有效")
    print("2. 图片文件是否存在")
    print("3. 静态文件服务是否正常")

if __name__ == '__main__':
    test_image_path_fix()
