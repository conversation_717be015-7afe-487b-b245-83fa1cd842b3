{% extends "base_simple.html" %}

{% block title %}创建分享链接 - {{ client.name }}{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3 class="mb-0">创建分享链接 - {{ client.name }}</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> 分享链接可以让客户在不需要系统账号的情况下访问文案内容。
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    {{ form.has_password(class="form-check-input") }}
                                    {{ form.has_password.label(class="form-check-label") }}
                                    <div class="form-text text-muted">启用后，访问链接时需要输入密码</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.expires_days.label(class="form-label") }}
                                    {{ form.expires_days(class="form-select") }}
                                    <div class="form-text text-muted">链接的有效期限</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="mb-3">权限设置</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    {{ form.view_permission(class="form-check-input") }}
                                                    {{ form.view_permission.label(class="form-check-label") }}
                                                    <div class="form-text text-muted">允许查看文案内容</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    {{ form.edit_permission(class="form-check-input") }}
                                                    {{ form.edit_permission.label(class="form-check-label") }}
                                                    <div class="form-text text-muted">允许编辑文案内容</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    {{ form.review_permission(class="form-check-input") }}
                                                    {{ form.review_permission.label(class="form-check-label") }}
                                                    <div class="form-text text-muted">允许审核文案内容</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('client.share_list', client_id=client.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-share-alt"></i> 创建分享链接
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 