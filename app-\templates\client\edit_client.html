<!-- 编辑客户页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-person-gear"></i> 编辑客户</h2>
                <a href="{{ url_for('main_simple.clients') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回客户列表
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form method="post" action="{{ url_for('main_simple.edit_client', id=client.id) }}">
                        {{ form.csrf_token }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control", placeholder="请输入客户名称") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.contact.label(class="form-label") }}
                                    {{ form.contact(class="form-control", placeholder="请输入联系人") }}
                                    {% if form.contact.errors %}
                                        <div class="text-danger">
                                            {% for error in form.contact.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control", placeholder="请输入电话号码") }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger">
                                            {% for error in form.phone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control", placeholder="请输入邮箱地址") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.address.label(class="form-label") }}
                                    {{ form.address(class="form-control", placeholder="请输入地址") }}
                                    {% if form.address.errors %}
                                        <div class="text-danger">
                                            {% for error in form.address.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.daily_content_count.label(class="form-label") }}
                                    {{ form.daily_content_count(class="form-control", placeholder="每日内容数量") }}
                                    {% if form.daily_content_count.errors %}
                                        <div class="text-danger">
                                            {% for error in form.daily_content_count.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.interval_min.label(class="form-label") }}
                                    {{ form.interval_min(class="form-control", placeholder="最小间隔(分钟)") }}
                                    {% if form.interval_min.errors %}
                                        <div class="text-danger">
                                            {% for error in form.interval_min.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.interval_max.label(class="form-label") }}
                                    {{ form.interval_max(class="form-control", placeholder="最大间隔(分钟)") }}
                                    {% if form.interval_max.errors %}
                                        <div class="text-danger">
                                            {% for error in form.interval_max.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.display_start_time.label(class="form-label") }}
                                    {{ form.display_start_time(class="form-control") }}
                                    {% if form.display_start_time.errors %}
                                        <div class="text-danger">
                                            {% for error in form.display_start_time.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态设置</label>
                                    <div class="d-flex gap-4">
                                        <div class="form-check form-switch">
                                            {{ form.status(class="form-check-input", id="status_switch") }}
                                            <label class="form-check-label" for="status_switch">
                                                <span id="status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-switch">
                                            {{ form.need_review(class="form-check-input", id="need_review_switch") }}
                                            <label class="form-check-label" for="need_review_switch">
                                                <span id="need_review_text">{{ '需要审核' if form.need_review.data else '无需审核' }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.remark.label(class="form-label") }}
                                    {{ form.remark(class="form-control", rows="3", placeholder="请输入备注信息") }}
                                    {% if form.remark.errors %}
                                        <div class="text-danger">
                                            {% for error in form.remark.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存修改
                            </button>
                            <a href="{{ url_for('main_simple.clients') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 状态开关事件
document.getElementById('status_switch').addEventListener('change', function() {
    document.getElementById('status_text').textContent = this.checked ? '启用' : '禁用';
});

document.getElementById('need_review_switch').addEventListener('change', function() {
    document.getElementById('need_review_text').textContent = this.checked ? '需要审核' : '无需审核';
});

// 表单提交成功后的处理
{% if request.method == 'POST' and not form.errors %}
    // 如果是POST请求且没有错误，显示成功消息并跳转
    if (window.showToast) {
        showToast('客户信息更新成功！', 'success');
    } else {
        alert('客户信息更新成功！');
    }
    
    setTimeout(() => {
        window.location.href = "{{ url_for('main_simple.clients') }}";
    }, 1500);
{% endif %}
</script>
