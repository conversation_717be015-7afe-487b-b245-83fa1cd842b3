# -*- coding: utf-8 -*-
"""
主页视图
"""

from flask import Blueprint, redirect, url_for
from flask_login import login_required, current_user

# 创建主页蓝图
main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main_simple.dashboard'))
    return redirect(url_for('auth.login'))


@main_bp.route('/dashboard')
@login_required
def dashboard():
    """仪表盘 - 重定向到新版本后台"""
    return redirect(url_for('main_simple.dashboard'))