#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建待发布状态的测试数据
"""

import sys
import os
from datetime import datetime, date, time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

def create_pending_publish_data():
    """创建待发布状态的测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 直接使用SQL插入数据，确保状态正确
            sql = """
            INSERT INTO contents (
                title, content, topics, location, 
                workflow_status, publish_status, publish_priority, 
                display_date, display_time,
                created_at, client_id, task_id, batch_id, template_id, created_by, is_deleted
            ) VALUES 
            ('【高优先级】小红书种草神器推荐', 
             '最近发现了一个超好用的产品！真的是太惊喜了～\n\n用了一段时间，效果真的很棒，强烈推荐给大家！\n\n#种草分享 #好物推荐', 
             '["种草分享", "好物推荐", "生活分享"]', 
             '上海·静安区', 
             'pending_publish', 'unpublished', 'high', 
             '2025-07-28', '09:00:00',
             NOW(), 1, 1, 1, 1, 1, 0),
            ('【高优先级】护肤心得分享', 
             '分享一下最近的护肤心得～\n\n坚持使用了一个月，皮肤状态真的有明显改善！\n\n姐妹们可以试试看！\n\n#护肤分享 #美容心得', 
             '["护肤分享", "美容心得", "生活分享"]', 
             '深圳·南山区', 
             'pending_publish', 'unpublished', 'high', 
             '2025-07-28', '10:00:00',
             NOW(), 1, 1, 1, 1, 1, 0),
            ('【中优先级】今日穿搭分享', 
             '今天的穿搭分享来啦～\n\n这套搭配简约又时尚，很适合日常出街！\n\n大家觉得怎么样呢？\n\n#穿搭分享 #时尚搭配', 
             '["穿搭分享", "时尚搭配", "日常穿搭"]', 
             '北京·朝阳区', 
             'pending_publish', 'unpublished', 'normal', 
             '2025-07-28', '11:00:00',
             NOW(), 1, 1, 1, 1, 1, 0),
            ('【低优先级】美食探店记录', 
             '今天去了一家超棒的餐厅！\n\n环境很好，菜品也很精致，服务态度也很棒～\n\n推荐给喜欢美食的朋友们！\n\n#美食探店 #餐厅推荐', 
             '["美食探店", "餐厅推荐", "生活记录"]', 
             '广州·天河区', 
             'pending_publish', 'unpublished', 'low', 
             '2025-07-28', '12:00:00',
             NOW(), 1, 1, 1, 1, 1, 0)
            """
            
            # 执行插入
            from sqlalchemy import text
            db.session.execute(text(sql))
            db.session.commit()
            
            print("✅ 待发布测试数据插入成功！")
            
            # 验证插入的数据
            result = db.session.execute(text("""
                SELECT id, title, publish_priority, workflow_status, publish_status, display_date, display_time
                FROM contents 
                WHERE workflow_status = 'pending_publish' AND is_deleted = 0
                ORDER BY 
                    publish_priority DESC,
                    display_date ASC,
                    display_time ASC,
                    created_at ASC
            """))
            
            print("\n插入的待发布测试数据:")
            print("ID\t标题\t\t\t\t优先级\t工作流状态\t\t发布状态\t展示时间")
            print("-" * 100)
            for row in result:
                print(f"{row[0]}\t{row[1][:20]}...\t{row[2]}\t{row[3]}\t{row[4]}\t{row[5]} {row[6]}")
            
            print(f"\n现在可以测试API了！API会按优先级自动返回最高优先级的待发布文案。")
            
        except Exception as e:
            print(f"插入数据失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_pending_publish_data()
