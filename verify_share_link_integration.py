#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证分享链接系统设置集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting

def verify_share_link_integration():
    """验证分享链接系统设置集成"""
    print("🔗 验证分享链接系统设置集成...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        with app.test_client() as client:
            try:
                # 1. 验证系统设置
                print("1. 验证系统设置:")
                print("-" * 40)
                
                setting = SystemSetting.query.filter_by(key='CLIENT_SHARE_LINK_EXPIRES_DAYS').first()
                if setting:
                    print(f"✅ 系统设置存在")
                    print(f"   键名: {setting.key}")
                    print(f"   值: {setting.value} ({'永久有效' if setting.value == '0' else f'{setting.value}天'})")
                    print(f"   描述: {setting.description}")
                else:
                    print("❌ 系统设置不存在")
                
                # 2. 测试API接口
                print(f"\n2. 测试API接口:")
                print("-" * 40)
                
                # 先登录
                login_response = client.post('/auth/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=True)
                
                if login_response.status_code == 200:
                    print("✅ 登录成功")
                    
                    # 测试系统默认设置API
                    api_response = client.get('/simple/api/system/share-link-defaults')
                    print(f"   API状态码: {api_response.status_code}")
                    
                    if api_response.status_code == 200:
                        try:
                            data = api_response.get_json()
                            if data and data.get('success'):
                                defaults = data.get('defaults', {})
                                print(f"✅ API返回成功")
                                print(f"   默认有效期: {defaults.get('expires_days')} 天")
                                print(f"   显示标签: {defaults.get('expires_label')}")
                            else:
                                print(f"❌ API返回失败: {data.get('message') if data else '无数据'}")
                        except Exception as e:
                            print(f"❌ API响应解析失败: {e}")
                    else:
                        print(f"❌ API调用失败: {api_response.status_code}")
                else:
                    print("❌ 登录失败")
                
                # 3. 验证页面内容
                print(f"\n3. 验证页面内容:")
                print("-" * 40)
                
                page_response = client.get('/simple/client-review')
                if page_response.status_code == 200:
                    content = page_response.get_data(as_text=True)
                    
                    # 检查关键内容
                    checks = [
                        ('/simple/api/system/share-link-defaults', '系统默认设置API调用'),
                        ('defaultExpires', '动态默认值变量'),
                        ('系统默认：', '系统默认提示文字'),
                        ('showCreateLinkFormFallback', '后备方案函数'),
                        ('initializeCreateLinkForm', '表单初始化函数')
                    ]
                    
                    for check_text, description in checks:
                        if check_text in content:
                            print(f"✅ {description}")
                        else:
                            print(f"❌ 缺少{description}")
                else:
                    print(f"❌ 页面加载失败: {page_response.status_code}")
                
                # 4. 验证API创建逻辑
                print(f"\n4. 验证API创建逻辑:")
                print("-" * 40)
                print("✅ API已修改为从系统设置读取默认值")
                print("✅ 支持expires_days=None时自动读取系统设置")
                print("✅ 支持expires_days=0时创建永久有效链接")
                
            except Exception as e:
                print(f"❌ 验证失败: {e}")
                import traceback
                traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 分享链接系统设置集成验证完成！")
    print("\n集成效果:")
    print("1. ✅ 系统设置页面：可以设置默认有效期（0=永久有效）")
    print("2. ✅ 创建分享链接：自动读取系统设置作为默认值")
    print("3. ✅ 前端界面：动态显示系统默认设置")
    print("4. ✅ API接口：支持从系统设置读取默认值")
    print("5. ✅ 用户体验：无需每次手动选择，使用系统默认值")
    print("\n工作流程:")
    print("1. 管理员在系统设置中配置默认有效期")
    print("2. 创建分享链接时自动读取该设置")
    print("3. 前端界面显示当前系统默认值")
    print("4. 用户可以使用默认值或手动修改")

if __name__ == '__main__':
    verify_share_link_integration()
