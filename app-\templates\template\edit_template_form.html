<!-- 专用于编辑模板的独立表单页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" id="edit-template-form">
                        {{ form.csrf_token }}
                        <input type="hidden" name="redirect_after_submit" value="{{ url_for('template.index') }}">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.title.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-card-heading"></i>
                                        </span>
                                        {{ form.title(class="form-control", id="edit_title_field", placeholder="请输入模板标题，点击下方标记按钮可插入标记") }}
                                    </div>
                                    {% if form.title.errors %}
                                        {% for error in form.title.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        提示：点击下方的标记按钮可以快速插入标记到标题中 | 当前焦点：<span id="edit-title-focus-indicator" class="text-success">点击此处输入标题</span>
                                    </small>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.category_id.label(class="form-label") }}
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-folder"></i>
                                        </span>
                                        {{ form.category_id(class="form-select") }}
                                    </div>
                                    {% if form.category_id.errors %}
                                        {% for error in form.category_id.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="edit_status_switch" name="status" {{ 'checked' if form.status.data else '' }}>
                                        <label class="form-check-label" for="edit_status_switch">
                                            <span id="edit_status_text">{{ '启用' if form.status.data else '禁用' }}</span>
                                        </label>
                                    </div>
                                    {% if form.status.errors %}
                                        {% for error in form.status.errors %}
                                        <div class="invalid-feedback d-block">{{ error }}</div>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 可用标记按钮 -->
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-tags"></i> 可用标记
                            </label>
                            <div class="d-flex flex-wrap gap-2">
                                {% for mark in marks %}
                                <button type="button" class="btn btn-outline-primary btn-sm mark-button" 
                                        onclick="editInsertMark('{{ mark.name }}')" 
                                        title="{{ mark.description }}">
                                    <i class="bi bi-tag"></i> {{ mark.name }}
                                </button>
                                {% endfor %}
                            </div>
                            <small class="form-text text-muted">
                                <i class="bi bi-lightbulb"></i>
                                点击标记按钮将在当前焦点位置插入标记
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-file-text"></i> {{ form.content.label.text }}
                            </label>
                            {{ form.content(class="form-control", id="edit_content", rows="15", style="min-height: 400px; font-family: 'Courier New', monospace;", placeholder="请输入模板内容，点击上方标记按钮可插入标记...") }}
                            {% if form.content.errors %}
                                {% for error in form.content.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle"></i>
                                提示：点击上方的标记按钮可以快速插入标记到内容中 | 当前焦点：<span id="edit-content-focus-indicator" class="text-primary">点击此处输入内容</span>
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> 取消
                            </button>
                            <button type="button" class="btn btn-primary" id="edit-submit-btn">
                                <i class="bi bi-check-circle"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .mark-button {
        margin: 5px;
    }
    
    /* 标记的通用样式 */
    .template-mark {
        background-color: #e9f5ff;
        border: 1px solid #b3d7ff;
        border-radius: 3px;
        padding: 2px 4px;
        font-weight: bold;
        color: #0066cc;
        display: inline-block;
        margin: 0 2px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* 输入框焦点状态指示 */
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .current-focus {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    .has-marks {
        background-color: #f8f9fa;
    }
</style>

<!-- JavaScript逻辑已移至主页面，避免冲突 -->
