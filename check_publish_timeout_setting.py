#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查发布超时设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting

def check_publish_timeout_setting():
    """检查发布超时设置"""
    print("⏰ 检查发布超时设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取PUBLISH_TIMEOUT设置
            timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
            
            if timeout_setting:
                timeout_value = int(timeout_setting.value)
                timeout_minutes = timeout_value / 60
                print(f"✅ 找到PUBLISH_TIMEOUT设置")
                print(f"   设置值: {timeout_value} 秒")
                print(f"   等于: {timeout_minutes} 分钟")
                print(f"   描述: {timeout_setting.description}")
                
                # 检查是否与页面显示的120秒一致
                if timeout_value == 120:
                    print("✅ 设置值与页面显示一致（120秒 = 2分钟）")
                else:
                    print(f"❌ 设置值与页面显示不一致")
                    print(f"   页面显示: 120秒（2分钟）")
                    print(f"   数据库值: {timeout_value}秒（{timeout_minutes}分钟）")
            else:
                print("❌ 未找到PUBLISH_TIMEOUT设置")
                print("系统将使用默认值")
            
            # 检查超时处理策略
            action_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
            if action_setting:
                print(f"✅ 超时处理策略: {action_setting.value}")
            else:
                print("❌ 未找到超时处理策略设置")
            
            # 模拟API调用的超时检查逻辑
            print(f"\n🔍 API超时检查逻辑模拟:")
            if timeout_setting:
                api_timeout = int(timeout_setting.value)
            else:
                api_timeout = 7200  # API中的默认值
            
            print(f"API使用的超时时间: {api_timeout}秒（{api_timeout/60}分钟）")
            
            # 检查不同地方的默认值
            print(f"\n📋 不同地方的默认值:")
            print(f"API获取文案 (api.py:184): 7200秒（2小时）")
            print(f"API设置接口 (api.py:452): 86400秒（24小时）")
            print(f"超时检查接口 (api.py:476): 7200秒（2小时）")
            print(f"发布管理页面: 86400秒（24小时）")
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 发布超时设置检查完成！")

if __name__ == '__main__':
    check_publish_timeout_setting()
