#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查client_share_enabled设置的实际使用情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def check_client_share_usage():
    """检查client_share_enabled设置的实际使用情况"""
    print("🔍 检查客户分享功能设置的实际使用情况...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找分享设置
            print("1. 查找分享设置:")
            print("-" * 40)
            
            share_setting = SystemSetting.query.filter_by(key='client_share_enabled').first()
            
            if share_setting:
                print(f"🔍 找到设置: {share_setting.key}")
                print(f"  当前值: {share_setting.value}")
                print(f"  描述: {share_setting.description}")
                print(f"  ID: {share_setting.id}")
            else:
                print("❌ 未找到 client_share_enabled 设置")
                return
            
            # 2. 搜索代码中的使用情况
            print(f"\n2. 搜索代码中的使用情况:")
            print("-" * 40)
            
            usage_files = []
            search_terms = ['client_share_enabled']
            
            # 搜索所有Python文件
            for root, dirs, files in os.walk('.'):
                # 跳过一些不需要搜索的目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'node_modules']]
                
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                for term in search_terms:
                                    if term in content:
                                        usage_files.append(file_path)
                                        break
                        except:
                            pass
            
            if usage_files:
                print("🔍 找到使用分享设置的文件:")
                for file_path in usage_files:
                    print(f"  - {file_path}")
                    
                    # 显示具体的使用上下文
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            for i, line in enumerate(lines):
                                if 'client_share_enabled' in line:
                                    print(f"    第{i+1}行: {line.strip()}")
                    except:
                        pass
                    print()
            else:
                print("❌ 没有找到使用分享设置的文件")
            
            # 3. 检查客户审核页面是否检查这个设置
            print(f"\n3. 检查客户审核页面:")
            print("-" * 40)
            
            client_review_file = "app/views/client_review.py"
            if os.path.exists(client_review_file):
                with open(client_review_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'client_share_enabled' in content:
                        print("✅ 客户审核页面检查了分享设置")
                    else:
                        print("❌ 客户审核页面没有检查分享设置")
                        print("   这意味着即使关闭分享功能，客户仍然可以访问审核页面")
            else:
                print("❌ 客户审核页面文件不存在")
            
            # 4. 检查分享链接创建是否检查这个设置
            print(f"\n4. 检查分享链接创建:")
            print("-" * 40)
            
            # 检查可能创建分享链接的文件
            share_creation_files = [
                "app/views/main_simple.py",
                "app/utils/share_link.py"
            ]
            
            for file_path in share_creation_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'client_share_enabled' in content:
                            print(f"✅ {file_path} 检查了分享设置")
                        else:
                            print(f"❌ {file_path} 没有检查分享设置")
            
            # 5. 分析设置的实际作用
            print(f"\n5. 设置作用分析:")
            print("-" * 40)
            
            print("📋 client_share_enabled 设置分析:")
            print("  🔍 设置名称: 客户分享功能")
            print("  🔍 当前状态: 启用")
            print("  🔍 预期功能: 控制客户是否可以通过分享链接审核文案")
            print()
            
            if not any('client_share_enabled' in open(f, 'r', encoding='utf-8').read() 
                      for f in ['app/views/client_review.py'] if os.path.exists(f)):
                print("❌ 功能状态: 设置无效")
                print("  - 客户审核页面没有检查该设置")
                print("  - 即使关闭分享功能，客户仍可访问审核页面")
                print("  - 设置开关无实际作用")
                print("  - 用户看到开关但功能不受控制")
            else:
                print("✅ 功能状态: 设置有效")
                print("  - 相关页面检查了该设置")
                print("  - 可以有效控制分享功能")
            
            # 6. 测试当前链接
            print(f"\n6. 测试分享链接访问:")
            print("-" * 40)
            
            test_url = "http://127.0.0.1:5000/client-review/4dbc790d5015faeca985cb74da6f43fb?key=DWYR"
            print(f"测试链接: {test_url}")
            print("根据代码分析:")
            print("  - 该链接会直接访问客户审核页面")
            print("  - 不会检查 client_share_enabled 设置")
            print("  - 即使关闭分享功能，链接仍然有效")
            print("  - 审核按钮仍然可以使用")
            
            # 7. 修复建议
            print(f"\n7. 修复建议:")
            print("-" * 40)
            
            print("🔧 要让设置真正起作用，需要:")
            print("  1. 在客户审核页面检查 client_share_enabled 设置")
            print("  2. 如果设置为关闭，显示功能已禁用的提示")
            print("  3. 禁用审核按钮和相关功能")
            print("  4. 在分享链接创建时也检查该设置")
            print()
            print("🗑️ 或者删除该无用设置:")
            print("  - 如果不需要控制分享功能的开关")
            print("  - 简化系统设置页面")
            print("  - 避免用户困惑")
            
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 客户分享功能设置检查完成！")

if __name__ == '__main__':
    check_client_share_usage()
