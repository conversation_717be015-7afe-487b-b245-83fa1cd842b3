<!-- 模板标记管理模态框内容 -->
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <h5>模板标记管理</h5>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary btn-sm" onclick="showAddMarkModal()">
                <i class="bi bi-plus-lg"></i> 添加标记
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>标记名称</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mark in marks %}
                        <tr>
                            <td>{{ mark.id }}</td>
                            <td>
                                <span class="badge bg-primary">{{ mark.name }}</span>
                            </td>
                            <td>{{ mark.description or '-' }}</td>
                            <td>
                                <small class="text-muted">{{ mark.created_at.strftime('%Y-%m-%d %H:%M') if mark.created_at else '-' }}</small>
                            </td>
                            <td class="mark-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="showEditMarkModal({{ mark.id }})" 
                                        title="编辑标记">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="deleteMarkById({{ mark.id }})" 
                                        title="删除标记">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if marks|length == 0 %}
            <div class="text-center py-4">
                <i class="bi bi-tags" style="font-size: 3rem; color: #ccc;"></i>
                <p class="text-muted mt-2">暂无标记数据</p>
                <button type="button" class="btn btn-primary" onclick="showAddMarkModal()">
                    <i class="bi bi-plus-lg"></i> 添加第一个标记
                </button>
            </div>
            {% endif %}

            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="分页导航">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage({{ pagination.prev_num }}); return false;">上一页</a>
                    </li>
                    {% endif %}

                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="#" onclick="changePage({{ page_num }}); return false;">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="changePage({{ pagination.next_num }}); return false;">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>

            <!-- 每页显示数量选择 -->
            <div class="d-flex justify-content-center align-items-center mt-3">
                <label for="per-page-select" class="form-label me-2">每页显示：</label>
                <select class="form-select" id="per-page-select" style="width: auto;" onchange="changePageSize(this.value)">
                    <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                    <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                    <option value="30" {% if per_page == 30 %}selected{% endif %}>30</option>
                    <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                    <option value="80" {% if per_page == 80 %}selected{% endif %}>80</option>
                    <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                </select>
                <span class="text-muted ms-2">条记录</span>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 添加标记模态框 -->
<div class="modal fade" id="addMarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加标记</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="addMarkContent">
                <!-- 内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 编辑标记模态框 -->
<div class="modal fade" id="editMarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑标记</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="editMarkContent">
                <!-- 内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>

<style>
    .mark-actions {
        white-space: nowrap;
    }
</style>

<script>
console.log('🚀 标记管理模态框JavaScript加载');

// 分页函数
function changePage(pageNum) {
    console.log('📄 标记管理分页:', pageNum);
    if (window.currentModalAjaxHandler) {
        const perPage = document.querySelector('#per-page-select')?.value || 20;
        window.currentModalAjaxHandler(pageNum, perPage);
    }
}

// 每页显示数量变更
function changePageSize(newSize) {
    console.log('📊 标记管理每页显示数量变更:', newSize);
    if (window.currentModalAjaxHandler) {
        window.currentModalAjaxHandler(1, newSize);
    }
}
</script>
