#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试页面显示效果
"""

import requests
import json

def test_page_display():
    """测试页面显示效果"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("📋 测试页面显示效果...")
    print("=" * 50)
    
    # 创建一些测试数据来验证页面显示
    print("\n1. 创建测试数据...")
    
    # 获取一篇文案并设置不同的状态和提示信息
    try:
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                
                print(f"✅ 获取到文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                
                # 设置一个详细的成功提示信息
                success_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/discovery/67890',
                    'platform': '小红书',
                    'account': '测试账号@页面显示',
                    'message': '🎉 发布成功！获得了1.2K点赞，500评论，转发率达到8.5%'
                }
                
                print(f"\n2. 设置成功提示信息...")
                print(f"提示内容: {success_data['message']}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=success_data,
                    timeout=10
                )
                
                if update_response.status_code == 200:
                    result = update_response.json()
                    if result.get('success'):
                        print("✅ 成功状态设置完成！")
                        print(f"现在可以在页面上看到：")
                        print(f"- ID列显示: {content_id}")
                        print(f"- 提示信息显示: 包含时间戳的成功信息")
                        print(f"- 发布时间列已隐藏")
                    else:
                        print(f"❌ 状态设置失败: {result.get('error')}")
                else:
                    print(f"❌ 状态设置请求失败: {update_response.status_code}")
                
                # 获取下一篇文案设置失败状态
                print(f"\n3. 获取下一篇文案设置失败状态...")
                
                response2 = requests.get(f'{base_url}/content', headers=headers, timeout=10)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        content2 = data2.get('content')
                        content_id2 = content2.get('id')
                        
                        print(f"✅ 获取到文案ID: {content_id2}")
                        
                        # 设置一个详细的失败提示信息
                        failed_data = {
                            'status': 'failed',
                            'platform': '小红书',
                            'account': '测试账号@页面显示',
                            'message': '❌ 发布失败：内容包含敏感词"特价"，建议替换为"优惠"后重新发布'
                        }
                        
                        print(f"提示内容: {failed_data['message']}")
                        
                        update_response2 = requests.post(
                            f'{base_url}/content/{content_id2}/status',
                            headers=headers,
                            json=failed_data,
                            timeout=10
                        )
                        
                        if update_response2.status_code == 200:
                            result2 = update_response2.json()
                            if result2.get('success'):
                                print("✅ 失败状态设置完成！")
                                print(f"现在可以在页面上看到：")
                                print(f"- ID列显示: {content_id2}")
                                print(f"- 提示信息显示: 包含时间戳的失败原因")
                            else:
                                print(f"❌ 失败状态设置失败: {result2.get('error')}")
                        else:
                            print(f"❌ 失败状态设置请求失败: {update_response2.status_code}")
                    else:
                        print(f"ℹ️ 没有更多文案: {data2.get('message')}")
                else:
                    print(f"❌ 获取下一篇文案失败: {response2.status_code}")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 页面显示测试完成！")
    print("\n请打开以下页面查看效果：")
    print("http://127.0.0.1:5000/simple/publish-status-manage")
    print("\n预期看到的改进：")
    print("1. ✅ 第一列显示文案ID（灰色徽章）")
    print("2. ✅ 发布时间列已移除")
    print("3. ✅ 提示信息列显示详细的状态信息")
    print("4. ✅ 不同状态用不同颜色显示")
    print("5. ✅ 包含时间戳的智能提示")

if __name__ == '__main__':
    test_page_display()
