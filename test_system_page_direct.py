#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接测试系统设置页面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting
from flask import url_for

def test_system_page_direct():
    """直接测试系统设置页面"""
    print("🔧 直接测试系统设置页面...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        with app.test_client() as client:
            try:
                # 先登录
                login_response = client.post('/auth/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=True)
                
                print(f"登录响应状态码: {login_response.status_code}")
                
                # 访问系统设置页面
                response = client.get('/simple/system')
                print(f"系统设置页面状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.get_data(as_text=True)
                    print("✅ 页面加载成功")
                    
                    # 检查关键内容
                    if '系统设置' in content:
                        print("✅ 找到系统设置标题")
                    else:
                        print("❌ 未找到系统设置标题")
                    
                    # 检查是否有错误信息
                    if 'error' in content.lower() or 'exception' in content.lower():
                        print("❌ 页面包含错误信息")
                        # 查找错误信息
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if 'error' in line.lower() or 'exception' in line.lower():
                                print(f"错误行 {i+1}: {line.strip()}")
                    else:
                        print("✅ 页面没有错误信息")
                    
                    # 检查模板变量
                    if 'workflow_settings' in content:
                        print("✅ 找到workflow_settings变量")
                    else:
                        print("❌ 未找到workflow_settings变量")
                    
                    if 'upload_settings' in content:
                        print("✅ 找到upload_settings变量")
                    else:
                        print("❌ 未找到upload_settings变量")
                    
                    # 保存页面内容到文件以便调试
                    with open('system_page_debug.html', 'w', encoding='utf-8') as f:
                        f.write(content)
                    print("✅ 页面内容已保存到 system_page_debug.html")
                    
                    # 显示页面的前500个字符
                    print(f"\n页面内容预览:")
                    print("-" * 40)
                    print(content[:500])
                    print("-" * 40)
                    
                else:
                    print(f"❌ 页面加载失败: {response.status_code}")
                    print(f"响应内容: {response.get_data(as_text=True)[:200]}")
                
                # 测试系统设置数据
                print(f"\n测试系统设置数据:")
                settings = SystemSetting.get_all_settings()
                print(f"设置数量: {len(settings)}")
                
                # 分组测试
                workflow_settings = []
                upload_settings = []
                other_settings = []
                
                for setting in settings:
                    if setting.key.startswith('ENABLE_'):
                        workflow_settings.append(setting)
                    elif setting.key.startswith('IMAGE_') or setting.key.startswith('MAX_'):
                        upload_settings.append(setting)
                    else:
                        other_settings.append(setting)
                
                print(f"工作流设置: {len(workflow_settings)} 个")
                print(f"上传设置: {len(upload_settings)} 个")
                print(f"其他设置: {len(other_settings)} 个")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 直接测试完成！")

if __name__ == '__main__':
    test_system_page_direct()
