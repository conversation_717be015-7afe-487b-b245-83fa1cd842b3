#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试详情弹窗功能
"""

import requests
import json

def test_detail_modal():
    """测试详情弹窗功能"""
    print("🔍 测试详情弹窗功能...")
    print("=" * 50)
    
    # 测试详情API
    try:
        # 使用一个已知的文案ID进行测试（从之前的日志中看到的ID 90）
        content_id = 90
        
        # 模拟浏览器请求（需要登录状态）
        session = requests.Session()
        
        # 先访问登录页面获取session
        login_response = session.get('http://127.0.0.1:5000/auth/login')
        if login_response.status_code == 200:
            print("✅ 可以访问登录页面")
        
        # 测试详情API（不需要登录的话）
        detail_response = session.get(f'http://127.0.0.1:5000/simple/api/content-detail/{content_id}')
        
        print(f"详情API状态码: {detail_response.status_code}")
        
        if detail_response.status_code == 200:
            try:
                data = detail_response.json()
                if data.get('success'):
                    content = data.get('content')
                    print("✅ 详情API调用成功！")
                    print(f"文案ID: {content.get('id')}")
                    print(f"标题: {content.get('title')}")
                    print(f"客户: {content.get('client_name')}")
                    print(f"优先级: {content.get('priority')}")
                    print(f"工作流状态: {content.get('workflow_status')}")
                    print(f"图片数量: {len(content.get('images', []))}")
                    print(f"发布记录数量: {len(content.get('publish_records', []))}")
                else:
                    print(f"❌ API返回失败: {data.get('message')}")
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {detail_response.text[:200]}")
        elif detail_response.status_code == 302:
            print("ℹ️ 需要登录才能访问详情API")
        else:
            print(f"❌ 详情API调用失败: {detail_response.status_code}")
            print(f"响应: {detail_response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 详情弹窗测试完成！")
    print("\n使用方法：")
    print("1. 打开页面: http://127.0.0.1:5000/simple/publish-status-manage")
    print("2. 点击任意文案的'详情'按钮")
    print("3. 应该弹出详情弹窗，显示：")
    print("   - 基本信息（ID、标题、客户、优先级等）")
    print("   - 发布状态信息")
    print("   - 文案内容")
    print("   - 关联图片（如果有）")
    print("   - 发布记录历史（如果有）")

if __name__ == '__main__':
    test_detail_modal()
