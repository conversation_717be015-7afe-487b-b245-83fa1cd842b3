#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库中的提示信息
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.content import Content

def check_database_messages():
    """检查数据库中的提示信息"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 检查数据库中的提示信息...")
            print("=" * 60)
            
            # 查询最近更新的文案
            recent_contents = Content.query.filter(
                Content.workflow_status.in_(['published', 'publish_failed'])
            ).order_by(Content.status_update_time.desc()).limit(10).all()
            
            if recent_contents:
                print(f"找到 {len(recent_contents)} 篇最近更新的文案:")
                print()
                
                for i, content in enumerate(recent_contents, 1):
                    print(f"{i}. 文案ID: {content.id}")
                    print(f"   标题: {content.title[:40]}...")
                    print(f"   工作流状态: {content.workflow_status}")
                    print(f"   发布状态: {content.publish_status}")
                    print(f"   状态更新时间: {content.status_update_time}")
                    print(f"   提示信息(publish_error): '{content.publish_error}'")
                    
                    # 检查提示信息是否为空
                    if content.publish_error:
                        print(f"   ✅ 有提示信息")
                    else:
                        print(f"   ❌ 提示信息为空")
                    
                    print("-" * 50)
                
                # 特别检查ID为76的文案（从日志中看到的）
                content_76 = Content.query.get(76)
                if content_76:
                    print(f"\n🎯 特别检查文案ID 76 (从日志中看到的):")
                    print(f"   标题: {content_76.title}")
                    print(f"   工作流状态: {content_76.workflow_status}")
                    print(f"   发布状态: {content_76.publish_status}")
                    print(f"   状态更新时间: {content_76.status_update_time}")
                    print(f"   提示信息: '{content_76.publish_error}'")
                    
                    if content_76.publish_error:
                        print(f"   ✅ 文案76有提示信息，应该在页面显示")
                    else:
                        print(f"   ❌ 文案76没有提示信息")
                else:
                    print(f"\n❌ 找不到文案ID 76")
                
            else:
                print("❌ 没有找到已发布或发布失败的文案")
            
        except Exception as e:
            print(f"检查失败: {e}")

if __name__ == '__main__':
    check_database_messages()
