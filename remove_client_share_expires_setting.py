#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
删除客户分享链接有效期的全局默认设置
保留分享功能开关，删除有效期默认值（创建时可以直接设置）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def remove_client_share_expires_setting():
    """删除客户分享链接有效期的全局默认设置"""
    print("🗑️ 删除客户分享链接有效期的全局默认设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找要删除的设置
            print("1. 查找要删除的设置:")
            print("-" * 40)
            
            expires_setting = SystemSetting.query.filter_by(key='CLIENT_SHARE_LINK_EXPIRES_DAYS').first()
            
            if not expires_setting:
                print("✅ CLIENT_SHARE_LINK_EXPIRES_DAYS 设置不存在，无需删除")
                return
            
            print(f"🔍 找到设置: {expires_setting.key}")
            print(f"  当前值: {expires_setting.value}")
            print(f"  描述: {expires_setting.description}")
            print(f"  ID: {expires_setting.id}")
            
            # 2. 确认删除原因
            print(f"\n2. 删除原因:")
            print("-" * 40)
            print("删除CLIENT_SHARE_LINK_EXPIRES_DAYS的原因:")
            print("  ✅ 创建分享链接时可以直接选择有效期")
            print("  ✅ 用户界面提供了丰富的选项（永久、1天、3天、7天等）")
            print("  ✅ 全局默认值很少被使用")
            print("  ✅ 简化系统设置页面，减少冗余选项")
            print("  ✅ 提高用户体验，设置更直观")
            
            # 3. 确认保留的设置
            print(f"\n3. 确认保留的分享相关设置:")
            print("-" * 40)
            
            share_enabled_setting = SystemSetting.query.filter_by(key='client_share_enabled').first()
            if share_enabled_setting:
                print(f"✅ 保留: client_share_enabled = {share_enabled_setting.value}")
                print(f"   原因: 系统级功能开关，控制整个分享功能的启用/禁用")
            else:
                print("❌ 未找到 client_share_enabled 设置")
            
            # 4. 检查代码影响
            print(f"\n4. 代码影响分析:")
            print("-" * 40)
            print("删除此设置后的影响:")
            print("  ✅ 创建分享链接时会使用代码中的默认值")
            print("  ✅ 用户仍然可以在界面上选择任意有效期")
            print("  ✅ 不会影响现有分享链接的功能")
            print("  ✅ 系统设置页面更加简洁")
            
            # 5. 执行删除操作
            print(f"\n5. 执行删除操作:")
            print("-" * 40)
            
            try:
                print(f"🗑️ 删除设置: {expires_setting.key} (ID: {expires_setting.id})")
                db.session.delete(expires_setting)
                db.session.commit()
                print(f"✅ 成功删除 CLIENT_SHARE_LINK_EXPIRES_DAYS 设置")
            except Exception as e:
                db.session.rollback()
                print(f"❌ 删除失败: {e}")
                raise
            
            # 6. 验证删除结果
            print(f"\n6. 验证删除结果:")
            print("-" * 40)
            
            # 确认设置已删除
            deleted_setting = SystemSetting.query.filter_by(key='CLIENT_SHARE_LINK_EXPIRES_DAYS').first()
            if deleted_setting:
                print(f"❌ 设置仍然存在，删除失败")
            else:
                print(f"✅ 设置已成功删除")
            
            # 显示剩余的分享相关设置
            remaining_share_settings = SystemSetting.query.filter(
                SystemSetting.key.like('%share%')
            ).all()
            
            print(f"\n剩余的分享相关设置:")
            for setting in remaining_share_settings:
                print(f"  ✅ {setting.key}: {setting.value}")
                print(f"      {setting.description}")
            
            # 7. 使用指南
            print(f"\n7. 使用指南:")
            print("-" * 40)
            print("删除此设置后，用户应该:")
            print("  1. 🔧 在创建分享链接时直接选择有效期")
            print("  2. 📋 根据具体需求选择合适的有效期")
            print("  3. 🎯 不再依赖全局默认值")
            print()
            print("可选的有效期:")
            print("  - 永久有效 (推荐用于长期合作客户)")
            print("  - 1天 (用于临时查看)")
            print("  - 3天 (用于短期审核)")
            print("  - 7天 (用于一周内的审核)")
            print("  - 15天 (用于半月内的审核)")
            print("  - 30天 (用于月度审核)")
            print("  - 90天 (用于季度审核)")
            
        except Exception as e:
            print(f"❌ 删除过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 客户分享链接有效期设置删除完成！")
    print("\n总结:")
    print("1. 🗑️ 删除了冗余的全局有效期默认值")
    print("2. ✅ 保留了分享功能开关")
    print("3. 🎯 用户在创建分享链接时直接设置有效期")
    print("4. 🧹 系统设置页面更加简洁")
    print("\n现在系统设置页面不再显示客户分享链接有效期选项！")

if __name__ == '__main__':
    remove_client_share_expires_setting()
