#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优先级弹窗功能
"""

import requests

def test_priority_modal():
    """测试优先级弹窗功能"""
    print("🎯 测试优先级弹窗功能...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查优先级设置图标按钮
            if 'bi-gear' in response.text:
                print("✅ 找到优先级设置图标")
            else:
                print("❌ 未找到优先级设置图标")
            
            # 检查优先级弹窗
            if 'priorityModal' in response.text:
                print("✅ 找到优先级设置弹窗")
            else:
                print("❌ 未找到优先级设置弹窗")
            
            # 检查弹窗函数
            if 'showPriorityModal' in response.text:
                print("✅ 找到弹窗显示函数")
            else:
                print("❌ 未找到弹窗显示函数")
            
            # 检查优先级选项按钮
            priority_buttons = ['高优先级', '普通优先级', '低优先级']
            found_buttons = 0
            for button_text in priority_buttons:
                if button_text in response.text:
                    found_buttons += 1
            
            print(f"✅ 找到 {found_buttons}/{len(priority_buttons)} 个优先级选项按钮")
            
            # 检查图标
            icons = ['bi-arrow-up-circle', 'bi-dash-circle', 'bi-arrow-down-circle']
            found_icons = 0
            for icon in icons:
                if icon in response.text:
                    found_icons += 1
            
            print(f"✅ 找到 {found_icons}/{len(icons)} 个优先级图标")
            
            # 检查弹窗样式
            if 'priority-option' in response.text:
                print("✅ 找到优先级选项样式")
            else:
                print("❌ 未找到优先级选项样式")
            
            # 检查批量设置功能
            if 'batch-set-priority-btn' in response.text:
                print("✅ 保留批量设置功能")
            else:
                print("❌ 批量设置功能丢失")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 优先级弹窗功能测试完成！")
    print("\n新的优先级设置方式：")
    print("1. ✅ 设置图标 - 每行优先级旁边有齿轮图标")
    print("2. ✅ 弹窗选择 - 点击图标弹出优先级选择弹窗")
    print("3. ✅ 直观选择 - 三个大按钮分别对应高/普通/低优先级")
    print("4. ✅ 视觉反馈 - 当前优先级高亮显示")
    print("5. ✅ 即时更新 - 选择后立即保存并刷新页面")
    print("\n使用方法：")
    print("【单个设置】")
    print("1. 点击任意文案行优先级列的齿轮图标")
    print("2. 在弹窗中点击目标优先级按钮")
    print("3. 系统自动保存并刷新页面显示")
    print("\n【批量设置】")
    print("1. 勾选要设置的文案")
    print("2. 在右上角选择目标优先级")
    print("3. 点击'设置'按钮批量更新")
    print("\n弹窗特点：")
    print("- 显示文案标题便于确认")
    print("- 当前优先级高亮显示")
    print("- 三个大按钮易于点击")
    print("- 每个按钮有图标和说明文字")

if __name__ == '__main__':
    test_priority_modal()
