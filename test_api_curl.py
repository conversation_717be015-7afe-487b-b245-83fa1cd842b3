#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用requests测试API
"""

import requests
import json

def test_api_with_requests():
    """使用requests测试API"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔄 使用requests测试API...")
    print("=" * 50)
    
    # 1. 测试获取文案
    print("\n1. 测试获取文案...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                print(f"✅ 获取成功！文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                
                # 2. 测试状态更新
                print(f"\n2. 测试状态更新 (ID: {content_id})...")
                
                update_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/test/123',
                    'platform': '小红书',
                    'account': '测试账号'
                }
                
                print(f"发送数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=update_data,
                    timeout=10
                )
                
                print(f"状态更新状态码: {update_response.status_code}")
                print(f"响应内容类型: {update_response.headers.get('Content-Type')}")
                
                if update_response.status_code == 200:
                    try:
                        result = update_response.json()
                        print("✅ 状态更新成功！")
                        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    except json.JSONDecodeError:
                        print("❌ 响应不是有效的JSON")
                        print(f"响应内容: {update_response.text[:200]}...")
                else:
                    print("❌ 状态更新失败！")
                    print(f"响应内容: {update_response.text[:200]}...")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API测试完成！")

if __name__ == '__main__':
    test_api_with_requests()
