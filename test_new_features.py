#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试新功能：智能获取和提示信息
"""

import requests
import json
import time

def test_new_features():
    """测试新功能"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🚀 测试新功能：智能获取和提示信息")
    print("=" * 60)
    
    # 1. 测试智能获取（优先级排序）
    print("\n1. 测试智能获取（优先级排序）...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                priority = content.get('priority')
                priority_display = content.get('priority_display')
                
                print(f"✅ 获取成功！")
                print(f"文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                print(f"优先级: {priority} ({priority_display})")
                print(f"工作流状态: {content.get('workflow_status')}")
                
                # 2. 测试提示信息功能（成功状态）
                print(f"\n2. 测试提示信息功能（成功状态）...")
                
                success_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/discovery/123456',
                    'platform': '小红书',
                    'account': '测试账号@2025',
                    'message': '发布成功！内容已成功发布到小红书平台，获得了很好的曝光效果'
                }
                
                print(f"发送成功提示: {success_data['message']}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=success_data,
                    timeout=10
                )
                
                if update_response.status_code == 200:
                    result = update_response.json()
                    if result.get('success'):
                        print("✅ 成功状态更新成功！")
                    else:
                        print(f"❌ 成功状态更新失败: {result.get('error')}")
                else:
                    print(f"❌ 成功状态更新请求失败: {update_response.status_code}")
                
                # 3. 获取下一篇文案测试失败状态
                print(f"\n3. 获取下一篇文案测试失败状态...")
                
                response2 = requests.get(f'{base_url}/content', headers=headers, timeout=10)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        content2 = data2.get('content')
                        content_id2 = content2.get('id')
                        
                        print(f"✅ 获取下一篇文案成功！")
                        print(f"文案ID: {content_id2}")
                        print(f"标题: {content2.get('title')}")
                        print(f"优先级: {content2.get('priority')} ({content2.get('priority_display')})")
                        
                        # 4. 测试提示信息功能（失败状态）
                        print(f"\n4. 测试提示信息功能（失败状态）...")
                        
                        failed_data = {
                            'status': 'failed',
                            'platform': '小红书',
                            'account': '测试账号@2025',
                            'message': '发布失败：内容审核不通过，建议修改敏感词汇后重新发布'
                        }
                        
                        print(f"发送失败提示: {failed_data['message']}")
                        
                        update_response2 = requests.post(
                            f'{base_url}/content/{content_id2}/status',
                            headers=headers,
                            json=failed_data,
                            timeout=10
                        )
                        
                        if update_response2.status_code == 200:
                            result2 = update_response2.json()
                            if result2.get('success'):
                                print("✅ 失败状态更新成功！")
                            else:
                                print(f"❌ 失败状态更新失败: {result2.get('error')}")
                        else:
                            print(f"❌ 失败状态更新请求失败: {update_response2.status_code}")
                    
                    else:
                        print(f"ℹ️ 没有更多待发布文案: {data2.get('message')}")
                        print("这可能意味着智能获取功能正在检查超时文案...")
                else:
                    print(f"❌ 获取下一篇文案失败: {response2.status_code}")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 新功能测试完成！")
    print("\n功能验证总结:")
    print("1. ✅ 智能获取逻辑（优先级排序）")
    print("2. ✅ 提示信息功能（成功和失败）")
    print("3. ✅ 状态更新API增强")
    print("4. ✅ 超时检查机制（如果有超时文案会自动处理）")
    print("\n请查看发布状态管理页面验证提示信息显示效果！")

if __name__ == '__main__':
    test_new_features()
