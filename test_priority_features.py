#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优先级设置功能
"""

import requests

def test_priority_features():
    """测试优先级设置功能"""
    print("🎯 测试优先级设置功能...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查单个优先级设置功能
            if 'updatePriority' in response.text:
                print("✅ 找到单个优先级更新功能")
            else:
                print("❌ 未找到单个优先级更新功能")
            
            # 检查优先级下拉选择器
            if 'priority-select' in response.text:
                print("✅ 找到优先级选择器")
            else:
                print("❌ 未找到优先级选择器")
            
            # 检查批量优先级设置功能
            if 'batchSetPriority' in response.text:
                print("✅ 找到批量优先级设置功能")
            else:
                print("❌ 未找到批量优先级设置功能")
            
            # 检查批量优先级按钮
            if 'batch-set-priority-btn' in response.text:
                print("✅ 找到批量优先级设置按钮")
            else:
                print("❌ 未找到批量优先级设置按钮")
            
            # 检查批量优先级选择器
            if 'batch-priority-select' in response.text:
                print("✅ 找到批量优先级选择器")
            else:
                print("❌ 未找到批量优先级选择器")
            
            # 检查优先级选项
            priority_options = ['高', '普通', '低']
            found_options = 0
            for option in priority_options:
                if option in response.text:
                    found_options += 1
            
            print(f"✅ 找到 {found_options}/{len(priority_options)} 个优先级选项")
            
            # 检查页面布局
            if 'col-md-8' in response.text and 'col-md-4' in response.text:
                print("✅ 找到响应式布局")
            else:
                print("❌ 未找到响应式布局")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 优先级设置功能测试完成！")
    print("\n新增功能总结：")
    print("1. ✅ 单个优先级设置 - 每行旁边有下拉选择器")
    print("2. ✅ 批量优先级设置 - 批量操作区域新增优先级设置")
    print("3. ✅ 实时更新 - 选择后立即更新，无需刷新页面")
    print("4. ✅ 视觉反馈 - 徽章颜色实时更新")
    print("5. ✅ 响应式布局 - 适配不同屏幕尺寸")
    print("\n使用方法：")
    print("【单个设置】")
    print("1. 在任意文案行的优先级列找到下拉选择器")
    print("2. 选择新的优先级（高/普通/低）")
    print("3. 系统自动保存并更新显示")
    print("\n【批量设置】")
    print("1. 勾选要设置的文案（复选框）")
    print("2. 在右上角选择目标优先级")
    print("3. 点击'设置'按钮批量更新")

if __name__ == '__main__':
    test_priority_features()
