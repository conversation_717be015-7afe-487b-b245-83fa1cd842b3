<!-- 图片上传页面 -->
<style>
.upload-drop-zone {
    transition: all 0.3s ease;
}

.upload-drop-zone:hover {
    border-color: #0d6efd !important;
    background-color: #f8f9fa !important;
}

.upload-drop-zone.border-primary {
    border-color: #0d6efd !important;
    background-color: #e7f1ff !important;
}

/* 极简的上传进度样式 */
#uploadProgressModal .modal-content {
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

#uploadProgressModal .modal-body {
    padding: 1rem;
}

#uploadProgressModal .spinner-border {
    border-width: 2px;
}

/* 简洁的状态文字 */
#uploadProgressModal #uploadStatus {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 确保模态框正确显示 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal.show {
    display: block !important;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.5;
}

.modal-backdrop.show {
    opacity: 0.5;
}

body.modal-open {
    overflow: hidden;
}

.content-preview {
    max-height: 400px;
    overflow-y: auto;
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="bi bi-image"></i> 图片上传</h2>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="" onclick="filterByStatus('')">
                <div class="card-body">
                    <h4 class="text-primary" id="totalCount">{{ content_data|length }}</h4>
                    <small class="text-muted">全部文案</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="pending_upload" onclick="filterByStatus('pending_upload')">
                <div class="card-body">
                    <h4 class="text-success" id="pendingUploadCount">{{ status_counts.pending_upload or 0 }}</h4>
                    <small class="text-muted">图片待上传</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="uploaded" onclick="filterByStatus('uploaded')">
                <div class="card-body">
                    <h4 class="text-info" id="uploadedCount">{{ status_counts.uploaded or 0 }}</h4>
                    <small class="text-muted">图片已上传</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="client_rejected" onclick="filterByStatus('client_rejected')">
                <div class="card-body">
                    <h4 class="text-danger" id="clientRejectedCount">{{ status_counts.client_rejected or 0 }}</h4>
                    <small class="text-muted">客户驳回</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center status-card" data-status="internal_rejected" onclick="filterByStatus('internal_rejected')">
                <div class="card-body">
                    <h4 class="text-warning" id="internalRejectedCount">{{ status_counts.internal_rejected or 0 }}</h4>
                    <small class="text-muted">内部驳回</small>
                </div>
            </div>
        </div>

        <div class="col-md-2">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-secondary" id="totalImagesCount">{{ content_data|map(attribute='image_count')|sum }}</h4>
                    <small class="text-muted">总图片数</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态筛选样式 -->
    <style>
    .status-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .status-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        transform: translateY(-2px);
    }

    .status-card.active {
        border-color: #007bff;
        background-color: #f8f9ff;
        box-shadow: 0 4px 12px rgba(0,123,255,0.2);
    }

    .status-filter-row {
        display: table-row;
    }

    .status-filter-row.hide {
        display: none !important;
    }
    </style>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-3">文案列表</h6>

            <!-- 搜索筛选条件 - 直接展开 -->
            <div class="mt-3" id="searchFilters">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="client_filter" class="form-label">客户</label>
                        <select class="form-select form-select-sm" id="client_filter" name="client_id">
                            <option value="">全部客户 (共{{ clients|length }}个)</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}"
                                    {% if request.args.get('client_id') == client.id|string %}selected{% endif %}>
                                {{ client.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="task_filter" class="form-label">任务</label>
                        <select class="form-select form-select-sm" id="task_filter" name="task_id">
                            <option value="">请先选择客户</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="status_filter" class="form-label">状态</label>
                        <select class="form-select form-select-sm" id="status_filter" name="status">
                            <option value="">全部状态</option>
                            <option value="pending_upload" {% if request.args.get('status') == 'pending_upload' %}selected{% endif %}>
                                图片待上传
                            </option>
                            <option value="uploaded" {% if request.args.get('status') == 'uploaded' %}selected{% endif %}>
                                图片已上传
                            </option>
                            <option value="client_rejected" {% if request.args.get('status') == 'client_rejected' %}selected{% endif %}>
                                客户驳回
                            </option>
                            <option value="internal_rejected" {% if request.args.get('status') == 'internal_rejected' %}selected{% endif %}>
                                内部驳回
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary btn-sm" id="searchBtn" onclick="performSearch(1, 20)">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                            <button type="button" class="btn btn-success btn-sm" id="batchSubmitBtn" disabled>
                                <i class="bi bi-check-circle-fill"></i> 批量提交 (<span id="batchCount">0</span>)
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body">
            {% if content_data %}
                <!-- 表格形式显示文案列表 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 5%;">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th style="width: 5%;">ID</th>
                                <th style="width: 25%;">标题</th>
                                <th style="width: 12%;">客户</th>
                                <th style="width: 12%;">状态</th>
                                <th style="width: 10%;">创建时间</th>
                                <th style="width: 8%;">图片数量</th>
                                <th style="width: 23%;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            {% set status_class = '' %}
                            {% if item.content.client_review_status == 'rejected' %}
                                {% set status_class = 'client_rejected' %}
                            {% elif (item.content.internal_review_status == 'rejected' or (item.content.internal_review_status and item.content.internal_review_status.startswith('final_rej'))) and item.content.image_completed != 1 %}
                                {% set status_class = 'internal_rejected' %}
                            {% elif item.content.workflow_status == 'pending_review' %}
                                {% set status_class = 'pending_review' %}
                            {% elif item.content.workflow_status == 'first_reviewed' %}
                                {% set status_class = 'pending_upload' %}
                            {% elif item.content.workflow_status == 'image_uploaded' %}
                                {% set status_class = 'uploaded' %}
                            {% endif %}
                            <tr data-content-id="{{ item.content.id }}" data-status="{{ status_class }}" class="status-filter-row">
                                <!-- 复选框 -->
                                <td>
                                    <input type="checkbox"
                                           class="form-check-input content-checkbox"
                                           data-content-id="{{ item.content.id }}"
                                           data-image-count="{{ item.image_count }}">
                                </td>
                                <!-- 文案ID -->
                                <td>{{ item.content.id }}</td>

                                <!-- 文案标题 -->
                                <td>
                                    <div class="fw-bold">{{ item.content.title }}</div>
                                </td>

                                <!-- 客户信息 -->
                                <td>
                                    <span class="badge bg-info">{{ item.content.client.name if item.content.client else '未知客户' }}</span>
                                </td>

                                <!-- 状态信息 -->
                                <td>
                                    {% if item.content.client_review_status == 'rejected' %}
                                        <span class="badge bg-danger">客户驳回</span>
                                    {% elif item.content.internal_review_status == 'rejected' %}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif item.content.internal_review_status == 'final_rej_both' %}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif item.content.internal_review_status == 'final_rej_img' %}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif item.content.internal_review_status == 'final_rej_text' %}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif item.content.internal_review_status == 'final_rej_both_img' %}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif item.content.workflow_status == 'pending_review' %}
                                        <span class="badge bg-info">待初审</span>
                                    {% elif item.content.workflow_status == 'first_reviewed' %}
                                        <span class="badge bg-success">待上传图片</span>
                                    {% elif item.content.workflow_status == 'image_uploaded' %}
                                        <span class="badge bg-primary">图片已上传</span>
                                    {% elif item.content.workflow_status == 'draft' %}
                                        <span class="badge bg-secondary">草稿</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ item.content.workflow_status }}</span>
                                    {% endif %}
                                </td>

                                <!-- 创建时间 -->
                                <td>
                                    <small>{{ item.content.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>

                                <!-- 图片数量 -->
                                <td>
                                    <span class="badge bg-{% if item.image_count > 0 %}success{% else %}secondary{% endif %}"
                                          data-content-id="{{ item.content.id }}">
                                        {{ item.image_count }}张图片
                                    </span>
                                </td>

                                <!-- 操作按钮 -->
                                <td>
                                    <button type="button"
                                            class="btn btn-primary btn-sm"
                                            onclick="openImageUploadModal({{ item.content.id }}, '{{ item.content.title|replace("'", "\\'") }}')">
                                        <i class="bi bi-images"></i> 管理图片
                                    </button>

                                    <button type="button"
                                            class="btn {% if item.image_count > 0 %}btn-success{% else %}btn-secondary{% endif %} btn-sm"
                                            onclick="submitContent({{ item.content.id }})"
                                            {% if item.image_count == 0 %}disabled title="请先上传图片"{% endif %}>
                                        <i class="bi bi-check-circle"></i> 提交
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                {% if pagination and pagination.pages > 1 %}
                    {% include 'components/pagination.html' %}
                {% elif pagination %}
                    <!-- 即使只有一页也显示分页信息 -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="d-flex align-items-center">
                            <span class="me-2">每页显示：</span>
                            <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value, event)">
                                <option value="10" {% if per_page == 10 %}selected{% endif %}>10条</option>
                                <option value="20" {% if per_page == 20 %}selected{% endif %}>20条</option>
                                <option value="30" {% if per_page == 30 %}selected{% endif %}>30条</option>
                                <option value="50" {% if per_page == 50 %}selected{% endif %}>50条</option>
                                <option value="80" {% if per_page == 80 %}selected{% endif %}>80条</option>
                                <option value="100" {% if per_page == 100 %}selected{% endif %}>100条</option>
                            </select>
                            <span class="ms-3 text-muted">
                                共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.pages }} 页
                            </span>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无待上传图片的文案</h5>
                    <p class="text-muted">请先完成文案的初审流程</p>
                    <button type="button" class="btn btn-outline-primary" onclick="window.location.href='/simple/content-review'">
                        <i class="bi bi-arrow-left"></i> 去初审文案
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 图片管理模态框 -->
<div class="modal fade" id="imageUploadModal" tabindex="-1" aria-labelledby="imageUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" id="uploadDropZone"
             ondrop="handleDrop(event)"
             ondragover="handleDragOver(event)"
             ondragleave="handleDragLeave(event)"
             style="position: relative;">

            <!-- 拖拽提示覆盖层 -->
            <div id="dragOverlay" class="position-absolute top-0 start-0 w-100 h-100 d-none"
                 style="background: rgba(13, 110, 253, 0.1); border: 3px dashed #0d6efd; z-index: 1050; border-radius: 0.375rem;">
                <div class="d-flex flex-column align-items-center justify-content-center h-100">
                    <i class="bi bi-cloud-upload fs-1 text-primary mb-2"></i>
                    <h4 class="text-primary">释放文件以上传图片</h4>
                    <p class="text-muted" id="dragOverlayText">支持多选，最多上传9张图片</p>
                </div>
            </div>

            <div class="modal-header">
                <h5 class="modal-title" id="imageUploadModalLabel">图片管理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 隐藏的文件输入框 -->
                <input type="file"
                       class="d-none"
                       id="modalFileInput"
                       accept="image/*"
                       multiple
                       onchange="handleFileSelect(event)">

                <div class="row">
                    <!-- 左侧：文案信息 -->
                    <div class="col-md-5">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="card-title mb-0"><i class="bi bi-file-text"></i> 文案信息</h6>
                            </div>
                            <div class="card-body">
                                <div id="contentPreview">
                                    <!-- 文案信息将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：图片管理 -->
                    <div class="col-md-7">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0"><i class="bi bi-images"></i> 图片管理</h6>
                                <span id="imageCount" class="badge bg-secondary">0/9</span>
                            </div>
                            <div class="card-body">
                                <!-- 已上传图片展示区域 -->
                                <div id="uploadedImages">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="small mb-0">已上传图片：</h6>
                                        <small class="text-muted">
                                            <i class="bi bi-arrows-move"></i> 拖拽图片可调整顺序
                                        </small>
                                    </div>
                                    <div id="imageGrid" class="row g-2">
                                        <!-- 图片将通过JavaScript动态加载 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="triggerFileSelect()">
                    <i class="bi bi-plus"></i> 选择图片
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 极简的上传进度提示 -->
<div class="modal fade" id="uploadProgressModal" tabindex="-1">
    <div class="modal-dialog" style="max-width: 280px;">
        <div class="modal-content border-0 shadow">
            <div class="modal-body text-center py-3">
                <div class="spinner-border text-primary mb-2" style="width: 1.5rem; height: 1.5rem;" role="status">
                    <span class="visually-hidden">上传中...</span>
                </div>
                <div id="uploadStatus" class="fw-bold text-muted small">准备上传...</div>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量，用于跟踪是否正在进行图片排序拖拽
let isImageSorting = false;

// 全局变量，用于防止重复关闭上传进度模态框
let isUploadModalClosing = false;

// 安全关闭上传进度模态框的函数
function safeCloseUploadModal() {
    if (isUploadModalClosing) {
        console.log('上传模态框正在关闭中，跳过重复操作');
        return;
    }

    isUploadModalClosing = true;

    try {
        const progressModal = document.getElementById('uploadProgressModal');
        if (progressModal && progressModal.style) {
            progressModal.classList.remove('show');
            progressModal.style.display = 'none';
            document.body.classList.remove('modal-open');
            console.log('上传进度模态框已关闭');
        }

        // 只移除上传进度的背景遮罩，保留图片管理模态框的遮罩
        const uploadProgressBackdrop = document.querySelector('.modal-backdrop[data-upload-progress="true"]');
        if (uploadProgressBackdrop) {
            uploadProgressBackdrop.remove();
            console.log('上传进度模态框背景已移除');
        } else {
            console.log('没有找到上传进度的遮罩，可能图片管理模态框的遮罩仍在使用中');
        }
    } catch (error) {
        console.error('关闭上传进度模态框时出错:', error);
    } finally {
        // 延迟重置标志，避免过快的重复操作
        setTimeout(() => {
            isUploadModalClosing = false;
        }, 500);
    }
}

// 全选/取消全选功能
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const contentCheckboxes = document.querySelectorAll('.content-checkbox');

    console.log('=== 全选功能触发 ===');
    console.log('全选状态:', selectAllCheckbox.checked);
    console.log('找到的复选框数量:', contentCheckboxes.length);

    let validCount = 0;
    let selectedCount = 0;

    contentCheckboxes.forEach((checkbox, index) => {
        // 只选择有图片的文案（可以提交的）
        const imageCount = parseInt(checkbox.getAttribute('data-image-count')) || 0;
        console.log(`复选框 ${index}: contentId=${checkbox.getAttribute('data-content-id')}, imageCount=${imageCount}`);

        if (imageCount > 0) {
            validCount++;
            checkbox.checked = selectAllCheckbox.checked;
            if (checkbox.checked) {
                selectedCount++;
            }
            console.log(`  -> 有图片，设置为: ${checkbox.checked}`);
        } else {
            // 没有图片的文案，确保不被选中
            checkbox.checked = false;
            console.log(`  -> 无图片，保持未选中`);
        }
    });

    console.log(`有效文案数量: ${validCount}, 选中数量: ${selectedCount}`);
    updateBatchSubmitButton();
}

// 更新批量提交按钮状态
function updateBatchSubmitButton() {
    console.log('=== 更新批量提交按钮状态 ===');

    const allContentCheckboxes = document.querySelectorAll('.content-checkbox');
    const checkedContentCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const batchSubmitBtn = document.getElementById('batchSubmitBtn');
    const batchCount = document.getElementById('batchCount');

    console.log('所有复选框数量:', allContentCheckboxes.length);
    console.log('选中复选框数量:', checkedContentCheckboxes.length);

    // 只计算有图片的选中项
    let validCount = 0;
    checkedContentCheckboxes.forEach((checkbox, index) => {
        const imageCount = parseInt(checkbox.getAttribute('data-image-count')) || 0;
        const contentId = checkbox.getAttribute('data-content-id');
        console.log(`选中项 ${index}: contentId=${contentId}, imageCount=${imageCount}`);

        if (imageCount > 0) {
            validCount++;
            console.log(`  -> 有效选中项`);
        } else {
            console.log(`  -> 无效选中项（无图片）`);
        }
    });

    console.log('有效选中数量:', validCount);

    if (!batchSubmitBtn || !batchCount) {
        console.warn('批量提交按钮或计数元素未找到，可能正在DOM更新中，跳过此次更新');
        // 延迟重试一次
        setTimeout(() => {
            const retryBtn = document.getElementById('batchSubmitBtn');
            const retryCount = document.getElementById('batchCount');
            if (retryBtn && retryCount) {
                console.log('重试更新批量提交按钮状态');
                // 简化的状态更新
                if (validCount > 0) {
                    retryBtn.disabled = false;
                    retryBtn.classList.remove('btn-secondary');
                    retryBtn.classList.add('btn-success');
                } else {
                    retryBtn.disabled = true;
                    retryBtn.classList.remove('btn-success');
                    retryBtn.classList.add('btn-secondary');
                }
                retryCount.textContent = validCount;
            }
        }, 100);
        return;
    }

    // 更新按钮状态
    if (validCount > 0) {
        batchSubmitBtn.disabled = false;
        batchSubmitBtn.classList.remove('btn-secondary');
        batchSubmitBtn.classList.add('btn-success');
        console.log('✅ 批量提交按钮已激活');
    } else {
        batchSubmitBtn.disabled = true;
        batchSubmitBtn.classList.remove('btn-success');
        batchSubmitBtn.classList.add('btn-secondary');
        console.log('❌ 批量提交按钮已禁用');
    }

    // 更新计数显示
    batchCount.textContent = validCount;
    console.log('更新计数显示:', validCount);

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        // 找到所有有图片的复选框
        const allValidCheckboxes = [];
        const checkedValidCheckboxes = [];

        allContentCheckboxes.forEach(checkbox => {
            const imageCount = parseInt(checkbox.getAttribute('data-image-count')) || 0;
            if (imageCount > 0) {
                allValidCheckboxes.push(checkbox);
                if (checkbox.checked) {
                    checkedValidCheckboxes.push(checkbox);
                }
            }
        });

        console.log('有图片的复选框总数:', allValidCheckboxes.length);
        console.log('有图片且选中的复选框数:', checkedValidCheckboxes.length);

        if (checkedValidCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
            console.log('全选状态: 未选中');
        } else if (checkedValidCheckboxes.length === allValidCheckboxes.length && allValidCheckboxes.length > 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
            console.log('全选状态: 全选');
        } else {
            selectAllCheckbox.indeterminate = true;
            console.log('全选状态: 部分选中');
        }
    }

    console.log('=== 批量提交按钮状态更新完成 ===');
}

// 批量提交功能
function batchSubmitContents() {
    // 防止重复执行
    if (window.isBatchSubmitting) {
        console.log('批量提交正在进行中，忽略重复调用');
        return;
    }

    const checkedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const contentIds = [];

    // 收集有图片的选中文案ID
    checkedCheckboxes.forEach(checkbox => {
        const imageCount = parseInt(checkbox.getAttribute('data-image-count')) || 0;
        if (imageCount > 0) {
            contentIds.push(checkbox.getAttribute('data-content-id'));
        }
    });

    if (contentIds.length === 0) {
        alert('请选择要提交的文案');
        return;
    }

    if (!confirm(`确定要批量提交这 ${contentIds.length} 篇文案到最终审核吗？提交后将无法再修改图片。`)) {
        return;
    }

    // 设置提交状态标志
    window.isBatchSubmitting = true;

    console.log('开始批量提交文案:', contentIds);

    // 禁用批量提交按钮
    const batchSubmitBtn = document.getElementById('batchSubmitBtn');
    const originalText = batchSubmitBtn.innerHTML;
    batchSubmitBtn.disabled = true;
    batchSubmitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 提交中...';

    // 逐个提交文案
    let successCount = 0;
    let failCount = 0;
    const totalCount = contentIds.length;

    const submitPromises = contentIds.map(contentId => {
        return fetch(`/simple/api/contents/${contentId}/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                successCount++;
                console.log(`文案 ${contentId} 提交成功`);

                // 移除成功提交的行
                const row = document.querySelector(`tr[data-content-id="${contentId}"]`);
                if (row) {
                    row.style.transition = 'opacity 0.5s ease-out';
                    row.style.opacity = '0.5';
                    setTimeout(() => {
                        row.remove();
                        // 延迟更新，避免在DOM变更过程中更新状态
                        setTimeout(() => {
                            updateBatchSubmitButton();
                        }, 50);
                    }, 500);
                }
            } else {
                failCount++;
                console.error(`文案 ${contentId} 提交失败:`, data.message);
            }
        })
        .catch(error => {
            failCount++;
            console.error(`文案 ${contentId} 提交失败:`, error);
        });
    });

    // 等待所有提交完成
    Promise.all(submitPromises).then(() => {
        // 恢复按钮状态
        batchSubmitBtn.innerHTML = originalText;
        updateBatchSubmitButton();

        // 显示结果
        if (successCount > 0 && failCount === 0) {
            showToast(`批量提交成功！共提交 ${successCount} 篇文案`, 'success');
        } else if (successCount > 0 && failCount > 0) {
            showToast(`部分提交成功：成功 ${successCount} 篇，失败 ${failCount} 篇`, 'warning');
        } else {
            showToast(`批量提交失败：${failCount} 篇文案提交失败`, 'error');
        }

        // 更新统计数据
        updateStatistics();

        console.log(`批量提交完成：成功 ${successCount} 篇，失败 ${failCount} 篇`);

        // 重置提交状态标志
        window.isBatchSubmitting = false;
    });
}
// 当前正在管理图片的文案ID（使用window对象避免重复声明）
if (typeof window.currentContentId === 'undefined') {
    window.currentContentId = null;
}
// 上传状态标志，防止在上传过程中关闭模态框
if (typeof window.isUploading === 'undefined') {
    window.isUploading = false;
}
// 为了兼容现有代码，创建一个局部引用（避免重复声明）
if (typeof currentContentId === 'undefined') {
    var currentContentId = window.currentContentId;
}

// 当前筛选状态
let currentFilterStatus = '';

// 图片限制设置
let maxImagesPerContent = 9; // 默认值

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取图片限制设置
    loadImageLimits();

    updateStatusCounts();
    showAllRows(); // 默认显示所有行

    // 初始化侧边栏关闭按钮
    const closeSidebarBtn = document.getElementById('closeSidebar');
    if (closeSidebarBtn) {
        closeSidebarBtn.addEventListener('click', hideRejectionSidebar);
    }

    // 初始化侧边栏拖拽功能
    initSidebarDrag();
});

// 获取图片限制设置
function loadImageLimits() {
    fetch('/simple/api/system/image-limits')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                maxImagesPerContent = data.limits.max_images;
                console.log('获取到图片数量限制:', maxImagesPerContent);

                // 更新页面上的显示
                updateImageLimitDisplay();
            } else {
                console.error('获取图片限制设置失败:', data.message);
            }
        })
        .catch(error => {
            console.error('获取图片限制设置失败:', error);
        });
}

// 更新页面上的图片限制显示
function updateImageLimitDisplay() {
    // 更新拖拽提示文字
    const dragOverlayText = document.getElementById('dragOverlayText');
    if (dragOverlayText) {
        dragOverlayText.textContent = `支持多选，最多上传${maxImagesPerContent}张图片`;
    }

    // 更新所有的图片计数显示
    const imageCountElements = document.querySelectorAll('#imageCount, span.badge[data-content-id]');
    imageCountElements.forEach(element => {
        const currentText = element.textContent;
        const match = currentText.match(/(\d+)\/\d+/);
        if (match) {
            const currentCount = match[1];
            element.textContent = `${currentCount}/${maxImagesPerContent}`;
        } else if (currentText === '0/9') {
            element.textContent = `0/${maxImagesPerContent}`;
        }
    });

    console.log('已更新页面上的图片限制显示');
}

// 更新状态统计
function updateStatusCounts() {
    const rows = document.querySelectorAll('.status-filter-row');
    let totalCount = 0;
    let pendingUploadCount = 0;
    let uploadedCount = 0;
    let clientRejectedCount = 0;
    let internalRejectedCount = 0;

    rows.forEach(row => {
        const status = row.dataset.status;
        totalCount++;

        switch(status) {
            case 'pending_upload':
                pendingUploadCount++;
                break;
            case 'uploaded':
                uploadedCount++;
                break;
            case 'client_rejected':
                clientRejectedCount++;
                break;
            case 'internal_rejected':
                internalRejectedCount++;
                break;
        }
    });

    // 更新统计数字
    document.getElementById('totalCount').textContent = totalCount;
    document.getElementById('pendingUploadCount').textContent = pendingUploadCount;
    document.getElementById('uploadedCount').textContent = uploadedCount;
    document.getElementById('clientRejectedCount').textContent = clientRejectedCount;
    document.getElementById('internalRejectedCount').textContent = internalRejectedCount;
}

// 按状态筛选
function filterByStatus(status) {
    currentFilterStatus = status;
    const rows = document.querySelectorAll('.status-filter-row');
    const statusCards = document.querySelectorAll('.status-card');

    // 更新卡片样式
    statusCards.forEach(card => {
        if (card.dataset.status === status) {
            card.classList.add('active');
        } else {
            card.classList.remove('active');
        }
    });

    // 筛选行
    if (status === '') {
        // 显示所有行
        showAllRows();
    } else {
        // 只显示指定状态的行
        rows.forEach(row => {
            if (row.dataset.status === status) {
                row.classList.remove('hide');
            } else {
                row.classList.add('hide');
            }
        });
    }

    console.log(`筛选状态: ${status || '全部'}`);
}

// 显示所有行
function showAllRows() {
    const rows = document.querySelectorAll('.status-filter-row');
    rows.forEach(row => {
        row.classList.remove('hide');
    });
}

// 打开图片上传模态框
function openImageUploadModal(contentId, title) {
    console.log('=== 打开图片管理模态框 ===');
    console.log('传入的文案ID:', contentId, '标题:', title);
    console.log('contentId类型:', typeof contentId);

    // 设置全局变量，记录当前正在编辑的文案
    window.currentContentId = contentId;
    currentContentId = window.currentContentId;
    console.log('✅ 设置当前文案ID:', currentContentId);
    console.log('window.currentContentId:', window.currentContentId);
    console.log('局部currentContentId:', currentContentId);

    // 设置模态框标题（限制长度，避免换行）
    const maxTitleLength = 20;
    const truncatedTitle = title.length > maxTitleLength ? title.substring(0, maxTitleLength) + '...' : title;
    const modalLabel = document.getElementById('imageUploadModalLabel');
    if (modalLabel) {
        modalLabel.textContent = `图片管理 - ${truncatedTitle}`;
    }

    // 加载文案信息
    loadContentPreview(contentId);

    // 加载已上传的图片
    loadUploadedImages(contentId);

    // 显示模态框
    const modalElement = document.getElementById('imageUploadModal');
    console.log('模态框元素:', modalElement);

    if (modalElement) {
        console.log('Bootstrap对象:', typeof bootstrap, bootstrap);

        try {
            const modal = new bootstrap.Modal(modalElement);
            console.log('模态框实例创建成功:', modal);

            // 移除之前的事件监听器（避免重复绑定）
            modalElement.removeEventListener('hidden.bs.modal', handleModalClose);

            // 添加弹窗关闭事件监听器
            modalElement.addEventListener('hidden.bs.modal', handleModalClose);

            modal.show();
            console.log('✅ 图片管理模态框已显示');

            // 检查遮罩状态
            setTimeout(() => {
                const backdrop = document.querySelector('.modal-backdrop');
                console.log('图片管理模态框打开后，遮罩状态:', backdrop ? '存在' : '不存在');
            }, 100);
        } catch (error) {
            console.error('❌ 创建或显示模态框时出错:', error);
        }
    } else {
        console.error('❌ 找不到图片上传模态框元素');
    }
}

// 处理模态框关闭事件
function handleModalClose() {
    console.log('=== 图片管理模态框关闭事件触发 ===');
    console.log('⚠️ 模态框被关闭了！这可能不是预期的行为');
    console.log('调用堆栈:', new Error().stack);

    // 关闭驳回理由侧边栏
    hideRejectionSidebar();

    if (currentContentId) {
        const contentId = currentContentId;
        console.log('正在处理的文案ID:', contentId);

        // 延迟一点时间确保所有上传操作都完成
        setTimeout(() => {
            console.log('开始最终状态更新...');

            // 获取最新的图片数量并更新显示和按钮状态
            getCurrentImageCount(contentId).then(count => {
                console.log('模态框关闭时获取到最新图片数量:', count);

                // 强制更新页面上的图片数量显示
                console.log('强制更新图片数量显示...');
                updateImageCountDisplay(contentId, count);

                // 强制更新按钮状态
                console.log('强制更新提交按钮状态...');
                updateSubmitButtonState(contentId, count);

                // 更新总图片数量统计
                updateTotalImageCount();

                console.log('✅ 模态框关闭后所有状态更新完成');

                // 重要：不要调用performSearch()，避免覆盖我们的更新
                console.log('⚠️ 跳过自动搜索，保持当前更新的显示');

            }).catch(error => {
                console.error('获取图片数量失败:', error);
            });
        }, 500); // 延迟500ms确保上传完成

        // 重置当前内容ID
        window.currentContentId = null;
        currentContentId = window.currentContentId;
    } else {
        console.log('❌ 没有当前内容ID，跳过更新');
    }
}

// 加载文案预览信息
function loadContentPreview(contentId) {
    fetch(`/simple/api/contents/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = data.content;

                // 生成话题标签
                let topicsHtml = '';
                if (content.topics && content.topics.length > 0) {
                    topicsHtml = content.topics.map(topic =>
                        `<span class="badge bg-primary me-1 mb-1">#${topic}</span>`
                    ).join('');
                } else {
                    topicsHtml = '<span class="text-muted">无</span>';
                }

                // 生成@用户标签
                let atUsersHtml = '';
                if (content.at_users && content.at_users.length > 0) {
                    atUsersHtml = content.at_users.map(user =>
                        `<span class="badge bg-warning text-dark me-1 mb-1">@${user.replace('@', '')}</span>`
                    ).join('');
                } else {
                    atUsersHtml = '<span class="text-muted">无</span>';
                }

                // 生成定位信息
                const locationHtml = content.location ?
                    `<span class="badge bg-success me-1 mb-1"><i class="bi bi-geo-alt"></i> ${content.location}</span>` :
                    '<span class="text-muted">无</span>';

                // 状态映射和显示逻辑
                let statusText, statusClass;

                if (content.client_review_status === 'rejected') {
                    statusText = '客户驳回';
                    statusClass = 'bg-danger';
                } else if (content.internal_review_status === 'rejected') {
                    statusText = '内部驳回';
                    statusClass = 'bg-warning';
                } else if (content.internal_review_status && content.internal_review_status.includes('final_rej')) {
                    // 处理新的终审驳回状态
                    if (content.internal_review_status === 'final_rej_text') {
                        statusText = '内部驳回（文案问题）';
                    } else if (content.internal_review_status === 'final_rej_img') {
                        statusText = '内部驳回（图片问题）';
                    } else if (content.internal_review_status === 'final_rej_both') {
                        statusText = '内部驳回（两者都有问题）';
                    } else if (content.internal_review_status === 'final_rej_both_txt') {
                        statusText = '文案已编辑，待上传图片';
                    } else {
                        statusText = '内部驳回';
                    }
                    statusClass = 'bg-danger';
                } else if (content.workflow_status === 'pending_review') {
                    statusText = '待初审';
                    statusClass = 'bg-info';
                } else if (content.workflow_status === 'first_reviewed') {
                    // 根据内部审核状态显示更精确的状态信息
                    if (content.internal_review_status === 'client_rej_img') {
                        statusText = '客户驳回-需重新上传图片';
                        statusClass = 'bg-warning';
                    } else if (content.internal_review_status === 'final_rej_img') {
                        statusText = '终审驳回-需重新上传图片';
                        statusClass = 'bg-warning';
                    } else {
                        statusText = '待上传图片';
                        statusClass = 'bg-success';
                    }
                } else if (content.workflow_status === 'image_uploaded') {
                    statusText = '图片已上传';
                    statusClass = 'bg-primary';
                } else if (content.workflow_status === 'final_review') {
                    statusText = '待最终审核';
                    statusClass = 'bg-warning';
                } else {
                    // 其他状态的映射
                    const statusMap = {
                        'draft': '草稿',
                        'pending_final_review': '待最终审核',
                        'pending_client_review': '待客户审核',
                        'client_approved': '客户已通过',
                        'pending_publish': '待发布',
                        'published': '已发布'
                    };
                    statusText = statusMap[content.workflow_status] || content.workflow_status;
                    statusClass = 'bg-secondary';
                }

                // 先设置基本的文案信息，驳回理由将通过loadRejectionReasonsInline函数添加
                document.getElementById('contentPreview').innerHTML = `
                    <div class="content-preview">
                        <!-- 驳回理由将在这里动态插入 -->
                        <div id="rejectionReasonsContainer"></div>

                        <h6 class="mb-3 text-truncate" title="${content.title}">${content.title}</h6>

                        <div class="content-text mb-3">
                            <p class="text-muted small mb-0">${content.content.substring(0, 200)}${content.content.length > 200 ? '...' : ''}</p>
                        </div>

                        <div class="content-meta">
                            <div class="row g-2">
                                <div class="col-12">
                                    <small class="text-muted d-block mb-1">话题：</small>
                                    <div>${topicsHtml}</div>
                                </div>
                                <div class="col-12">
                                    <small class="text-muted d-block mb-1">@用户：</small>
                                    <div>${atUsersHtml}</div>
                                </div>
                                <div class="col-12">
                                    <small class="text-muted d-block mb-1">定位：</small>
                                    <div>${locationHtml}</div>
                                </div>
                                <div class="col-12 mt-3">
                                    <span class="badge bg-info me-1">${content.client_name || '未知客户'}</span>
                                    <span class="badge ${statusClass}">${statusText}</span>
                                    <span class="badge bg-light text-dark">${content.created_at || ''}</span>
                                </div>
                                ${content.internal_review_status === 'client_rej_img' ? `
                                <div class="col-12 mt-2">
                                    <div class="alert alert-warning alert-sm py-2 mb-0">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        <strong>客户驳回：</strong>文案已审核通过，请重新上传图片后点击"提交图片审核"按钮
                                    </div>
                                </div>
                                ` : ''}
                                ${content.internal_review_status === 'final_rej_img' ? `
                                <div class="col-12 mt-2">
                                    <div class="alert alert-warning alert-sm py-2 mb-0">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        <strong>终审驳回：</strong>文案已审核通过，请重新上传图片后点击"提交图片审核"按钮
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;

                // 加载驳回理由并显示在顶部
                loadRejectionReasonsInline(contentId, content);

                // 加载驳回理由（如果存在）- 保持原有的侧边栏功能
                loadRejectionReasons(contentId, content);
            }
        })
        .catch(error => {
            console.error('加载文案信息失败:', error);
            document.getElementById('contentPreview').innerHTML = '<p class="text-danger">加载失败</p>';
        });
}

// 在文案信息顶部显示驳回理由
function loadRejectionReasonsInline(contentId, content) {
    console.log('=== 加载内联驳回理由 ===');
    console.log('文案ID:', contentId);
    console.log('文案信息:', content);

    // 检查是否有驳回状态
    const hasClientRejection = content.client_review_status === 'rejected';
    const hasInternalRejection = content.internal_review_status && (
        content.internal_review_status === 'rejected' ||
        content.internal_review_status.includes('final_rej')
    );

    console.log('客户驳回状态:', hasClientRejection);
    console.log('内部驳回状态:', hasInternalRejection);
    console.log('内部审核状态值:', content.internal_review_status);

    if (!hasClientRejection && !hasInternalRejection) {
        // 没有驳回，不显示驳回理由
        console.log('没有驳回状态，不显示驳回理由');
        document.getElementById('rejectionReasonsContainer').innerHTML = '';
        return;
    }

    console.log('检测到驳回状态，开始加载驳回理由');

    // 加载驳回理由
    fetch(`/simple/api/rejection-reasons/${contentId}`)
        .then(response => response.json())
        .then(data => {
            console.log('驳回理由API响应:', data);

            if (data.success && data.reasons && data.reasons.length > 0) {
                // 按时间倒序排列，显示最新的驳回理由
                const sortedReasons = data.reasons.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                const latestReason = sortedReasons[0];

                console.log('最新驳回理由:', latestReason);

                // 确定驳回类型和样式
                let rejectionTypeText = '';
                let rejectionTypeClass = 'bg-danger';

                if (latestReason.rejection_type === 'content') {
                    rejectionTypeText = '文案问题';
                    rejectionTypeClass = 'bg-warning';
                } else if (latestReason.rejection_type === 'image') {
                    rejectionTypeText = '图片问题';
                    rejectionTypeClass = 'bg-info';
                } else if (latestReason.rejection_type === 'both') {
                    rejectionTypeText = '文案+图片问题';
                    rejectionTypeClass = 'bg-danger';
                } else {
                    rejectionTypeText = '驳回';
                    rejectionTypeClass = 'bg-secondary';
                }

                // 格式化时间
                const rejectionTime = new Date(latestReason.created_at).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // 显示驳回理由
                document.getElementById('rejectionReasonsContainer').innerHTML = `
                    <div class="alert alert-warning border-start border-warning border-4 mb-3" style="background-color: #fff3cd;">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-exclamation-triangle-fill text-warning me-2 mt-1"></i>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0 text-warning">
                                        <i class="bi bi-arrow-return-left me-1"></i>驳回理由
                                    </h6>
                                    <div>
                                        <span class="badge ${rejectionTypeClass} me-1">${rejectionTypeText}</span>
                                        <small class="text-muted">${rejectionTime}</small>
                                    </div>
                                </div>
                                <p class="mb-0 text-dark">${latestReason.reason}</p>
                                ${latestReason.reviewer_name ? `<small class="text-muted">审核人：${latestReason.reviewer_name}</small>` : ''}
                            </div>
                        </div>
                    </div>
                `;

                console.log('驳回理由显示完成');
            } else {
                console.log('没有找到驳回理由数据');
                document.getElementById('rejectionReasonsContainer').innerHTML = '';
            }
        })
        .catch(error => {
            console.error('加载驳回理由失败:', error);
            document.getElementById('rejectionReasonsContainer').innerHTML = '';
        });
}

// 加载驳回理由
function loadRejectionReasons(contentId, content) {
    console.log('=== 加载驳回理由 ===');
    console.log('文案ID:', contentId);
    console.log('文案信息:', content);

    // 检查是否有驳回状态
    const hasClientRejection = content.client_review_status === 'rejected';
    const hasInternalRejection = content.internal_review_status === 'rejected';

    console.log('客户驳回状态:', hasClientRejection);
    console.log('内部驳回状态:', hasInternalRejection);

    if (!hasClientRejection && !hasInternalRejection) {
        // 没有驳回，隐藏侧边栏
        console.log('没有驳回状态，隐藏侧边栏');
        hideRejectionSidebar();
        return;
    }

    console.log('检测到驳回状态，开始加载驳回理由');

    // 加载驳回理由
    fetch(`/simple/api/rejection-reasons/${contentId}`)
        .then(response => {
            console.log('驳回理由API响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('驳回理由API返回数据:', data);
            if (data.success && data.reasons && data.reasons.length > 0) {
                console.log('找到驳回理由，数量:', data.reasons.length);
                displayRejectionReasons(data.reasons, hasClientRejection, hasInternalRejection);
            } else {
                // 有驳回状态但没有具体理由
                console.log('有驳回状态但没有具体理由');
                displayRejectionReasons([], hasClientRejection, hasInternalRejection);
            }
        })
        .catch(error => {
            console.error('加载驳回理由失败:', error);
            // 显示基本的驳回状态
            displayRejectionReasons([], hasClientRejection, hasInternalRejection);
        });
}

// 显示驳回理由
function displayRejectionReasons(reasons, hasClientRejection, hasInternalRejection) {
    console.log('=== 显示驳回理由 ===');
    console.log('驳回理由数量:', reasons.length);
    console.log('客户驳回:', hasClientRejection);
    console.log('内部驳回:', hasInternalRejection);
    console.log('驳回理由详情:', reasons);

    // 显示侧边栏
    showRejectionSidebar(reasons, hasClientRejection, hasInternalRejection);
}

// 显示驳回理由侧边栏
function showRejectionSidebar(reasons, hasClientRejection, hasInternalRejection) {
    console.log('=== 显示驳回理由侧边栏 ===');

    const sidebar = document.getElementById('rejectionSidebar');
    const titleElement = document.getElementById('rejectionSidebarTitle');
    const contentElement = document.getElementById('rejectionSidebarContent');

    if (!sidebar || !titleElement || !contentElement) {
        console.error('侧边栏元素未找到');
        return;
    }

    // 确定标题
    let title;
    if (hasClientRejection && hasInternalRejection) {
        title = '客户和内部都已驳回';
    } else if (hasClientRejection) {
        title = '客户已驳回';
    } else {
        title = '内部已驳回';
    }

    titleElement.textContent = title;

    // 生成理由内容
    let contentHtml = '';
    if (reasons.length > 0) {
        // 按类型分组显示理由
        const clientReasons = reasons.filter(r => r.is_client);
        const internalReasons = reasons.filter(r => !r.is_client);

        if (clientReasons.length > 0) {
            clientReasons.forEach(reason => {
                const typeText = reason.rejection_type ? getTypeDisplayText(reason.rejection_type) : '';
                const typeTag = typeText ? `<div class="rejection-type-badge">${typeText}</div>` : '';
                contentHtml += `
                    <div class="rejection-item client-rejection">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <small><i class="bi bi-person-fill me-1"></i>客户驳回</small>
                            <div class="rejection-time">${formatDateTime(reason.created_at)}</div>
                        </div>
                        ${typeTag}
                        <div class="rejection-reason">${reason.reason}</div>
                    </div>
                `;
            });
        }

        if (internalReasons.length > 0) {
            internalReasons.forEach(reason => {
                const typeText = reason.rejection_type ? getTypeDisplayText(reason.rejection_type) : '';
                const typeTag = typeText ? `<div class="rejection-type-badge">${typeText}</div>` : '';
                contentHtml += `
                    <div class="rejection-item internal-rejection">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <small><i class="bi bi-building me-1"></i>内部驳回</small>
                            <div class="rejection-time">${formatDateTime(reason.created_at)}</div>
                        </div>
                        ${typeTag}
                        <div class="rejection-reason">${reason.reason}</div>
                    </div>
                `;
            });
        }
    } else {
        contentHtml = `
            <div class="rejection-item">
                <div class="rejection-reason">暂无具体驳回理由</div>
            </div>
        `;
    }

    contentElement.innerHTML = contentHtml;

    // 显示侧边栏
    sidebar.classList.remove('d-none');

    // 如果侧边栏之前被拖拽过，保持其位置；否则使用默认位置
    if (!sidebar.style.left && !sidebar.style.top) {
        // 重置为默认位置和变换
        sidebar.style.left = '';
        sidebar.style.top = '';
        sidebar.style.transform = '';
    }

    setTimeout(() => {
        sidebar.classList.add('show');
    }, 10);

    console.log('驳回理由侧边栏显示完成');
}

// 隐藏驳回理由侧边栏
function hideRejectionSidebar() {
    console.log('=== 隐藏驳回理由侧边栏 ===');

    const sidebar = document.getElementById('rejectionSidebar');
    if (sidebar) {
        sidebar.classList.remove('show');
        setTimeout(() => {
            sidebar.classList.add('d-none');
        }, 300);
    }
}

// 初始化侧边栏拖拽功能
function initSidebarDrag() {
    const sidebar = document.getElementById('rejectionSidebar');
    const header = sidebar.querySelector('.rejection-sidebar-header');

    if (!sidebar || !header) return;

    let isDragging = false;
    let startX, startY, startLeft, startTop;

    header.addEventListener('mousedown', function(e) {
        // 如果点击的是关闭按钮，不启动拖拽
        if (e.target.classList.contains('btn-close') || e.target.closest('.btn-close')) {
            return;
        }

        isDragging = true;
        sidebar.classList.add('dragging');

        // 记录初始位置
        startX = e.clientX;
        startY = e.clientY;

        // 获取当前位置
        const rect = sidebar.getBoundingClientRect();
        startLeft = rect.left;
        startTop = rect.top;

        // 阻止文本选择
        e.preventDefault();

        console.log('开始拖拽侧边栏');
    });

    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        // 计算新位置
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        let newLeft = startLeft + deltaX;
        let newTop = startTop + deltaY;

        // 限制在视窗范围内
        const sidebarRect = sidebar.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 左边界限制
        if (newLeft < 0) newLeft = 0;
        // 右边界限制
        if (newLeft + sidebarRect.width > viewportWidth) {
            newLeft = viewportWidth - sidebarRect.width;
        }
        // 上边界限制
        if (newTop < 0) newTop = 0;
        // 下边界限制
        if (newTop + sidebarRect.height > viewportHeight) {
            newTop = viewportHeight - sidebarRect.height;
        }

        // 应用新位置
        sidebar.style.left = newLeft + 'px';
        sidebar.style.top = newTop + 'px';
        sidebar.style.transform = 'none'; // 移除默认的transform
    });

    document.addEventListener('mouseup', function() {
        if (isDragging) {
            isDragging = false;
            sidebar.classList.remove('dragging');
            console.log('结束拖拽侧边栏');
        }
    });

    // 防止拖拽时选中文本
    header.addEventListener('selectstart', function(e) {
        e.preventDefault();
    });
}

// 获取驳回类型显示文本
function getTypeDisplayText(rejectionType) {
    const typeMap = {
        'content': '文案问题',
        'image': '图片问题',
        'both': '文案+图片问题'
    };
    return typeMap[rejectionType] || rejectionType;
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 加载已上传的图片
function loadUploadedImages(contentId) {
    fetch(`/simple/api/images/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const images = data.images;
                const imageGrid = document.getElementById('imageGrid');
                const imageCount = document.getElementById('imageCount');

                // 更新图片数量显示
                imageCount.textContent = `${images.length}/${maxImagesPerContent}`;

                // 清空现有图片
                imageGrid.innerHTML = '';

                if (images.length === 0) {
                    imageGrid.innerHTML = `
                        <div class="col-12 text-center py-4">
                            <i class="bi bi-images fs-1 text-muted"></i>
                            <p class="text-muted mt-2">暂无图片</p>
                        </div>
                    `;
                } else {
                    // 显示图片（支持拖拽排序）
                    images.forEach((image, index) => {
                        const imageHtml = `
                            <div class="col-md-4 mb-2 image-item"
                                 draggable="true"
                                 data-image-id="${image.id}"
                                 data-image-order="${image.image_order || index}">
                                <div class="card position-relative">
                                    <div class="drag-handle position-absolute top-0 start-0 p-1"
                                         style="background: rgba(0,0,0,0.5); color: white; cursor: move; z-index: 10;">
                                        <i class="bi bi-arrows-move"></i>
                                    </div>
                                    <div class="order-badge position-absolute top-0 end-0 p-1"
                                         style="background: rgba(0,0,0,0.7); color: white; font-size: 12px; z-index: 10;">
                                        ${index + 1}
                                    </div>
                                    <img src="/static/uploads/${image.image_path}"
                                         class="card-img-top"
                                         style="height: 120px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted text-truncate" title="${image.original_name}">
                                                ${image.original_name.length > 15 ? image.original_name.substring(0, 15) + '...' : image.original_name}
                                            </small>
                                            <button class="btn btn-danger btn-sm" onclick="deleteImage(${image.id}, ${contentId})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        imageGrid.insertAdjacentHTML('beforeend', imageHtml);
                    });

                    // 初始化拖拽排序功能
                    initImageSorting();
                }
            }
        })
        .catch(error => {
            console.error('加载图片失败:', error);
            document.getElementById('imageGrid').innerHTML = '<p class="text-danger">加载失败</p>';
        });
}

// 拖拽上传功能
function handleDragOver(event) {
    // 如果正在进行图片排序，不处理文件上传
    if (isImageSorting || window.isImageSorting) {
        return;
    }

    // 检查是否包含text/html类型（图片排序拖拽会有这个类型）
    if (event.dataTransfer.types.includes('text/html')) {
        return;
    }

    // 检查是否是文件拖拽（从外部拖入）
    if (!event.dataTransfer.types.includes('Files')) {
        return;
    }

    event.preventDefault();
    event.stopPropagation();

    // 显示拖拽覆盖层
    const overlay = document.getElementById('dragOverlay');
    if (overlay) {
        overlay.classList.remove('d-none');
    }
}

function handleDragLeave(event) {
    // 如果正在进行图片排序，不处理文件上传
    if (isImageSorting || window.isImageSorting) {
        return;
    }

    event.preventDefault();
    event.stopPropagation();

    // 检查是否真的离开了模态框区域
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        const overlay = document.getElementById('dragOverlay');
        if (overlay) {
            overlay.classList.add('d-none');
        }
    }
}

function handleDrop(event) {
    console.log('handleDrop called', {
        isImageSorting: isImageSorting,
        windowIsImageSorting: window.isImageSorting,
        hasFiles: event.dataTransfer.files && event.dataTransfer.files.length > 0,
        dataTransferTypes: Array.from(event.dataTransfer.types)
    });

    // 如果正在进行图片排序，不处理文件上传
    if (isImageSorting || window.isImageSorting) {
        console.log('阻止文件上传：正在进行图片排序');
        event.preventDefault();
        event.stopPropagation();
        return;
    }

    // 检查是否包含text/html类型（图片排序拖拽会有这个类型）
    if (event.dataTransfer.types.includes('text/html')) {
        console.log('阻止文件上传：检测到text/html类型（图片排序）');
        return;
    }

    // 检查是否是文件拖拽
    if (!event.dataTransfer.files || event.dataTransfer.files.length === 0) {
        console.log('没有文件，忽略');
        return;
    }

    // 检查是否只包含Files类型（真正的文件拖拽）
    if (!event.dataTransfer.types.includes('Files')) {
        console.log('不是文件拖拽，忽略');
        return;
    }

    console.log('处理文件上传');
    event.preventDefault();
    event.stopPropagation();

    // 隐藏拖拽覆盖层
    const overlay = document.getElementById('dragOverlay');
    if (overlay) {
        overlay.classList.add('d-none');
    }

    const files = event.dataTransfer.files;
    // 使用全局变量确保获取到正确的值
    const actualContentId = window.currentContentId;
    console.log('拖拽上传 - 当前文案ID:', actualContentId);

    if (files.length > 0 && actualContentId) {
        // 过滤出图片文件
        const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
        if (imageFiles.length > 0) {
            uploadFilesArray(imageFiles, actualContentId);
        } else {
            createSimpleToast('请选择图片文件', 'error');
        }
    } else if (files.length > 0 && !actualContentId) {
        console.error('❌ 拖拽上传失败：当前文案ID为空');
        showToast('请先点击"管理图片"按钮打开图片管理窗口，然后再拖拽上传图片', 'warning');
    }
}

function handleFileSelect(event) {
    console.log('=== 文件选择事件触发 ===');
    const files = event.target.files;
    console.log('选择的文件数量:', files.length);
    console.log('当前文案ID (局部变量):', currentContentId);
    console.log('当前文案ID (全局变量):', window.currentContentId);

    // 使用全局变量确保获取到正确的值
    const actualContentId = window.currentContentId;
    console.log('实际使用的文案ID:', actualContentId);

    if (files.length > 0 && actualContentId) {
        console.log('开始上传文件...');
        uploadFilesArray(files, actualContentId);
    } else {
        if (files.length === 0) {
            console.warn('❌ 没有选择文件');
        }
        if (!actualContentId) {
            console.error('❌ 当前文案ID为空，无法上传');
            // 显示用户友好的提示
            showToast('请先点击"管理图片"按钮打开图片管理窗口，然后再上传图片', 'warning');
            // 清空文件选择
            event.target.value = '';
        }
    }
}

function triggerFileSelect() {
    const fileInput = document.getElementById('modalFileInput');
    if (fileInput) {
        fileInput.click();
    }
}

// 文件上传处理
function uploadFiles(input, contentId) {
    const files = input.files;
    if (files.length > 0) {
        uploadFilesArray(files, contentId);
    }
}

function uploadFilesArray(files, contentId) {
    // 确保DOM已加载
    if (document.readyState === 'loading') {
        console.log('DOM未完全加载，延迟执行上传');
        document.addEventListener('DOMContentLoaded', () => {
            uploadFilesArray(files, contentId);
        });
        return;
    }

    // 设置上传状态标志
    window.isUploading = true;
    console.log('🚀 开始上传，设置上传状态标志');

    const modalElement = document.getElementById('uploadProgressModal');
    const statusDiv = document.getElementById('uploadStatus');

    // 检查元素是否存在
    if (!modalElement || !statusDiv) {
        console.error('上传进度模态框元素未找到', {
            modalElement: !!modalElement,
            statusDiv: !!statusDiv,
            readyState: document.readyState
        });
        return;
    }

    // 清空之前的内容并显示简洁的上传提示
    const totalFiles = files.length;
    statusDiv.innerHTML = ''; // 清空之前的内容
    statusDiv.textContent = `${totalFiles}张图片上传中...`;

    // 手动显示模态框 - 再次检查元素是否存在
    if (modalElement && modalElement.style) {
        try {
            modalElement.classList.add('show');
            modalElement.style.display = 'block';
            document.body.classList.add('modal-open');
        } catch (error) {
            console.error('显示上传进度模态框时出错:', error);
            return;
        }
    } else {
        console.error('模态框元素无效，无法显示上传进度');
        return;
    }

    // 检查是否需要为上传进度添加遮罩
    // 如果图片管理模态框已经有遮罩，就不需要额外添加
    const existingBackdrop = document.querySelector('.modal-backdrop');
    let uploadProgressBackdrop = null;

    if (!existingBackdrop) {
        // 没有现有遮罩，为上传进度创建一个
        uploadProgressBackdrop = document.createElement('div');
        uploadProgressBackdrop.className = 'modal-backdrop fade show';
        uploadProgressBackdrop.setAttribute('data-upload-progress', 'true'); // 标记这是上传进度的遮罩
        document.body.appendChild(uploadProgressBackdrop);
        console.log('为上传进度创建了新遮罩');
    } else {
        console.log('检测到现有遮罩（可能是图片管理模态框的），不创建新遮罩');
    }

    let uploadedCount = 0;
    
    // 逐个上传文件
    Array.from(files).forEach((file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('content_id', contentId);
        
        fetch('/simple/api/upload/image', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('上传响应状态:', response.status);
            if (!response.ok) {
                // HTTP错误状态，但仍需要解析JSON获取错误信息
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                });
            }
            return response.json();
        })
        .then(data => {
            uploadedCount++;

            // 更新简洁的进度提示 - 检查元素是否仍然存在
            const currentStatusDiv = document.getElementById('uploadStatus');
            if (currentStatusDiv) {
                const remainingFiles = totalFiles - uploadedCount;
                if (remainingFiles > 0) {
                    currentStatusDiv.textContent = `${remainingFiles}张图片上传中...`;
                } else {
                    currentStatusDiv.textContent = '上传完成！';
                }
            }

            if (data.success) {
                // 每个文件上传成功后立即更新数量显示
                console.log('单个文件上传成功，更新数量显示');
                getCurrentImageCount(contentId).then(count => {
                    console.log('获取到最新图片数量:', count);
                    updateImageCountDisplay(contentId, count);
                });
            }
            
            // 所有文件上传完成
            if (uploadedCount === totalFiles) {
                // 显示完成状态，然后快速关闭
                setTimeout(() => {
                    // 关闭上传进度模态框（不是图片管理模态框）
                    safeCloseUploadModal();
                    console.log('✅ 上传进度模态框已关闭，图片管理模态框保持打开');

                    // 检查遮罩状态
                    setTimeout(() => {
                        const backdrop = document.querySelector('.modal-backdrop');
                        console.log('上传完成后，遮罩状态:', backdrop ? '存在' : '不存在');
                        if (backdrop) {
                            console.log('✅ 图片管理模态框的遮罩已保留');
                        } else {
                            console.log('❌ 遮罩被意外移除了');
                        }
                    }, 100);

                    // 如果在模态框中上传，刷新模态框中的图片显示
                    if (currentContentId) {
                        console.log('🔄 刷新图片管理模态框中的图片显示');
                        loadUploadedImages(currentContentId);
                        // 清空文件选择，准备下次上传
                        document.getElementById('modalFileInput').value = '';
                        console.log('✅ 图片管理模态框已更新，用户可以继续操作');

                        // 显示友好提示
                        showToast('图片上传完成！您可以继续上传更多图片或调整图片顺序', 'success');

                        // 重置上传状态标志
                        window.isUploading = false;
                        console.log('✅ 上传完成，重置上传状态标志');
                    }

                    // 立即更新页面上的图片数量显示
                    console.log('=== 上传完成，立即更新页面显示 ===');
                    console.log('使用contentId:', contentId, '当前文案ID:', currentContentId);

                    // 使用当前正在编辑的文案ID
                    const targetContentId = currentContentId || contentId;
                    if (targetContentId) {
                        console.log('开始更新文案', targetContentId, '的图片数量显示');
                        getCurrentImageCount(targetContentId).then(count => {
                            console.log('获取到最新图片数量:', count);
                            console.log('调用updateImageCountDisplay更新显示');
                            updateImageCountDisplay(targetContentId, count);
                            console.log('✅ 页面显示更新完成');
                        }).catch(error => {
                            console.error('获取图片数量失败:', error);
                        });
                    }

                    // 如果不在模态框中，刷新整个页面
                    console.log('检查是否需要刷新页面，currentContentId:', currentContentId);
                    console.log('检查全局变量，window.currentContentId:', window.currentContentId);
                    console.log('检查上传状态，window.isUploading:', window.isUploading);

                    // 使用全局变量进行判断，更可靠
                    const actualContentId = window.currentContentId;
                    if (!actualContentId && !window.isUploading) {
                        console.log('❌ 全局currentContentId为空且不在上传中，将刷新页面');
                        location.reload();
                    } else {
                        console.log('✅ 在模态框中上传或正在上传中，保持模态框打开，contentId:', actualContentId);
                    }
                }, 1500);
            }
        })
        .catch(error => {
            uploadedCount++;
            console.error('图片上传失败:', error.message);

            // 更新简洁的进度提示（错误情况）
            const currentStatusDiv = document.getElementById('uploadStatus');
            if (currentStatusDiv) {
                const remainingFiles = totalFiles - uploadedCount;
                if (remainingFiles > 0) {
                    currentStatusDiv.textContent = `${remainingFiles}张图片上传中...`;
                } else {
                    currentStatusDiv.textContent = '上传失败！';
                }
            }

            // 显示具体的错误信息
            let errorMessage = error.message;

            // 针对常见错误提供更友好的提示
            if (errorMessage.includes('文案状态不正确')) {
                errorMessage = '该文案当前状态不允许上传图片。请确保文案已通过初审。';
            } else if (errorMessage.includes('文案不存在')) {
                errorMessage = '文案不存在，请刷新页面重试。';
            } else if (errorMessage.includes('没有选择文件')) {
                errorMessage = '请选择要上传的图片文件。';
            }

            showToast(`图片上传失败: ${errorMessage}`, 'error');
            
            if (uploadedCount === totalFiles) {
                setTimeout(() => {
                    // 关闭上传进度模态框（不是图片管理模态框）
                    safeCloseUploadModal();
                    console.log('✅ 上传进度模态框已关闭（错误处理），图片管理模态框保持打开');

                    // 如果在模态框中上传，刷新模态框中的图片显示
                    if (currentContentId) {
                        console.log('🔄 刷新图片管理模态框中的图片显示（错误处理）');
                        loadUploadedImages(currentContentId);
                        // 清空文件选择，准备下次上传
                        document.getElementById('modalFileInput').value = '';
                        console.log('✅ 图片管理模态框已更新（错误处理），用户可以继续操作');

                        // 重置上传状态标志
                        window.isUploading = false;
                        console.log('✅ 上传完成（错误处理），重置上传状态标志');
                    }

                    // 更新页面上的图片数量显示（使用传入的contentId）
                    if (contentId) {
                        console.log('更新图片数量显示（失败处理），contentId:', contentId);
                        getCurrentImageCount(contentId).then(count => {
                            console.log('获取到图片数量:', count);
                            updateImageCountDisplay(contentId, count);
                        });
                    }

                    // 如果不在模态框中，刷新整个页面
                    console.log('错误处理：检查是否需要刷新页面，currentContentId:', currentContentId);
                    console.log('错误处理：检查全局变量，window.currentContentId:', window.currentContentId);
                    console.log('错误处理：检查上传状态，window.isUploading:', window.isUploading);

                    // 使用全局变量进行判断，更可靠
                    const actualContentId = window.currentContentId;
                    if (!actualContentId && !window.isUploading) {
                        console.log('❌ 全局currentContentId为空且不在上传中，将刷新页面');
                        location.reload();
                    } else {
                        console.log('✅ 在模态框中上传或正在上传中，保持模态框打开，contentId:', actualContentId);
                    }
                }, 1500);
            }
        });
    });
}

// 删除图片
function deleteImage(imageId, contentId) {
    if (!confirm('确定要删除这张图片吗？')) {
        return;
    }
    
    fetch(`/simple/api/images/${imageId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('图片删除成功', 'success');

            // 更新页面上的图片数量显示
            getCurrentImageCount(contentId).then(count => {
                updateImageCountDisplay(contentId, count);
            });

            // 延迟刷新页面，让用户看到数量更新
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('删除失败: ' + error.message, 'error');
    });
}

// 创建简单的toast提示函数
function createSimpleToast(message, type = 'success') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
    const iconClass = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle';

    const toastHtml = `
        <div class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${iconClass} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        toast.show();

        // 监听隐藏事件，移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // 如果Bootstrap不可用，使用简单的显示/隐藏
        toastElement.style.display = 'block';
        setTimeout(() => {
            toastElement.remove();
        }, 3000);
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    createSimpleToast(message, type);
}

// 更新页面上指定内容的图片数量显示
function updateImageCountDisplay(contentId, newCount) {
    console.log('=== updateImageCountDisplay 开始执行 ===');
    console.log('文案ID:', contentId, '新数量:', newCount);

    // 直接通过data-content-id查找badge元素
    const badgeElement = document.querySelector(`span.badge[data-content-id="${contentId}"]`);
    console.log('查找到的badge元素:', badgeElement);

    if (badgeElement) {
        console.log('当前badge内容:', badgeElement.textContent);
        console.log('当前badge类名:', badgeElement.className);

        // 强制更新内容和样式
        const newText = `${newCount}张图片`;
        const newClass = `badge bg-${newCount > 0 ? 'success' : 'secondary'}`;

        // 使用多种方式强制更新，确保显示正确
        badgeElement.textContent = newText;
        badgeElement.innerText = newText;
        badgeElement.innerHTML = newText;
        badgeElement.className = newClass;

        // 强制重绘
        badgeElement.style.display = 'none';
        badgeElement.offsetHeight; // 触发重绘
        badgeElement.style.display = '';

        console.log('✅ Badge更新完成:');
        console.log('  新内容:', badgeElement.textContent);
        console.log('  新类名:', badgeElement.className);

        // 🔥 关键修复：更新复选框的 data-image-count 属性
        const checkbox = document.querySelector(`input.content-checkbox[data-content-id="${contentId}"]`);
        if (checkbox) {
            checkbox.setAttribute('data-image-count', newCount);
            console.log('✅ 复选框 data-image-count 已更新为:', newCount);
        } else {
            console.error('❌ 未找到对应的复选框，contentId:', contentId);
        }

        // 立即更新对应的提交按钮状态
        console.log('调用updateSubmitButtonState更新按钮状态...');
        updateSubmitButtonState(contentId, newCount);

        // 延迟再次确认按钮状态
        setTimeout(() => {
            console.log('延迟确认按钮状态...');
            updateSubmitButtonState(contentId, newCount);
        }, 100);
    } else {
        console.error('❌ 没有找到对应的badge元素，contentId:', contentId);
        // 列出所有badge元素用于调试
        const allBadges = document.querySelectorAll('span.badge[data-content-id]');
        console.log('页面上所有的badge元素数量:', allBadges.length);
        allBadges.forEach((badge, index) => {
            console.log(`Badge ${index}: data-content-id=${badge.getAttribute('data-content-id')}, 内容=${badge.textContent}`);
        });
    }

    // 更新总图片数量（重新计算所有内容的图片数量）
    updateTotalImageCount();

    console.log('=== updateImageCountDisplay 执行完成 ===');
}

// 更新提交按钮状态
function updateSubmitButtonState(contentId, imageCount) {
    console.log('=== 更新提交按钮状态 ===');
    console.log('文案ID:', contentId, '图片数量:', imageCount);

    // 找到对应的表格行
    const contentRow = document.querySelector(`tr[data-content-id="${contentId}"]`);
    console.log('找到的表格行:', contentRow);

    if (contentRow) {
        // 查找提交按钮
        const submitButton = contentRow.querySelector('button[onclick*="submitContent"]');
        console.log('找到的提交按钮:', submitButton);

        if (submitButton) {
            console.log('按钮当前状态 - disabled:', submitButton.disabled, 'class:', submitButton.className);

            if (imageCount > 0) {
                // 有图片，激活按钮
                submitButton.disabled = false;
                submitButton.removeAttribute('title');
                submitButton.classList.remove('btn-secondary');
                submitButton.classList.add('btn-success');
                console.log('✅ 提交按钮已激活 - 新状态:', submitButton.className);
            } else {
                // 没有图片，禁用按钮
                submitButton.disabled = true;
                submitButton.setAttribute('title', '请先上传图片');
                submitButton.classList.remove('btn-success');
                submitButton.classList.add('btn-secondary');
                console.log('❌ 提交按钮已禁用 - 新状态:', submitButton.className);
            }
        } else {
            console.error('❌ 未找到提交按钮');
            // 列出该行的所有按钮
            const allButtons = contentRow.querySelectorAll('button');
            console.log('该行的所有按钮:', allButtons);
            allButtons.forEach((btn, index) => {
                console.log(`按钮${index}:`, btn.outerHTML);
            });
        }
    } else {
        console.error('❌ 未找到对应的表格行');
        // 列出所有表格行
        const allRows = document.querySelectorAll('tr[data-content-id]');
        console.log('页面上所有的表格行:', allRows);
        allRows.forEach((row, index) => {
            console.log(`行${index}: data-content-id=${row.getAttribute('data-content-id')}`);
        });
    }
}

// 更新总图片数量显示
function updateTotalImageCount() {
    let totalCount = 0;
    const countBadges = document.querySelectorAll('tbody .badge');
    countBadges.forEach(badge => {
        // 从"X张图片"格式中提取数字
        const match = badge.textContent.match(/(\d+)张图片/);
        if (match) {
            const count = parseInt(match[1]) || 0;
            totalCount += count;
        }
    });

    // 更新总数显示
    const totalCountElement = document.querySelector('.text-warning');
    if (totalCountElement) {
        totalCountElement.textContent = totalCount;
    }

    console.log('总图片数量已更新:', totalCount);
}

// 获取指定内容的当前图片数量
function getCurrentImageCount(contentId) {
    return fetch(`/simple/api/images/${contentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                return data.images.length;
            }
            return 0;
        })
        .catch(error => {
            console.error('获取图片数量失败:', error);
            return 0;
        });
}

// 确保关键函数在全局作用域中可访问（防止Ajax更新后函数丢失）
function ensureGlobalFunctions() {
    // 将所有需要在onclick中调用的函数设置为全局函数
    window.openImageUploadModal = openImageUploadModal;
    window.filterByStatus = filterByStatus;
    window.performSearch = performSearch;
    window.resetFilters = resetFilters;
    window.triggerFileSelect = triggerFileSelect;
    window.deleteImage = deleteImage;
    window.submitContent = submitContent;

    console.log('✅ 全局函数已重新设置，防止Ajax更新后丢失');
}

// 将重新设置函数也设为全局，以便在需要时调用
window.ensureGlobalFunctions = ensureGlobalFunctions;

// 提交内容到最终审核 - 确保在全局作用域
window.submitContent = function(contentId) {
    console.log('submitContent 被调用，contentId:', contentId);

    console.log('开始提交到最终审核...');

    fetch(`/simple/api/contents/${contentId}/submit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        console.log('提交响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('提交响应数据:', data);
        if (data.success) {
            // 显示服务器返回的消息
            showToast(data.message, 'success');

            // 检查是否应该移除文案行（进入终审或图片提交完成）
            const shouldRemoveRow = data.message.includes('已提交最终审核') ||
                                  data.message.includes('进入最终审核') ||
                                  data.message.includes('进入终审') ||
                                  data.message.includes('图片已重新上传');  // 图片提交成功也移除

            if (shouldRemoveRow) {
                // 只有真正进入终审时才移除文案行
                const contentRow = document.querySelector(`tr[data-content-id="${contentId}"]`);
                if (contentRow) {
                    console.log('找到文案行，开始淡出动画');

                    // 添加淡出动画
                    contentRow.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                    contentRow.style.opacity = '0.5';
                    contentRow.style.transform = 'translateX(20px)';

                    // 动画完成后移除行
                    setTimeout(() => {
                        contentRow.style.opacity = '0';
                        contentRow.style.transform = 'translateX(50px)';

                        // 再等一点时间后完全移除
                        setTimeout(() => {
                            contentRow.remove();
                            console.log('文案行已移除');

                            // 检查是否还有文案，如果没有则显示空状态
                            const remainingRows = document.querySelectorAll('tbody tr[data-content-id]');
                            if (remainingRows.length === 0) {
                                const tbody = document.querySelector('tbody');
                                if (tbody) {
                                    tbody.innerHTML = `
                                        <tr>
                                            <td colspan="6" class="text-center py-5">
                                                <i class="bi bi-check-circle fs-1 text-success"></i>
                                                <h5 class="text-muted mt-3">所有文案已提交完成</h5>
                                                <p class="text-muted">当前没有待上传图片的文案</p>
                                            </td>
                                        </tr>
                                    `;
                                }
                            }

                            // 更新统计数字
                            updateStatistics();
                        }, 300);
                    }, 200);
                } else {
                    console.log('未找到对应的文案行');
                }
            } else {
                // 部分完成，不移除文案行，只显示提示信息
                console.log('部分完成，文案行保留在页面上');

                // 可以选择性地更新行的样式，表示状态已更新
                const contentRow = document.querySelector(`tr[data-content-id="${contentId}"]`);
                if (contentRow) {
                    // 添加一个短暂的高亮效果，表示状态已更新
                    contentRow.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        contentRow.style.backgroundColor = '';
                    }, 2000);
                }
            }
        } else {
            showToast('提交失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('提交失败:', error);
        showToast('提交失败: ' + error.message, 'error');
    });
};



console.log('=== 图片上传页面JavaScript开始执行 ===');

// 检查Bootstrap是否正确加载
if (typeof bootstrap !== 'undefined') {
    console.log('✅ Bootstrap已加载，版本:', bootstrap);
} else {
    console.error('❌ Bootstrap未加载！');
}

// 检查关键元素是否存在
console.log('检查关键元素:');
console.log('图片上传模态框:', document.getElementById('imageUploadModal'));
console.log('模态框文件输入:', document.getElementById('modalFileInput'));

// 立即测试元素是否存在
console.log('立即检查元素:');
console.log('客户选择框:', document.getElementById('client_filter'));
console.log('任务选择框:', document.getElementById('task_filter'));

// 简化版的客户任务联动，直接绑定
function setupClientTaskBinding() {
    console.log('=== 设置客户任务绑定 ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    if (!clientSelect) {
        console.error('❌ 找不到客户选择框 #client_filter');
        return;
    }

    if (!taskSelect) {
        console.error('❌ 找不到任务选择框 #task_filter');
        return;
    }

    console.log('✅ 找到了客户和任务选择框');
    console.log('客户选择框选项数量:', clientSelect.options.length);
    console.log('任务选择框当前内容:', taskSelect.innerHTML);

    // 绑定客户选择变更事件
    clientSelect.onchange = function() {
        console.log('=== 客户选择变更 (onchange) ===');
        console.log('新选择的客户ID:', this.value);
        console.log('客户名称:', this.options[this.selectedIndex].text);

        const clientId = this.value;

        if (clientId) {
            // 选择了具体客户，加载该客户的任务
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            console.log('开始调用API获取任务...');

            // 调用API
            fetch(`/simple/api/get-client-tasks/${clientId}`)
                .then(response => {
                    console.log('API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('API响应数据:', data);

                    // 更新任务选择框
                    taskSelect.innerHTML = '<option value="">全部任务</option>';
                    taskSelect.disabled = false;

                    if (data.success && data.tasks) {
                        data.tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.name;
                            taskSelect.appendChild(option);
                        });
                        console.log(`✅ 成功加载 ${data.tasks.length} 个任务`);
                    } else {
                        console.log('该客户没有任务');
                    }

                    // 任务加载完成后，执行搜索（重置到第一页）
                    performSearch(1, 20);
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    taskSelect.innerHTML = '<option value="">加载失败</option>';
                    taskSelect.disabled = false;
                });
        } else {
            // 选择了"全部客户"，重置任务选择框并显示所有数据
            console.log('选择了全部客户，重置任务选择框');
            taskSelect.innerHTML = '<option value="">全部任务</option>';
            taskSelect.disabled = false;

            // 执行搜索显示所有数据（重置到第一页）
            performSearch(1, 20);
        }
    };

    console.log('✅ 客户选择事件绑定完成');

    // 绑定任务选择变更事件
    taskSelect.onchange = function() {
        console.log('=== 任务选择变更 ===');
        console.log('选择的任务ID:', this.value);
        performSearch(1, 20);
    };

    // 绑定状态选择变更事件
    const statusSelect = document.getElementById('status_filter');
    if (statusSelect) {
        statusSelect.onchange = function() {
            console.log('=== 状态选择变更 ===');
            console.log('选择的状态:', this.value);
            performSearch(1, 20);
        };
    }

    // 如果有默认选中的客户，只加载任务，不触发搜索
    if (clientSelect.value) {
        console.log('发现默认选中的客户，只加载任务:', clientSelect.value);
        const clientId = clientSelect.value;

        if (clientId) {
            // 加载该客户的任务，但不触发搜索
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            fetch(`/simple/api/get-client-tasks/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    taskSelect.innerHTML = '<option value="">全部任务</option>';
                    taskSelect.disabled = false;

                    if (data.success && data.tasks) {
                        data.tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.name;
                            taskSelect.appendChild(option);
                        });
                        console.log(`✅ 成功加载 ${data.tasks.length} 个任务（不触发搜索）`);
                    }
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    taskSelect.innerHTML = '<option value="">加载失败</option>';
                    taskSelect.disabled = false;
                });
        }
    }

    // 页面初始化时显示所有数据（默认选择"全部客户"）
    console.log('页面初始化，显示所有数据');

    // 从服务器传递的per_page值或URL参数中获取初始的每页显示数量
    const urlParams = new URLSearchParams(window.location.search);
    const initialPerPage = urlParams.get('per_page') || {% if per_page %}{{ per_page }}{% else %}20{% endif %};
    currentPerPage = parseInt(initialPerPage);
    console.log('初始每页显示数量:', currentPerPage);

    performSearch(1, currentPerPage);
}

// 全局变量：当前每页显示数量
let currentPerPage = 20; // 默认20条

// 分页处理函数
window.changePageForSimple = function(pageNum, perPage) {
    console.log('=== 图片上传页面分页处理 ===');
    console.log('页码:', pageNum, '每页显示:', perPage);

    // 更新全局变量
    currentPerPage = perPage || currentPerPage;

    // 调用搜索函数，传入分页参数
    performSearch(pageNum, currentPerPage);
};

// 全局分页函数（确保分页组件能正常工作）
window.changePage = function(pageNum, evt) {
    console.log('changePage called with:', pageNum);

    // 如果在简化版本页面中
    if (window.changePageForSimple && typeof window.changePageForSimple === 'function') {
        console.log('使用简化版本页面处理器');
        console.log('当前每页显示数量:', currentPerPage);
        window.changePageForSimple(pageNum, currentPerPage);
    } else {
        // 在普通页面中，使用页面跳转
        console.log('使用页面跳转');
        const url = new URL(window.location);
        url.searchParams.set('page', pageNum);
        url.searchParams.set('per_page', currentPerPage);
        window.location.href = url.toString();
    }

    return false;
};

// 全局页面大小改变函数
window.changePageSize = function(newSize, evt) {
    console.log('changePageSize called with:', newSize);

    // 更新全局变量
    currentPerPage = parseInt(newSize);
    console.log('更新每页显示数量为:', currentPerPage);

    // 如果在简化版本页面中
    if (window.changePageForSimple && typeof window.changePageForSimple === 'function') {
        console.log('使用简化版本页面处理器');
        window.changePageForSimple(1, currentPerPage);
    } else {
        // 在普通页面中，使用页面跳转
        console.log('使用页面跳转');
        const url = new URL(window.location);
        url.searchParams.set('per_page', currentPerPage);
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    }

    return false;
};

// 同步每页显示数量选择框的状态
function syncPageSizeSelector() {
    console.log('=== 同步每页显示数量选择框 ===');
    console.log('当前每页显示数量:', currentPerPage);

    // 查找所有的每页显示选择框
    const pageSizeSelectors = document.querySelectorAll('select.form-select');

    pageSizeSelectors.forEach((selector, index) => {
        // 检查是否是每页显示选择框（通过onchange属性判断）
        if (selector.getAttribute('onchange') && selector.getAttribute('onchange').includes('changePageSize')) {
            console.log(`找到每页显示选择框 #${index + 1}`);

            // 设置正确的选中值
            selector.value = currentPerPage.toString();
            console.log(`设置选择框值为: ${currentPerPage}`);

            // 如果没有找到对应的选项，添加一个
            if (!selector.querySelector(`option[value="${currentPerPage}"]`)) {
                console.log(`添加缺失的选项: ${currentPerPage}`);
                const option = document.createElement('option');
                option.value = currentPerPage;
                option.textContent = `${currentPerPage}条`;
                option.selected = true;
                selector.appendChild(option);
            }
        }
    });

    console.log('✅ 每页显示数量选择框同步完成');
};

// 不触发搜索的客户任务绑定函数（用于AJAX更新后）
function setupClientTaskBindingWithoutSearch() {
    console.log('=== 设置客户任务绑定（不触发搜索） ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    if (!clientSelect || !taskSelect) {
        console.log('❌ 找不到筛选元素，跳过绑定');
        return;
    }

    // 绑定客户选择变更事件
    clientSelect.onchange = function() {
        console.log('=== 客户选择变更 (onchange) ===');
        console.log('新选择的客户ID:', this.value);

        const clientId = this.value;

        if (clientId) {
            // 选择了具体客户，加载该客户的任务
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            fetch(`/simple/api/get-client-tasks/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    taskSelect.innerHTML = '<option value="">全部任务</option>';
                    taskSelect.disabled = false;

                    if (data.success && data.tasks) {
                        data.tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.name;
                            taskSelect.appendChild(option);
                        });
                        console.log(`✅ 成功加载 ${data.tasks.length} 个任务`);
                    }

                    // 任务加载完成后，执行搜索
                    performSearch(1, 20);
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    taskSelect.innerHTML = '<option value="">加载失败</option>';
                    taskSelect.disabled = false;
                });
        } else {
            // 选择了"全部客户"，重置任务选择框
            taskSelect.innerHTML = '<option value="">全部任务</option>';
            taskSelect.disabled = false;
            performSearch(1, 20);
        }
    };

    // 绑定任务选择变更事件
    taskSelect.onchange = function() {
        console.log('=== 任务选择变更 ===');
        performSearch(1, 20);
    };

    // 绑定状态选择变更事件
    const statusSelect = document.getElementById('status_filter');
    if (statusSelect) {
        statusSelect.onchange = function() {
            console.log('=== 状态选择变更 ===');
            performSearch(1, 20);
        };
    }

    console.log('✅ 客户任务绑定完成（不触发初始搜索）');
};

// 注意：resetFilters函数已在下方定义，避免重复

// 执行搜索的函数
function performSearch(page = 1, perPage = 20) {
    console.log('=== 执行搜索 ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');
    const statusSelect = document.getElementById('status_filter');

    const clientId = clientSelect ? clientSelect.value : '';
    const taskId = taskSelect ? taskSelect.value : '';
    const status = statusSelect ? statusSelect.value : '';

    console.log('搜索参数:', { clientId, taskId, status, page, perPage });

    // 构建查询参数
    const params = new URLSearchParams();
    if (clientId) params.append('client_id', clientId);
    if (taskId) params.append('task_id', taskId);
    if (status) params.append('status', status);
    params.append('page', page);
    params.append('per_page', perPage);

    const url = `/simple/image-upload?${params.toString()}`;
    console.log('请求URL:', url);

    // 显示加载状态
    const contentTable = document.querySelector('tbody');
    if (contentTable) {
        contentTable.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在搜索...</div>
                </td>
            </tr>
        `;
    }

    // 发送AJAX请求
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('搜索响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('搜索响应长度:', html.length);

        // 解析返回的HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 找到主要的内容区域（包含表格和分页）
        // 查找文案列表的卡片（第二个卡片，第一个是统计信息）
        const newCards = doc.querySelectorAll('.card');
        const currentCards = document.querySelectorAll('.card');

        // 找到文案列表卡片（通常是最后一个卡片）
        let newContentCard = null;
        let currentContentCard = null;

        // 查找包含"文案列表"的卡片
        for (let card of newCards) {
            const cardHeader = card.querySelector('.card-header');
            if (cardHeader && cardHeader.textContent.includes('文案列表')) {
                newContentCard = card;
                break;
            }
        }

        for (let card of currentCards) {
            const cardHeader = card.querySelector('.card-header');
            if (cardHeader && cardHeader.textContent.includes('文案列表')) {
                currentContentCard = card;
                break;
            }
        }

        if (newContentCard && currentContentCard) {
            // 更新整个文案列表卡片（包括表格和分页）
            const newCardBody = newContentCard.querySelector('.card-body');
            const currentCardBody = currentContentCard.querySelector('.card-body');

            if (newCardBody && currentCardBody) {
                currentCardBody.innerHTML = newCardBody.innerHTML;
                console.log('✅ 文案列表卡片内容已更新（包括表格和分页）');

                // 重新初始化批量选择功能
                initBatchSelection();

                // 重新绑定客户任务联动（但不触发搜索）
                setupClientTaskBindingWithoutSearch();

                // 同步每页显示数量的选择框状态
                syncPageSizeSelector();

                // 重新设置全局函数，防止Ajax更新后函数丢失
                if (typeof ensureGlobalFunctions === 'function') {
                    ensureGlobalFunctions();
                } else {
                    console.warn('ensureGlobalFunctions函数不存在，可能需要刷新页面');
                }
            } else {
                console.error('❌ 无法找到卡片内容区域');
            }
        } else {
            console.error('❌ 无法找到文案列表卡片');
            console.log('newContentCard:', newContentCard);
            console.log('currentContentCard:', currentContentCard);

            // 尝试只更新表格内容作为备选方案
            const newTableBody = doc.querySelector('tbody');
            if (newTableBody && contentTable) {
                contentTable.innerHTML = newTableBody.innerHTML;
                console.log('✅ 表格内容已更新（备选方案）');
                initBatchSelection();

                // 重新设置全局函数，防止Ajax更新后函数丢失
                if (typeof ensureGlobalFunctions === 'function') {
                    ensureGlobalFunctions();
                }
            } else {
                console.log('没有找到表格内容，可能是空数据页面');
                // 检查是否是空数据页面
                const emptyMessage = doc.querySelector('.text-center.py-5');
                if (emptyMessage && contentTable) {
                    // 显示空数据消息
                    const tableContainer = contentTable.closest('.table-responsive') || contentTable.closest('.card-body');
                    if (tableContainer) {
                        tableContainer.innerHTML = emptyMessage.outerHTML;
                        console.log('✅ 已显示空数据消息');
                    }
                } else if (contentTable) {
                    contentTable.innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                                <h5 class="text-muted mt-3">搜索失败</h5>
                                <p class="text-muted">请刷新页面重试</p>
                            </td>
                        </tr>
                    `;
                }
            }
        }
    })
    .catch(error => {
        console.error('搜索失败:', error);
        if (contentTable) {
            contentTable.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-wifi-off text-danger fs-1"></i>
                        <h5 class="text-muted mt-3">网络错误</h5>
                        <p class="text-muted">请检查网络连接后重试</p>
                    </td>
                </tr>
            `;
        }
    });
}

// 页面初始化标志
let isPageInitialized = false;

// 页面加载完成后立即执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if (!isPageInitialized) {
            setupClientTaskBinding();
            isPageInitialized = true;
        }
    });
} else {
    // 如果页面已经加载完成，立即执行
    if (!isPageInitialized) {
        setupClientTaskBinding();
        isPageInitialized = true;
    }
}

// 更新统计数字的函数
function updateStatistics() {
    console.log('更新统计数字');

    // 获取当前页面上的文案行
    const contentRows = document.querySelectorAll('tbody tr[data-content-id]');
    const totalContents = contentRows.length;

    // 计算已上传图片的文案数量
    let uploadedCount = 0;
    let totalImages = 0;

    contentRows.forEach(row => {
        const badge = row.querySelector('.badge');
        if (badge) {
            const badgeText = badge.textContent.trim();
            // 从"X张图片"格式中提取数字
            const match = badgeText.match(/(\d+)张图片/);
            if (match) {
                const imageCount = parseInt(match[1]);
                totalImages += imageCount;
                if (imageCount > 0) {
                    uploadedCount++;
                }
            }
        }
    });

    // 更新统计卡片
    const statCards = document.querySelectorAll('.card .card-body h4');
    if (statCards.length >= 4) {
        statCards[0].textContent = totalContents; // 待上传图片文案
        statCards[1].textContent = uploadedCount; // 已上传图片文案
        statCards[2].textContent = totalImages;   // 总图片数量
        // 第4个卡片（每篇最大图片数）保持不变
    }

    console.log(`统计更新完成: 总文案${totalContents}, 已上传${uploadedCount}, 总图片${totalImages}`);
}

// 重置筛选条件的函数
function resetFilters() {
    console.log('=== 重置筛选条件 ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');
    const statusSelect = document.getElementById('status_filter');

    // 重置客户选择为"全部客户"
    if (clientSelect) {
        clientSelect.value = '';
        console.log('重置客户选择为全部客户');
    }

    // 重置任务选择为"全部任务"
    if (taskSelect) {
        taskSelect.innerHTML = '<option value="">全部任务</option>';
        taskSelect.disabled = false;
        console.log('重置任务选择为全部任务');
    }

    // 重置状态选择为"全部状态"
    if (statusSelect) {
        statusSelect.value = '';
        console.log('重置状态选择为全部状态');
    }

    // 执行搜索显示所有数据
    console.log('执行搜索显示所有数据');
    performSearch(1, 20);
}

// 初始化批量选择功能
function initBatchSelection() {
    console.log('=== 初始化批量选择功能 ===');

    // 绑定全选复选框事件
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        // 移除之前的事件监听器（避免重复绑定）
        selectAllCheckbox.removeEventListener('change', toggleSelectAll);
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
        console.log('✅ 全选复选框事件已绑定');
    }

    // 绑定所有内容复选框事件
    const contentCheckboxes = document.querySelectorAll('.content-checkbox');
    contentCheckboxes.forEach((checkbox, index) => {
        // 移除之前的事件监听器（避免重复绑定）
        checkbox.removeEventListener('change', updateBatchSubmitButton);
        checkbox.addEventListener('change', updateBatchSubmitButton);
        console.log(`✅ 复选框 ${index} 事件已绑定`);
    });

    // 绑定批量提交按钮事件（避免重复绑定）
    const batchSubmitBtn = document.getElementById('batchSubmitBtn');
    if (batchSubmitBtn && !batchSubmitBtn.hasAttribute('data-event-bound')) {
        batchSubmitBtn.addEventListener('click', batchSubmitContents);
        batchSubmitBtn.setAttribute('data-event-bound', 'true');
        console.log('✅ 批量提交按钮事件已绑定');
    } else if (batchSubmitBtn) {
        console.log('批量提交按钮事件已存在，跳过绑定');
    }

    // 初始化按钮状态
    updateBatchSubmitButton();

    console.log('=== 批量选择功能初始化完成 ===');
}

// 页面加载时初始化（避免重复初始化）
document.addEventListener('DOMContentLoaded', function() {
    if (!isPageInitialized) {
        initBatchSelection();
    }
});

// 图片拖拽排序功能
let draggedElement = null;
// isImageSorting 变量已在全局声明，这里不需要重复声明

function initImageSorting() {
    console.log('=== 初始化图片拖拽排序功能 ===');

    const imageItems = document.querySelectorAll('.image-item');

    imageItems.forEach(item => {
        // 拖拽开始
        item.addEventListener('dragstart', function(e) {
            console.log('拖拽开始:', this.dataset.imageId);
            draggedElement = this;
            isImageSorting = true;
            this.style.opacity = '0.5';

            // 设置拖拽数据
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
            e.dataTransfer.setData('text/plain', this.dataset.imageId);
        });

        // 拖拽结束
        item.addEventListener('dragend', function(e) {
            console.log('拖拽结束');
            this.style.opacity = '1';
            draggedElement = null;

            // 延迟重置标志，避免影响其他拖拽操作
            setTimeout(() => {
                isImageSorting = false;
            }, 100);
        });

        // 拖拽经过
        item.addEventListener('dragover', function(e) {
            if (draggedElement && draggedElement !== this) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';

                // 添加视觉反馈
                this.style.borderLeft = '3px solid #007bff';
            }
        });

        // 拖拽离开
        item.addEventListener('dragleave', function(e) {
            this.style.borderLeft = '';
        });

        // 放置
        item.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderLeft = '';

            if (draggedElement && draggedElement !== this) {
                console.log('放置图片，交换位置');

                // 获取两个元素的位置
                const draggedIndex = Array.from(this.parentNode.children).indexOf(draggedElement);
                const targetIndex = Array.from(this.parentNode.children).indexOf(this);

                console.log('拖拽元素索引:', draggedIndex, '目标元素索引:', targetIndex);

                // 交换DOM元素位置
                if (draggedIndex < targetIndex) {
                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                } else {
                    this.parentNode.insertBefore(draggedElement, this);
                }

                // 更新排序号显示
                updateOrderBadges();

                // 保存新的排序到服务器
                saveImageOrder();
            }
        });
    });
}

// 更新排序号显示
function updateOrderBadges() {
    const imageItems = document.querySelectorAll('.image-item');
    imageItems.forEach((item, index) => {
        const badge = item.querySelector('.order-badge');
        if (badge) {
            badge.textContent = index + 1;
        }
        // 更新data属性
        item.dataset.imageOrder = index + 1;
    });
}

// 保存图片排序到服务器
function saveImageOrder() {
    if (!currentContentId) {
        console.error('没有当前文案ID，无法保存排序');
        return;
    }

    console.log('=== 保存图片排序 ===');

    const imageItems = document.querySelectorAll('.image-item');
    const orderData = [];

    imageItems.forEach((item, index) => {
        orderData.push({
            id: parseInt(item.dataset.imageId),
            order: index + 1
        });
    });

    console.log('排序数据:', orderData);

    // 发送到服务器（使用现有的API格式）
    fetch('/simple/api/images/reorder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            image_orders: orderData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ 图片排序保存成功');
            // 可以显示成功提示
            showToast('图片排序已保存', 'success');
        } else {
            console.error('❌ 图片排序保存失败:', data.message);
            showToast('排序保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存排序时发生错误:', error);
        showToast('排序保存失败', 'error');
    });
}

// 简单的提示函数
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.textContent = message;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

console.log('图片上传页面JavaScript执行完成');
</script>

<!-- 驳回理由侧边栏 -->
<div id="rejectionSidebar" class="rejection-sidebar d-none">
    <div class="rejection-sidebar-header">
        <h6 class="mb-0">
            <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
            <span id="rejectionSidebarTitle">驳回提醒</span>
        </h6>
        <button type="button" class="btn-close btn-close-white" id="closeSidebar" aria-label="关闭"></button>
    </div>
    <div class="rejection-sidebar-body">
        <div id="rejectionSidebarContent">
            <!-- 驳回理由内容将通过JavaScript动态加载 -->
        </div>
    </div>
</div>

<!-- 驳回理由侧边栏样式 -->
<style>
.rejection-sidebar {
    position: fixed;
    top: 50%;
    left: 20px;
    width: 280px;
    max-height: 500px;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    z-index: 1060; /* 高于模态框 */
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.1);
    transform: translateY(-50%) translateX(-100%);
    transition: transform 0.3s ease-in-out;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    user-select: none;
}

.rejection-sidebar.show {
    transform: translateY(-50%) translateX(0);
}

.rejection-sidebar.dragging {
    transition: none;
}

.rejection-sidebar-header {
    padding: 0.75rem 1rem;
    background: rgba(0,0,0,0.2);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    cursor: move;
    position: relative;
}

.rejection-sidebar-header::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 3px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    box-shadow: 0 4px 0 rgba(255,255,255,0.3), 0 8px 0 rgba(255,255,255,0.3);
}

.rejection-sidebar-header h6 {
    color: white;
    margin: 0;
    font-size: 0.9rem;
    flex-grow: 1;
    pointer-events: none;
}

.rejection-sidebar-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
    width: 1rem;
    height: 1rem;
    cursor: pointer;
    z-index: 10;
}

.rejection-sidebar-header .btn-close:hover {
    opacity: 1;
}

.rejection-sidebar-body {
    padding: 0.75rem;
    overflow-y: auto;
    flex-grow: 1;
}

.rejection-sidebar .rejection-item {
    background: rgba(255,255,255,0.08);
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-left: 3px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.rejection-sidebar .rejection-item:last-child {
    margin-bottom: 0;
}

.rejection-sidebar .rejection-item.client-rejection {
    border-left-color: #17a2b8;
    background: rgba(23,162,184,0.1);
}

.rejection-sidebar .rejection-item.internal-rejection {
    border-left-color: #ffc107;
    background: rgba(255,193,7,0.1);
}

.rejection-sidebar .rejection-type-badge {
    background: rgba(255,255,255,0.15);
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    margin-bottom: 0.4rem;
    display: inline-block;
}

.rejection-sidebar .rejection-time {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-bottom: 0.4rem;
}

.rejection-sidebar .rejection-reason {
    font-size: 0.85rem;
    line-height: 1.3;
    margin-bottom: 0.4rem;
}

.rejection-sidebar .rejection-item small {
    font-size: 0.7rem;
    opacity: 0.9;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .rejection-sidebar {
        width: 260px;
        left: 10px;
    }
}

@media (max-width: 480px) {
    .rejection-sidebar {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }
}
</style>

<script>
// 在所有函数定义完成后，立即执行一次全局函数设置
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有函数都已定义后再设置全局函数
    if (typeof ensureGlobalFunctions === 'function') {
        ensureGlobalFunctions();
    } else {
        console.error('ensureGlobalFunctions函数未定义');
    }
});
</script>
