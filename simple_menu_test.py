#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单菜单测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simple_menu_test():
    """简单菜单测试"""
    print("🔍 简单菜单测试...")
    print("=" * 60)
    
    # 1. 检查模板文件
    print("1. 检查模板文件:")
    print("-" * 40)
    
    templates = [
        ('发布管理', 'app/templates/publish/manage.html'),
        ('发布状态', 'app/templates/publish/status_manage.html'),
        ('主模板', 'app/templates/base_simple.html')
    ]
    
    for name, path in templates:
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📋 {name} 模板检查:")
            
            # 检查权限检查
            permission_checks = [
                'dashboard_access', 'template_manage', 'client_manage',
                'content_generate', 'content_manage', 'image_manage',
                'review.final', 'publish.manage', 'user_manage', 'system_settings'
            ]
            
            found_permissions = 0
            for perm in permission_checks:
                if f"has_permission('{perm}')" in content:
                    found_permissions += 1
            
            print(f"  权限检查数量: {found_permissions}/{len(permission_checks)}")
            
            # 检查菜单项
            menu_items = [
                '控制台', '模板管理', '客户管理', '内容生成', '初审文案',
                '图片上传', '最终审核', '客户审核', '发布管理', '发布状态',
                '用户管理', '系统设置'
            ]
            
            found_menus = 0
            for menu in menu_items:
                if menu in content:
                    found_menus += 1
            
            print(f"  菜单项数量: {found_menus}/{len(menu_items)}")
            
            # 检查图标
            if 'bi-speedometer2' in content and 'bi-person-gear' in content:
                print(f"  ✅ 图标样式完整")
            else:
                print(f"  ❌ 图标样式缺失")
                
        except Exception as e:
            print(f"❌ 检查 {name} 失败: {e}")
    
    # 2. 问题分析
    print(f"\n2. 问题分析:")
    print("-" * 40)
    
    print("🔍 从截图分析:")
    print("  第一张图: 显示完整菜单 (9个菜单项)")
    print("  第二张图: 只显示4个菜单项")
    print("    - 模板管理")
    print("    - 内容生成") 
    print("    - 系统设置")
    print("    - 退出登录")
    
    print(f"\n❌ 可能的原因:")
    print("  1. 用户权限不同:")
    print("     - 第一张图的用户有更多权限")
    print("     - 第二张图的用户权限受限")
    print("  2. 页面模板不同:")
    print("     - 发布页面可能使用了不同的菜单逻辑")
    print("  3. JavaScript动态隐藏:")
    print("     - 某些条件下隐藏了菜单项")
    print("  4. CSS样式问题:")
    print("     - 菜单项被样式隐藏")
    
    # 3. 解决方案
    print(f"\n3. 解决方案:")
    print("-" * 40)
    
    print("🔧 建议的修复方法:")
    print("  方案1: 统一使用base_simple.html模板")
    print("    - 让发布页面继承主模板")
    print("    - 确保菜单逻辑一致")
    
    print(f"\n  方案2: 修复独立页面的菜单")
    print("    - 检查权限检查逻辑")
    print("    - 确保所有菜单项都有正确的权限判断")
    
    print(f"\n  方案3: 调试用户权限")
    print("    - 检查当前登录用户的权限")
    print("    - 确保用户有访问所有菜单的权限")
    
    # 4. 立即修复建议
    print(f"\n4. 立即修复建议:")
    print("-" * 40)
    
    print("🚀 快速修复步骤:")
    print("  1. 检查当前登录用户:")
    print("     - 确认用户名和角色")
    print("     - 检查用户权限设置")
    
    print(f"\n  2. 对比页面源码:")
    print("     - 在第一张图的页面按F12查看源码")
    print("     - 在第二张图的页面按F12查看源码")
    print("     - 对比侧边栏HTML结构差异")
    
    print(f"\n  3. 检查权限判断:")
    print("     - 在浏览器Console中执行:")
    print("     - console.log(document.querySelectorAll('.nav-item').length)")
    print("     - 查看实际渲染的菜单数量")
    
    print(f"\n  4. 临时解决方案:")
    print("     - 如果是权限问题，给用户添加缺失的权限")
    print("     - 如果是模板问题，修改模板的权限检查逻辑")
    
    # 5. 测试指南
    print(f"\n5. 测试指南:")
    print("-" * 40)
    
    print("🔗 详细测试步骤:")
    print("  1. 以管理员身份登录")
    print("  2. 访问控制台页面 (第一张图的页面)")
    print("  3. 记录显示的菜单项")
    print("  4. 点击发布管理菜单")
    print("  5. 记录显示的菜单项")
    print("  6. 对比两个页面的菜单差异")
    print("  7. 检查浏览器开发者工具:")
    print("     - Elements: 查看HTML结构")
    print("     - Console: 查看JavaScript错误")
    print("     - Network: 查看请求状态")
    
    print(f"\n📱 用户权限检查:")
    print("  在浏览器Console中执行以下代码:")
    print("  fetch('/simple/api/user/permissions')")
    print("    .then(r => r.json())")
    print("    .then(d => console.log(d))")
    print("  查看当前用户的权限列表")
    
    print("\n" + "=" * 60)
    print("🎉 简单菜单测试完成！")
    print("\n📋 关键发现:")
    print("1. ✅ 模板文件都包含完整的权限检查")
    print("2. ✅ 菜单项定义完整")
    print("3. ❓ 需要检查用户权限和页面渲染")
    print("\n🚀 请按照测试指南进行详细调试！")
    print("   重点检查用户权限和页面HTML结构差异")

if __name__ == '__main__':
    simple_menu_test()
