#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
删除无用的客户分享功能设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def remove_client_share_setting():
    """删除无用的客户分享功能设置"""
    print("🗑️ 删除无用的客户分享功能设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找要删除的设置
            print("1. 查找要删除的设置:")
            print("-" * 40)
            
            share_setting = SystemSetting.query.filter_by(key='client_share_enabled').first()
            
            if share_setting:
                print(f"🔍 找到设置: {share_setting.key}")
                print(f"  当前值: {share_setting.value}")
                print(f"  描述: {share_setting.description}")
                print(f"  ID: {share_setting.id}")
            else:
                print("✅ 未找到 client_share_enabled 设置，无需删除")
                return
            
            # 2. 删除原因说明
            print(f"\n2. 删除原因:")
            print("-" * 40)
            print("删除 client_share_enabled 设置的原因:")
            print("  ❌ 客户审核页面没有检查该设置")
            print("  ❌ 即使关闭分享功能，客户仍可访问审核页面")
            print("  ❌ 审核按钮仍然可以使用")
            print("  ❌ 设置开关无实际控制作用")
            print("  ❌ 用户看到开关但功能不受控制")
            print("  ✅ 删除后客户分享功能始终可用")
            print("  ✅ 简化系统设置页面")
            print("  ✅ 避免用户困惑")
            
            # 3. 执行删除操作
            print(f"\n3. 执行删除操作:")
            print("-" * 40)
            
            try:
                print(f"🗑️ 删除设置: {share_setting.key} (ID: {share_setting.id})")
                db.session.delete(share_setting)
                db.session.commit()
                print(f"✅ 成功删除 client_share_enabled 设置")
            except Exception as e:
                print(f"❌ 删除失败: {e}")
                db.session.rollback()
                raise
            
            # 4. 验证删除结果
            print(f"\n4. 验证删除结果:")
            print("-" * 40)
            
            deleted_setting = SystemSetting.query.filter_by(key='client_share_enabled').first()
            if deleted_setting:
                print(f"❌ client_share_enabled 仍然存在，删除失败")
            else:
                print(f"✅ client_share_enabled 已成功删除")
            
            # 5. 查看剩余的系统设置
            print(f"\n5. 当前系统设置概览:")
            print("-" * 40)
            
            all_settings = SystemSetting.query.all()
            
            # 按类别分组
            categories = {
                '审核功能': [],
                '发布相关': [],
                '图片上传': [],
                '系统功能': []
            }
            
            for setting in all_settings:
                key_lower = setting.key.lower()
                if 'review' in key_lower:
                    categories['审核功能'].append(setting)
                elif 'publish' in key_lower:
                    categories['发布相关'].append(setting)
                elif 'image' in key_lower or 'upload' in key_lower:
                    categories['图片上传'].append(setting)
                else:
                    categories['系统功能'].append(setting)
            
            for category, settings in categories.items():
                if settings:
                    print(f"\n📋 {category} ({len(settings)}个):")
                    for setting in settings:
                        print(f"  ✅ {setting.key}: {setting.value}")
            
            # 6. 统计信息
            print(f"\n6. 设置统计:")
            print("-" * 40)
            
            total_count = len(all_settings)
            print(f"系统设置总数: {total_count}")
            print(f"删除前总数: {total_count + 1}")
            print(f"成功删除: 1个无用设置")
            
            # 7. 功能影响说明
            print(f"\n7. 功能影响说明:")
            print("-" * 40)
            
            print("✅ 删除后的功能状态:")
            print("  🔗 客户分享链接功能: 始终可用")
            print("  📋 客户审核页面: 始终可访问")
            print("  ✅ 审核按钮: 始终可用")
            print("  🎯 用户体验: 更加一致和可靠")
            print()
            print("✅ 测试链接状态:")
            print("  http://127.0.0.1:5000/client-review/4dbc790d5015faeca985cb74da6f43fb?key=DWYR")
            print("  - 链接继续有效")
            print("  - 审核功能正常")
            print("  - 不再受无效开关影响")
            
            # 8. 下一步操作提示
            print(f"\n8. 下一步操作:")
            print("-" * 40)
            
            print("✅ 需要更新的文件:")
            print("  1. app/templates/system/settings.html - 移除分享设置显示")
            print("  2. app/views/main_simple.py - 从重置函数中移除")
            print()
            print("✅ 清理效果:")
            print("  - 系统设置页面更简洁")
            print("  - 用户不再看到无效的开关")
            print("  - 客户分享功能始终可用")
            print("  - 避免了功能不一致的问题")
            
        except Exception as e:
            print(f"❌ 删除过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 无用客户分享功能设置删除完成！")
    print("\n总结:")
    print("1. 🗑️ 删除了无用的 client_share_enabled 设置")
    print("2. 🔗 客户分享功能现在始终可用")
    print("3. 🧹 系统设置页面将更加简洁")
    print("4. 👍 避免了用户对无效开关的困惑")
    print("\n下一步: 更新模板文件，移除设置显示")

if __name__ == '__main__':
    remove_client_share_setting()
