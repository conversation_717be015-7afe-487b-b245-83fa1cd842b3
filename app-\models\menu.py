"""
菜单模型
"""
from datetime import datetime
from . import db

class MenuItem(db.Model):
    """菜单项模型"""
    __tablename__ = 'menu_items'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    url = db.Column(db.String(200), nullable=False)
    icon = db.Column(db.String(50))
    permission = db.Column(db.String(50))  # 所需权限
    parent_id = db.Column(db.Integer, db.ForeignKey('menu_items.id'), nullable=True)
    order = db.Column(db.Integer, default=0)  # 排序
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 父子关系
    children = db.relationship('MenuItem', 
                              backref=db.backref('parent', remote_side=[id]),
                              cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<MenuItem {self.name}>'
    
    @staticmethod
    def get_user_menu_items(user):
        """获取用户可见的菜单项"""
        # 获取所有活跃的菜单项
        all_items = MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()
        
        # 过滤出用户有权限的菜单项
        user_items = []
        for item in all_items:
            # 如果没有设置权限要求，或者用户有对应权限
            if not item.permission or user.has_permission(item.permission):
                user_items.append(item)
        
        return user_items
    
    @staticmethod
    def build_menu_tree(user):
        """构建用户的菜单树"""
        # 获取用户可见的菜单项
        user_items = MenuItem.get_user_menu_items(user)
        
        # 构建菜单树
        menu_tree = []
        item_map = {item.id: item for item in user_items}
        
        # 找出顶级菜单项
        for item in user_items:
            if item.parent_id is None:
                menu_tree.append(item)
            elif item.parent_id in item_map:
                # 如果父菜单项在用户可见菜单中，添加为子菜单
                parent = item_map[item.parent_id]
                if not hasattr(parent, 'visible_children'):
                    parent.visible_children = []
                parent.visible_children.append(item)
        
        return menu_tree
