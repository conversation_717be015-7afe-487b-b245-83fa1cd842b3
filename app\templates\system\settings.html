<!-- 系统设置页面 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-gear"></i> 系统设置</h2>
            <p class="text-muted">配置系统的各项参数和功能开关</p>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-outline-warning" onclick="window.resetSettings ? window.resetSettings() : resetSettings()">
                <i class="bi bi-arrow-clockwise"></i> 重置为默认值
            </button>
            <button type="button" class="btn btn-primary" onclick="window.saveAllSettings ? window.saveAllSettings() : saveAllSettings()">
                <i class="bi bi-check"></i> 保存所有设置
            </button>
        </div>
    </div>

    <!-- 工作流程设置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-diagram-3"></i> 工作流程设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in workflow_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">
                                        {% if setting.key == 'ENABLE_FIRST_REVIEW' %}
                                            是否启用初审
                                        {% elif setting.key == 'ENABLE_FINAL_REVIEW' %}
                                            是否启用最终审核
                                        {% else %}
                                            {{ setting.description or setting.key }}
                                        {% endif %}
                                    </h6>
                                    <p class="card-text text-muted small">
                                        {% if setting.key == 'ENABLE_FIRST_REVIEW' %}
                                            关闭后，生成的文案将直接进入图片上传阶段
                                        {% elif setting.key == 'ENABLE_FINAL_REVIEW' %}
                                            关闭后，图片上传后将直接进入客户审核阶段
                                        {% else %}
                                            {{ setting.description }}
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           {% if setting.value == '1' %}checked{% endif %}>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 图片上传设置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-image"></i> 图片上传设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in upload_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                    图片最大大小
                                {% elif setting.key == 'IMAGE_UPLOAD_ALLOWED_TYPES' %}
                                    允许的图片格式
                                {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                    每篇文案最大图片数量
                                {% else %}
                                    {{ setting.description or setting.key }}
                                {% endif %}
                            </h6>
                            <p class="card-text text-muted small">
                                {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                    单个图片文件的最大大小（字节）
                                {% elif setting.key == 'IMAGE_UPLOAD_ALLOWED_TYPES' %}
                                    支持的图片文件格式，用逗号分隔
                                {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                    每篇文案最多可以上传的图片数量
                                {% else %}
                                    {{ setting.description }}
                                {% endif %}
                            </p>
                            
                            {% if setting.key == 'IMAGE_UPLOAD_MAX_SIZE' %}
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control" 
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           value="{{ (setting.value|int / 1048576)|round(1) }}"
                                           min="1" max="100" step="0.1">
                                    <span class="input-group-text">MB</span>
                                </div>
                                <small class="text-muted">当前: {{ (setting.value|int / 1048576)|round(1) }} MB</small>
                            {% elif setting.key == 'MAX_IMAGES_PER_CONTENT' %}
                                <input type="number" 
                                       class="form-control" 
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}"
                                       min="1" max="20">
                            {% else %}
                                <input type="text" 
                                       class="form-control" 
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}">
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 其他设置 -->
    {% if other_settings %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-sliders"></i> 其他设置</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for setting in other_settings %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                    客户分享链接有效期
                                {% elif setting.key == 'auto_publish_enabled' %}
                                    是否启用自动发布
                                {% elif setting.key == 'client_share_enabled' %}
                                    是否启用客户分享功能
                                {% elif setting.key == 'content_backup_enabled' %}
                                    是否启用文案备份
                                {% elif setting.key == 'notification_enabled' %}
                                    是否启用通知功能
                                {% elif setting.key == 'default_content_count' %}
                                    默认每日文案数量
                                {% elif setting.key == 'default_publish_interval_max' %}
                                    默认发布间隔最大值（分钟）
                                {% elif setting.key == 'default_publish_interval_min' %}
                                    默认发布间隔最小值（分钟）
                                {% else %}
                                    {{ setting.description or setting.key }}
                                {% endif %}
                            </h6>
                            <p class="card-text text-muted small">
                                {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                    客户审核分享链接的有效期（天）
                                {% elif setting.key == 'auto_publish_enabled' %}
                                    开启后，文案审核通过后将自动发布
                                {% elif setting.key == 'client_share_enabled' %}
                                    开启后，客户可以通过分享链接审核文案
                                {% elif setting.key == 'content_backup_enabled' %}
                                    开启后，系统将自动备份文案内容
                                {% elif setting.key == 'notification_enabled' %}
                                    开启后，系统将发送各种通知消息
                                {% elif setting.key == 'default_content_count' %}
                                    新客户的默认每日文案数量
                                {% elif setting.key == 'default_publish_interval_max' %}
                                    新客户的默认发布间隔最大值（分钟）
                                {% elif setting.key == 'default_publish_interval_min' %}
                                    新客户的默认发布间隔最小值（分钟）
                                {% else %}
                                    {{ setting.description }}
                                {% endif %}
                            </p>
                            
                            {% if setting.key == 'CLIENT_SHARE_LINK_EXPIRES_DAYS' %}
                                <div class="input-group">
                                    <input type="number"
                                           class="form-control"
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           value="{{ setting.value }}"
                                           min="1" max="365">
                                    <span class="input-group-text">天</span>
                                </div>
                            {% elif setting.key in ['auto_publish_enabled', 'client_share_enabled', 'content_backup_enabled', 'notification_enabled'] %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">{{ '启用' if setting.value == '1' else '禁用' }}</span>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="{{ setting.key }}"
                                               data-setting-key="{{ setting.key }}"
                                               {% if setting.value == '1' or setting.value == 'true' %}checked{% endif %}>
                                    </div>
                                </div>
                            {% elif setting.key in ['default_content_count', 'default_publish_interval_max', 'default_publish_interval_min'] %}
                                <div class="input-group">
                                    <input type="number"
                                           class="form-control"
                                           id="{{ setting.key }}"
                                           data-setting-key="{{ setting.key }}"
                                           value="{{ setting.value }}"
                                           min="1"
                                           {% if setting.key == 'default_content_count' %}max="50"
                                           {% elif setting.key == 'default_publish_interval_max' %}max="600"
                                           {% elif setting.key == 'default_publish_interval_min' %}max="300"
                                           {% endif %}>
                                    <span class="input-group-text">
                                        {% if setting.key == 'default_content_count' %}篇
                                        {% else %}分钟
                                        {% endif %}
                                    </span>
                                </div>
                            {% else %}
                                <input type="text"
                                       class="form-control"
                                       id="{{ setting.key }}"
                                       data-setting-key="{{ setting.key }}"
                                       value="{{ setting.value }}">
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 设置说明 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="bi bi-info-circle"></i> 设置说明</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>工作流程说明</h6>
                    <ul class="list-unstyled">
                        <li><strong>启用初审：</strong> 文案生成后需要经过初审才能进入图片上传</li>
                        <li><strong>启用最终审核：</strong> 图片上传后需要经过最终审核才能进入客户审核</li>
                        <li><strong>客户审核：</strong> 在客户管理中可以设置客户是否需要审核</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>图片上传说明</h6>
                    <ul class="list-unstyled">
                        <li><strong>文件大小：</strong> 建议设置为5-10MB，过大会影响上传速度</li>
                        <li><strong>文件格式：</strong> 支持常见的图片格式，建议包含jpg,png,gif</li>
                        <li><strong>图片数量：</strong> 每篇文案的最大图片数量，建议6-9张</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmButton">确认</button>
            </div>
        </div>
    </div>
</div>

<script>
// 系统设置页面脚本
console.log('系统设置页面脚本开始执行...');

// 保存所有设置
function saveAllSettings() {
    const settings = {};
    
    // 收集所有设置值
    document.querySelectorAll('[data-setting-key]').forEach(element => {
        const key = element.getAttribute('data-setting-key');
        let value;
        
        if (element.type === 'checkbox') {
            value = element.checked ? '1' : '0';
        } else if (key === 'IMAGE_UPLOAD_MAX_SIZE') {
            // 将MB转换为字节
            value = Math.round(parseFloat(element.value) * 1048576).toString();
        } else {
            value = element.value;
        }
        
        settings[key] = value;
    });
    
    // 发送请求
    fetch('/simple/api/system/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('保存设置失败: ' + error.message, 'error');
    });
}

// 重置设置
function resetSettings() {
    showConfirm('确定要重置所有设置为默认值吗？此操作不可撤销。', function() {
        fetch('/simple/api/system/settings/reset', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('重置设置失败: ' + error.message, 'error');
        });
    });
}

// 显示确认对话框
function showConfirm(message, callback) {
    document.getElementById('confirmMessage').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    
    document.getElementById('confirmButton').onclick = function() {
        modal.hide();
        callback();
    };
    
    modal.show();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建Toast元素
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';

    const toastHtml = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
    toast.show();

    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 创建Toast容器
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// 移除之前的事件监听器（如果存在）
if (window.systemSettingsChangeHandler) {
    document.removeEventListener('change', window.systemSettingsChangeHandler);
}

// 定义事件处理函数
window.systemSettingsChangeHandler = function(e) {
    if (e.target.hasAttribute('data-setting-key')) {
        const key = e.target.getAttribute('data-setting-key');
        let value;

        if (e.target.type === 'checkbox') {
            value = e.target.checked ? '1' : '0';
        } else if (key === 'IMAGE_UPLOAD_MAX_SIZE') {
            value = Math.round(parseFloat(e.target.value) * 1048576).toString();
        } else {
            value = e.target.value;
        }

        // 特殊处理：最终审核开关关闭时的确认
        if (key === 'ENABLE_FINAL_REVIEW' && value === '0') {
            handleFinalReviewDisable(e.target, key, value);
            return;
        }

        // 自动保存单个设置
        saveSingleSetting(key, value);
    }
};

// 添加事件监听器
document.addEventListener('change', window.systemSettingsChangeHandler);

// 保存单个设置的通用函数
function saveSingleSetting(key, value) {
    fetch(`/simple/api/system/settings/${key}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({ value: value })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`设置 ${key} 已保存`);
            showToast(`设置已保存`, 'success');
        } else {
            console.error(`保存设置 ${key} 失败:`, data.message);
            showToast(`保存失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error(`保存设置 ${key} 失败:`, error);
        showToast(`保存失败: ${error.message}`, 'error');
    });
}

// 处理最终审核开关关闭
function handleFinalReviewDisable(switchElement, key, value) {
    // 先检查是否有待最终审核的文案
    fetch('/simple/api/system/check-pending-final-review', {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.pending_count > 0) {
                // 有待审核的文案，显示确认对话框
                showFinalReviewConfirm(data.pending_count, function(action) {
                    if (action === 'approve') {
                        // 用户选择自动通过
                        processPendingFinalReview('approve', key, value);
                    } else if (action === 'keep') {
                        // 用户选择保持现状，但仍然关闭开关
                        saveSingleSetting(key, value);
                    } else {
                        // 用户取消，恢复开关状态
                        switchElement.checked = true;
                    }
                });
            } else {
                // 没有待审核的文案，直接保存
                saveSingleSetting(key, value);
            }
        } else {
            showToast('检查待审核文案失败，请重试', 'error');
            // 恢复开关状态
            switchElement.checked = true;
        }
    })
    .catch(error => {
        console.error('检查待审核文案失败:', error);
        showToast('检查待审核文案失败，请重试', 'error');
        // 恢复开关状态
        switchElement.checked = true;
    });
}

// 显示最终审核确认对话框
function showFinalReviewConfirm(pendingCount, callback) {
    const modalHtml = `
        <div class="modal fade" id="finalReviewConfirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle text-warning"></i>
                            关闭最终审核确认
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-3">
                            <strong>检测到有 ${pendingCount} 篇文案正在等待最终审核。</strong>
                        </p>
                        <p class="mb-3">关闭最终审核功能后，这些文案将如何处理？</p>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>选项说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>自动通过审核：</strong>所有待最终审核的文案将自动通过，进入客户审核阶段</li>
                                <li><strong>保持现状：</strong>这些文案将保持在最终审核状态，需要手动处理</li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-warning" id="keepStatusBtn">保持现状</button>
                        <button type="button" class="btn btn-success" id="autoApproveBtn">自动通过审核</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('finalReviewConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('finalReviewConfirmModal'));

    // 绑定按钮事件
    document.getElementById('autoApproveBtn').onclick = function() {
        modal.hide();
        callback('approve');
    };

    document.getElementById('keepStatusBtn').onclick = function() {
        modal.hide();
        callback('keep');
    };

    // 取消按钮和关闭按钮
    document.querySelector('#finalReviewConfirmModal .btn-close').onclick = function() {
        modal.hide();
        callback('cancel');
    };

    document.querySelector('#finalReviewConfirmModal .btn-secondary').onclick = function() {
        modal.hide();
        callback('cancel');
    };

    modal.show();
}

// 处理待最终审核的文案
function processPendingFinalReview(action, settingKey, settingValue) {
    showToast('正在处理待审核文案...', 'info');

    fetch('/simple/api/system/process-pending-final-review', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({
            action: action,
            setting_key: settingKey,
            setting_value: settingValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (action === 'approve') {
                showToast(`已自动通过 ${data.processed_count} 篇文案，文案已转入客户审核阶段`, 'success');
            } else {
                showToast(`成功处理 ${data.processed_count} 篇文案，设置已保存`, 'success');
            }
        } else {
            showToast(`处理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        showToast(`处理失败，请重试`, 'error');
    });
}

// 获取CSRF令牌
function getCSRFToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
}

// 确保函数在全局作用域中可访问
window.saveAllSettings = saveAllSettings;
window.resetSettings = resetSettings;
window.showConfirm = showConfirm;
window.showToast = showToast;

// 绑定按钮事件（备用方案）
document.addEventListener('DOMContentLoaded', function() {
    const saveBtn = document.querySelector('button[onclick*="saveAllSettings"]');
    const resetBtn = document.querySelector('button[onclick*="resetSettings"]');

    if (saveBtn) {
        saveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof saveAllSettings === 'function') {
                saveAllSettings();
            } else {
                console.error('saveAllSettings function not found');
            }
        });
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof resetSettings === 'function') {
                resetSettings();
            } else {
                console.error('resetSettings function not found');
            }
        });
    }
});

console.log('系统设置页面已加载');
console.log('函数已注册到全局作用域:', {
    saveAllSettings: typeof window.saveAllSettings,
    resetSettings: typeof window.resetSettings,
    showConfirm: typeof window.showConfirm,
    showToast: typeof window.showToast
});
</script>
