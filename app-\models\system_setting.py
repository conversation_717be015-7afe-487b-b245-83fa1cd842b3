#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统设置模型
"""

from datetime import datetime
from app.models import db

class SystemSetting(db.Model):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), nullable=False, unique=True, comment='设置键名')
    value = db.Column(db.Text, comment='设置值')
    description = db.Column(db.String(200), comment='设置描述')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='更新人')
    
    # 关联关系
    updater = db.relationship('User', backref=db.backref('updated_settings', lazy='dynamic'))
    
    def __repr__(self):
        return f'<SystemSetting {self.key}: {self.value}>'
    
    @classmethod
    def get_value(cls, key, default=None):
        """获取设置值"""
        setting = cls.query.filter_by(key=key).first()
        return setting.value if setting else default
    
    @classmethod
    def set_value(cls, key, value, description=None, updated_by=None):
        """设置值"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            if description:
                setting.description = description
            if updated_by:
                setting.updated_by = updated_by
            setting.updated_at = datetime.now()
        else:
            setting = cls(
                key=key,
                value=value,
                description=description,
                updated_by=updated_by,
                updated_at=datetime.now()
            )
            db.session.add(setting)
        
        db.session.commit()
        return setting
    
    @classmethod
    def get_all_settings(cls):
        """获取所有设置"""
        return cls.query.order_by(cls.key).all()
    
    @classmethod
    def get_settings_dict(cls):
        """获取所有设置的字典格式"""
        settings = cls.get_all_settings()
        return {setting.key: setting.value for setting in settings}
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }
