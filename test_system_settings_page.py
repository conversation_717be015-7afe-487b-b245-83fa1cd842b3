#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试系统设置页面恢复
"""

import requests

def test_system_settings_page():
    """测试系统设置页面"""
    print("⚙️ 测试系统设置页面恢复...")
    print("=" * 60)
    
    try:
        # 测试系统设置页面
        print("1. 测试系统设置页面...")
        page_url = 'http://127.0.0.1:5000/simple/system'
        response = requests.get(page_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 系统设置页面加载成功")
            
            # 检查页面内容
            if '系统设置' in response.text:
                print("✅ 找到系统设置标题")
            else:
                print("❌ 未找到系统设置标题")
            
            # 检查是否是控制台页面（错误的页面）
            if '控制台' in response.text or 'dashboard' in response.text.lower():
                print("❌ 显示的是控制台页面（错误）")
            else:
                print("✅ 不是控制台页面")
            
            # 检查系统设置相关元素
            settings_elements = [
                'workflow_settings',  # 工作流设置
                'upload_settings',    # 上传设置
                'other_settings',     # 其他设置
                'ENABLE_FIRST_REVIEW', # 启用初审
                'ENABLE_FINAL_REVIEW', # 启用终审
                'API_KEY',            # API密钥
                'PUBLISH_TIMEOUT'     # 发布超时
            ]
            
            found_elements = 0
            for element in settings_elements:
                if element in response.text:
                    found_elements += 1
            
            print(f"✅ 找到 {found_elements}/{len(settings_elements)} 个系统设置元素")
            
            # 检查表单元素
            if '<form' in response.text:
                print("✅ 找到表单元素")
            else:
                print("❌ 未找到表单元素")
            
            # 检查保存按钮
            if '保存' in response.text or 'save' in response.text.lower():
                print("✅ 找到保存按钮")
            else:
                print("❌ 未找到保存按钮")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
        
        # 测试系统设置API
        print("\n2. 测试系统设置API...")
        api_url = 'http://127.0.0.1:5000/simple/api/system/settings'
        response = requests.get(api_url, timeout=10)
        print(f"   API状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✅ 系统设置API正常")
                    settings_count = len(data.get('settings', []))
                    print(f"   找到 {settings_count} 个系统设置")
                else:
                    print("❌ 系统设置API返回失败")
            except:
                print("❌ API响应不是有效的JSON")
        else:
            print(f"❌ 系统设置API调用失败: {response.status_code}")
        
        # 检查控制台页面（确保没有混淆）
        print("\n3. 检查控制台页面...")
        console_url = 'http://127.0.0.1:5000/simple/'
        response = requests.get(console_url, timeout=10)
        print(f"   控制台页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            if '控制台' in response.text or 'dashboard' in response.text.lower():
                print("✅ 控制台页面正常")
            else:
                print("❌ 控制台页面异常")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 系统设置页面测试完成！")
    print("\n修复内容：")
    print("1. ✅ 修复了路由逻辑 - 普通请求返回系统设置页面")
    print("2. ✅ 移除了AJAX判断 - 直接返回系统设置模板")
    print("3. ✅ 保持了设置分组逻辑 - 工作流、上传、其他设置")
    print("\n修复前的问题：")
    print("- AJAX请求返回系统设置页面（错误）")
    print("- 普通请求返回控制台页面（错误）")
    print("\n修复后的逻辑：")
    print("- 所有请求都返回系统设置页面（正确）")
    print("- 系统设置按类别分组显示")
    print("- API接口独立处理设置的增删改查")

if __name__ == '__main__':
    test_system_settings_page()
