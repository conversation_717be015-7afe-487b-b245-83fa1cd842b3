#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试已发布筛选的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.content import Content

def debug_published_filter():
    """调试已发布筛选的问题"""
    print("🔍 调试已发布筛选的问题...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看测试客户的所有文案状态
            print("1. 查看测试客户的所有文案状态:")
            print("-" * 40)
            
            # 根据分享链接找到对应的客户ID
            from app.models.client import ClientShareLink
            share_link = ClientShareLink.query.filter_by(share_key='4dbc790d5015faeca985cb74da6f43fb').first()
            
            if share_link:
                client_id = share_link.client_id
                print(f"🔍 找到客户ID: {client_id}")
                
                # 查看该客户的所有文案
                contents = Content.query.filter_by(client_id=client_id, is_deleted=False).all()
                
                print(f"\n📋 客户的所有文案 (共{len(contents)}篇):")
                for content in contents:
                    print(f"  文案ID: {content.id}")
                    print(f"    标题: {content.title[:30]}...")
                    print(f"    工作流状态: {content.workflow_status}")
                    print(f"    客户审核状态: {content.client_review_status}")
                    print(f"    发布状态: {content.publish_status or 'None/未设置'}")
                    print()
                
                # 2. 测试不同筛选条件
                print("2. 测试不同筛选条件:")
                print("-" * 40)
                
                # 待审核筛选
                pending_query = Content.query.filter_by(client_id=client_id, is_deleted=False).filter(
                    Content.workflow_status == 'pending_client_review',
                    Content.client_review_status == 'pending'
                )
                pending_count = pending_query.count()
                print(f"📋 待审核筛选结果: {pending_count}篇")
                
                # 已通过筛选
                approved_query = Content.query.filter_by(client_id=client_id, is_deleted=False).filter(
                    Content.client_review_status == 'approved',
                    Content.publish_status != 'published'
                )
                approved_count = approved_query.count()
                print(f"📋 已通过筛选结果: {approved_count}篇")
                
                # 已发布筛选
                published_query = Content.query.filter_by(client_id=client_id, is_deleted=False).filter(
                    Content.publish_status == 'published'
                )
                published_count = published_query.count()
                print(f"📋 已发布筛选结果: {published_count}篇")
                
                if published_count > 0:
                    print("  已发布的文案:")
                    for content in published_query.all():
                        print(f"    - {content.title[:30]}... (ID: {content.id})")
                
                # 3. 检查问题文案的具体状态
                print(f"\n3. 检查问题文案的具体状态:")
                print("-" * 40)
                
                # 查找客户审核状态为approved的文案
                approved_contents = Content.query.filter_by(
                    client_id=client_id, 
                    is_deleted=False,
                    client_review_status='approved'
                ).all()
                
                if approved_contents:
                    print(f"🔍 找到{len(approved_contents)}篇已通过审核的文案:")
                    for content in approved_contents:
                        print(f"  文案ID: {content.id}")
                        print(f"    标题: {content.title[:30]}...")
                        print(f"    工作流状态: {content.workflow_status}")
                        print(f"    客户审核状态: {content.client_review_status}")
                        print(f"    发布状态: {content.publish_status or 'None/未设置'}")
                        
                        # 判断这篇文案在不同筛选下是否会显示
                        is_pending = (content.workflow_status == 'pending_client_review' and 
                                    content.client_review_status == 'pending')
                        is_approved = (content.client_review_status == 'approved' and 
                                     content.publish_status != 'published')
                        is_published = (content.publish_status == 'published')
                        
                        print(f"    筛选结果:")
                        print(f"      - 待审核筛选: {'✅ 会显示' if is_pending else '❌ 不显示'}")
                        print(f"      - 已通过筛选: {'✅ 会显示' if is_approved else '❌ 不显示'}")
                        print(f"      - 已发布筛选: {'✅ 会显示' if is_published else '❌ 不显示'}")
                        print()
                
                # 4. 分析问题原因
                print("4. 问题分析:")
                print("-" * 40)
                
                print("🔍 可能的问题原因:")
                print("  1. publish_status 字段为 None 或空值")
                print("  2. 筛选条件 publish_status != 'published' 可能包含了 None 值")
                print("  3. 需要明确排除 None 和空值")
                
                # 5. 检查 publish_status 的所有可能值
                print(f"\n5. 检查 publish_status 的所有可能值:")
                print("-" * 40)
                
                from sqlalchemy import distinct
                distinct_statuses = db.session.query(distinct(Content.publish_status)).filter_by(
                    client_id=client_id, is_deleted=False
                ).all()
                
                print("📋 该客户文案的所有 publish_status 值:")
                for status_tuple in distinct_statuses:
                    status = status_tuple[0]
                    count = Content.query.filter_by(
                        client_id=client_id, 
                        is_deleted=False, 
                        publish_status=status
                    ).count()
                    print(f"  - '{status}': {count}篇")
                
            else:
                print("❌ 未找到对应的分享链接")
                
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 调试完成！")

if __name__ == '__main__':
    debug_published_filter()
