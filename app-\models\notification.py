"""
通知相关模型
"""
from datetime import datetime
from . import db


class Notification(db.Model):
    """通知模型"""
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(30), nullable=False)  # 通知类型：review_status, client_operation, publish_notice, system_change
    related_content_id = db.Column(db.Integer, db.ForeignKey('contents.id'))  # 相关文案ID，可为NULL
    created_at = db.Column(db.DateTime, default=datetime.now, index=True)
    is_read = db.Column(db.Bo<PERSON>, default=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    priority = db.Column(db.String(10), default='normal')  # 优先级：high, normal, low
    
    # 关联
    recipient = db.relationship('User', backref=db.backref('notifications', lazy='dynamic'))
    related_content = db.relationship('Content', backref=db.backref('notifications', lazy='dynamic'))
    
    def __repr__(self):
        return f'<Notification {self.id}>' 