#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片显示功能
"""

import requests

def test_image_display():
    """测试图片显示功能"""
    print("🖼️ 测试图片显示功能...")
    print("=" * 50)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查页面是否包含图片相关的功能
            if 'enlargeImage' in response.text:
                print("✅ 找到图片放大功能")
            else:
                print("❌ 未找到图片放大功能")
            
            if 'imageModal' in response.text:
                print("✅ 找到图片放大弹窗")
            else:
                print("❌ 未找到图片放大弹窗")
            
            if 'images-' in response.text:
                print("✅ 找到图片数据容器")
            else:
                print("❌ 未找到图片数据容器")
            
            # 检查是否有详情按钮
            detail_button_count = response.text.count('详情</button>')
            print(f"✅ 找到 {detail_button_count} 个详情按钮")
            
            # 检查弹窗大小是否改为xl
            if 'modal-xl' in response.text:
                print("✅ 详情弹窗已扩大为xl尺寸")
            else:
                print("❌ 详情弹窗尺寸未更新")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 图片显示功能测试完成！")
    print("\n新增功能：")
    print("1. ✅ 详情弹窗右侧显示关联图片")
    print("2. ✅ 图片缩略图点击可放大查看")
    print("3. ✅ 图片放大弹窗支持全屏查看")
    print("4. ✅ 左右布局：左侧信息，右侧图片")
    print("5. ✅ 详情弹窗扩大为xl尺寸")
    print("\n使用方法：")
    print("1. 点击文案的'详情'按钮")
    print("2. 在弹窗右侧查看关联图片")
    print("3. 点击图片缩略图可放大查看")
    print("4. 点击放大弹窗的关闭按钮或外部区域关闭")

if __name__ == '__main__':
    test_image_display()
