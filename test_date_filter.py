#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试日期筛选功能
"""

import requests

def test_date_filter():
    """测试日期筛选功能"""
    print("📅 测试日期筛选功能...")
    print("=" * 60)
    
    try:
        # 测试基础页面
        print("1. 测试基础页面加载...")
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 基础页面加载失败")
            return
        
        print("✅ 基础页面加载成功")
        
        # 检查发布日期列
        if '发布日期' in response.text:
            print("✅ 找到发布日期列")
        else:
            print("❌ 未找到发布日期列")
        
        # 检查日期筛选下拉框
        if 'date-filter' in response.text:
            print("✅ 找到日期筛选下拉框")
        else:
            print("❌ 未找到日期筛选下拉框")
        
        # 检查日期筛选选项
        date_options = ['全部日期', '今天', '昨天', '本周', '上周']
        found_date_options = 0
        for option in date_options:
            if option in response.text:
                found_date_options += 1
        
        print(f"✅ 找到 {found_date_options}/{len(date_options)} 个日期筛选选项")
        
        # 检查筛选函数更新
        if 'dateFilter' in response.text:
            print("✅ 筛选函数已更新支持日期")
        else:
            print("❌ 筛选函数未更新支持日期")
        
        # 测试今天筛选
        print("\n2. 测试今天日期筛选...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?date_filter=today'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 今天筛选页面加载成功")
            # 检查状态按钮是否保持日期筛选
            if 'date_filter=today' in response.text:
                print("✅ 状态按钮保持今天筛选条件")
            else:
                print("❌ 状态按钮未保持今天筛选条件")
        
        # 测试组合筛选
        print("\n3. 测试组合筛选...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?status=pending&client_id=1&date_filter=today&priority=high'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 组合筛选页面加载成功")
            # 检查所有筛选条件是否保持
            conditions = ['status=published', 'client_id=1', 'date_filter=today', 'priority=high']
            found_conditions = 0
            for condition in conditions:
                if condition in response.text:
                    found_conditions += 1
            
            print(f"✅ 状态按钮保持 {found_conditions}/{len(conditions)} 个筛选条件")
        
        # 检查其他筛选组件
        print("\n4. 检查筛选组件完整性...")
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        if response.status_code == 200:
            # 检查客户筛选
            if 'client-filter' in response.text:
                print("✅ 客户筛选下拉框存在")
            else:
                print("❌ 客户筛选下拉框缺失")
            
            # 检查优先级筛选
            if 'priority-filter' in response.text:
                print("✅ 优先级筛选下拉框存在")
            else:
                print("❌ 优先级筛选下拉框缺失")
            
            # 检查筛选应用函数
            if 'applyFilters' in response.text:
                print("✅ 筛选应用函数存在")
            else:
                print("❌ 筛选应用函数缺失")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 日期筛选功能测试完成！")
    print("\n新增日期筛选功能：")
    print("1. ✅ 发布日期列 - 表格中显示文案的发布日期和时间")
    print("2. ✅ 日期筛选下拉框 - 支持今天、昨天、本周、上周筛选")
    print("3. ✅ 组合筛选支持 - 日期可与状态、客户、优先级组合使用")
    print("4. ✅ 筛选条件保持 - 切换状态时保持日期筛选条件")
    print("\n日期筛选选项：")
    print("- 全部日期：显示所有日期的文案")
    print("- 今天：只显示今天发布的文案")
    print("- 昨天：只显示昨天发布的文案")
    print("- 本周：显示本周（周一到周日）的文案")
    print("- 上周：显示上周（上周一到上周日）的文案")
    print("\n使用场景：")
    print("- 查看今天待发布的高优先级文案")
    print("- 查看昨天发布失败的特定客户文案")
    print("- 查看本周已发布的所有文案")
    print("- 查看上周特定客户的发布情况")

if __name__ == '__main__':
    test_date_filter()
