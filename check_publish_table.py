#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查publish_records表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.publish import PublishRecord

def check_publish_table():
    """检查publish_records表"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 检查publish_records表...")
            print("=" * 50)
            
            # 检查表是否存在
            from sqlalchemy import text
            result = db.session.execute(text("SHOW TABLES LIKE 'publish_records'"))
            tables = result.fetchall()
            
            if tables:
                print("✅ publish_records表存在")
                
                # 检查表结构
                result = db.session.execute(text("DESCRIBE publish_records"))
                columns = result.fetchall()
                
                print("\n表结构:")
                for col in columns:
                    print(f"  {col[0]}: {col[1]} {col[2]} {col[3]} {col[4]} {col[5]}")
                
                # 检查现有记录
                records = PublishRecord.query.all()
                print(f"\n现有记录数量: {len(records)}")
                
                if records:
                    print("最近的记录:")
                    for record in records[-5:]:  # 显示最后5条记录
                        print(f"  ID: {record.id}, content_id: {record.content_id}, status: {record.status}")
                
                # 尝试创建一个测试记录
                print("\n尝试创建测试记录...")
                test_record = PublishRecord(
                    content_id=1,  # 假设存在ID为1的文案
                    status='test',
                    platform='测试平台',
                    account='测试账号',
                    publish_url='https://test.com',
                    error_message='测试错误信息',
                    ext_info='{"test": true}'
                )
                
                db.session.add(test_record)
                db.session.commit()
                
                print("✅ 测试记录创建成功")
                
                # 删除测试记录
                db.session.delete(test_record)
                db.session.commit()
                print("✅ 测试记录已删除")
                
            else:
                print("❌ publish_records表不存在")
                
                # 尝试创建表
                print("尝试创建表...")
                db.create_all()
                print("✅ 表创建完成")
            
        except Exception as e:
            print(f"检查失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    check_publish_table()
