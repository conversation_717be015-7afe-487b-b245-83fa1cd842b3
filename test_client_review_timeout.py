#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试客户审核超时功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.client import Client
from app.models.content import Content
from app.services.client_review_timeout import ClientReviewTimeoutService
from datetime import datetime, timedelta, time

def test_client_review_timeout():
    """测试客户审核超时功能"""
    print("🧪 测试客户审核超时功能...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查客户表新字段
            print("1. 检查客户表新字段:")
            print("-" * 40)
            
            clients = Client.query.all()
            for client in clients:
                print(f"客户: {client.name}")
                print(f"  启用自动通过: {'是' if client.auto_approve_enabled else '否'}")
                print(f"  超时时间: {client.review_timeout_hours} 小时")
                print(f"  截止时间: {client.review_deadline_time}")
                print()
            
            # 2. 检查待审核文案
            print("2. 检查待审核文案:")
            print("-" * 40)
            
            pending_contents = Content.query.filter(
                Content.workflow_status == 'pending_client_review',
                Content.client_review_status == 'pending',
                Content.is_deleted == False
            ).all()
            
            print(f"当前待客户审核文案数量: {len(pending_contents)}")
            
            for content in pending_contents:
                client = Client.query.get(content.client_id)
                print(f"  文案ID: {content.id}")
                print(f"  客户: {client.name if client else '未知'}")
                print(f"  标题: {content.title}")
                print(f"  状态更新时间: {content.status_update_time}")
                
                if content.status_update_time:
                    time_diff = datetime.now() - content.status_update_time
                    hours_passed = time_diff.total_seconds() / 3600
                    print(f"  已等待: {hours_passed:.1f} 小时")
                print()
            
            # 3. 测试超时检查逻辑
            print("3. 测试超时检查逻辑:")
            print("-" * 40)
            
            if pending_contents:
                # 测试第一个文案的超时状态
                test_content = pending_contents[0]
                client = Client.query.get(test_content.client_id)
                
                if client:
                    print(f"测试文案: {test_content.title}")
                    print(f"所属客户: {client.name}")
                    
                    current_time = datetime.now()
                    
                    # 检查时间间隔超时
                    interval_timeout = ClientReviewTimeoutService._check_interval_timeout(
                        test_content, client, current_time
                    )
                    print(f"时间间隔超时: {'是' if interval_timeout else '否'}")
                    
                    # 检查截止时间超时
                    deadline_timeout = ClientReviewTimeoutService._check_deadline_timeout(
                        test_content, client, current_time
                    )
                    print(f"截止时间超时: {'是' if deadline_timeout else '否'}")
                    
                    # 获取超时状态
                    status = ClientReviewTimeoutService.get_timeout_status_for_client(client.id)
                    if status:
                        print(f"客户超时状态:")
                        print(f"  待审核数量: {status['pending_count']}")
                        print(f"  即将超时: {status['timeout_soon_count']}")
                        print(f"  已经超时: {status['already_timeout_count']}")
            
            # 4. 执行超时检查
            print(f"\n4. 执行超时检查:")
            print("-" * 40)
            
            processed_count = ClientReviewTimeoutService.check_and_process_timeouts()
            print(f"处理的超时文案数量: {processed_count}")
            
            # 5. 测试API接口
            print(f"\n5. API接口测试:")
            print("-" * 40)
            print("可以通过以下API接口测试功能:")
            print("  POST /api/check-client-review-timeouts")
            print("    - 检查并处理客户审核超时")
            print("  GET /api/client-review-timeout-status/<client_id>")
            print("    - 获取指定客户的超时状态")
            
            # 6. 定时任务说明
            print(f"\n6. 定时任务设置:")
            print("-" * 40)
            print("可以设置以下定时任务:")
            print("  Windows任务计划程序:")
            print("    python client_review_timeout_task.py")
            print("    建议每小时执行一次")
            print()
            print("  Linux cron:")
            print("    0 * * * * cd /path/to/project && python client_review_timeout_task.py")
            print("    每小时的0分执行")
            
            # 7. 功能特点
            print(f"\n7. 功能特点:")
            print("-" * 40)
            print("✅ 双重超时机制:")
            print("  - 时间间隔超时：从进入客户审核状态开始计算")
            print("  - 截止时间超时：每天到达截止时间自动通过")
            print()
            print("✅ 客户级别设置:")
            print("  - 每个客户可以有不同的超时规则")
            print("  - 可以单独开启/关闭自动通过功能")
            print()
            print("✅ 操作记录:")
            print("  - 自动通过操作会记录到文案历史")
            print("  - 包含超时原因和操作时间")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 客户审核超时功能测试完成！")
    print("\n使用说明:")
    print("1. 🔧 在客户编辑页面配置超时设置")
    print("2. ⏰ 设置定时任务定期检查超时")
    print("3. 📊 通过API接口监控超时状态")
    print("4. 📝 查看文案历史了解自动通过记录")

if __name__ == '__main__':
    test_client_review_timeout()
