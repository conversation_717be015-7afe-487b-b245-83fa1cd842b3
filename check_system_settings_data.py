#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查系统设置数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting

def check_system_settings_data():
    """检查系统设置数据"""
    print("🔍 检查系统设置数据...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取所有系统设置
            settings = SystemSetting.get_all_settings()
            print(f"数据库中的系统设置数量: {len(settings)}")
            
            if not settings:
                print("❌ 数据库中没有系统设置数据")
                print("正在创建默认系统设置...")
                
                # 创建默认设置
                default_settings = {
                    'ENABLE_FIRST_REVIEW': ('1', '是否启用初审'),
                    'ENABLE_FINAL_REVIEW': ('1', '是否启用最终审核'),
                    'IMAGE_UPLOAD_MAX_SIZE': ('10485760', '图片上传最大大小（字节）'),
                    'IMAGE_UPLOAD_ALLOWED_TYPES': ('jpg,jpeg,png,gif,webp', '允许的图片类型'),
                    'API_KEY': ('fETkRLwJQJ...', 'API密钥'),
                    'PUBLISH_TIMEOUT': ('120', '发布超时时间（秒）'),
                    'CLIENT_SHARE_LINK_EXPIRES_DAYS': ('30', '客户分享链接过期天数'),
                    'MAX_IMAGES_PER_CONTENT': ('9', '每个文案最大图片数量')
                }
                
                for key, (value, description) in default_settings.items():
                    SystemSetting.set_value(key, value, description)
                    print(f"✅ 创建设置: {key} = {value}")
                
                # 重新获取设置
                settings = SystemSetting.get_all_settings()
                print(f"创建后的系统设置数量: {len(settings)}")
            
            print("\n当前系统设置:")
            print("-" * 40)
            
            # 按类别分组
            workflow_settings = []
            upload_settings = []
            other_settings = []
            
            for setting in settings:
                print(f"{setting.key}: {setting.value} ({setting.description or '无描述'})")
                
                if setting.key.startswith('ENABLE_'):
                    workflow_settings.append(setting)
                elif setting.key.startswith('IMAGE_') or setting.key.startswith('MAX_'):
                    upload_settings.append(setting)
                else:
                    other_settings.append(setting)
            
            print(f"\n分组统计:")
            print(f"工作流设置: {len(workflow_settings)} 个")
            print(f"上传设置: {len(upload_settings)} 个")
            print(f"其他设置: {len(other_settings)} 个")
            
            # 测试模板渲染需要的数据
            print(f"\n模板数据准备:")
            print(f"workflow_settings: {[s.key for s in workflow_settings]}")
            print(f"upload_settings: {[s.key for s in upload_settings]}")
            print(f"other_settings: {[s.key for s in other_settings]}")
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 系统设置数据检查完成！")

if __name__ == '__main__':
    check_system_settings_data()
