"""
错误处理模块
"""

from datetime import datetime
from flask import render_template, request, jsonify


def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(e):
        """400错误处理"""
        if request.accept_mimetypes.accept_json and not request.accept_mimetypes.accept_html:
            return jsonify(error='Bad Request'), 400
        return render_template('errors/400.html', now=datetime.now()), 400
    
    @app.errorhandler(403)
    def forbidden(e):
        """403错误处理"""
        if request.accept_mimetypes.accept_json and not request.accept_mimetypes.accept_html:
            return jsonify(error='Forbidden'), 403
        return render_template('errors/403.html', now=datetime.now()), 403
    
    @app.errorhandler(404)
    def page_not_found(e):
        """404错误处理"""
        if request.accept_mimetypes.accept_json and not request.accept_mimetypes.accept_html:
            return jsonify(error='Not Found'), 404
        return render_template('errors/404.html', now=datetime.now()), 404
    
    @app.errorhandler(500)
    def internal_server_error(e):
        """500错误处理"""
        app.logger.error(f"Server Error: {e}")
        if request.accept_mimetypes.accept_json and not request.accept_mimetypes.accept_html:
            return jsonify(error='Internal Server Error'), 500
        return render_template('errors/500.html', now=datetime.now()), 500


class ApiError(Exception):
    """API错误类"""
    
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload
    
    def to_dict(self):
        """转换为字典"""
        rv = dict(self.payload or ())
        rv['message'] = self.message
        rv['error'] = True
        return rv


def register_api_error_handlers(app):
    """注册API错误处理器"""
    
    @app.errorhandler(ApiError)
    def handle_api_error(error):
        """API错误处理"""
        response = jsonify(error.to_dict())
        response.status_code = error.status_code
        return response 