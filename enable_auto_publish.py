#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启用自动发布功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def enable_auto_publish():
    """启用自动发布功能"""
    print("🚀 启用自动发布功能...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找自动发布设置
            print("1. 查找自动发布设置:")
            print("-" * 40)
            
            auto_publish_setting = SystemSetting.query.filter_by(key='auto_publish_enabled').first()
            
            if auto_publish_setting:
                print(f"🔍 找到设置: {auto_publish_setting.key}")
                print(f"  当前值: {auto_publish_setting.value}")
                print(f"  描述: {auto_publish_setting.description}")
            else:
                print("❌ 未找到自动发布设置")
                return
            
            # 2. 启用自动发布
            print(f"\n2. 启用自动发布:")
            print("-" * 40)
            
            old_value = auto_publish_setting.value
            auto_publish_setting.value = 'true'
            
            try:
                db.session.commit()
                print(f"✅ 自动发布已启用")
                print(f"  设置值: {old_value} → {auto_publish_setting.value}")
            except Exception as e:
                print(f"❌ 启用失败: {e}")
                db.session.rollback()
                return
            
            # 3. 验证设置
            print(f"\n3. 验证设置:")
            print("-" * 40)
            
            # 重新查询确认
            updated_setting = SystemSetting.query.filter_by(key='auto_publish_enabled').first()
            if updated_setting and updated_setting.value.lower() in ['true', '1']:
                print("✅ 自动发布功能已成功启用")
            else:
                print("❌ 自动发布功能启用失败")
                return
            
            # 4. 功能说明
            print(f"\n4. 功能说明:")
            print("-" * 40)
            
            print("🎯 启用自动发布后的工作流程:")
            print("  1. 客户在审核页面点击'通过'")
            print("  2. 文案状态直接变为 'pending_publish'")
            print("  3. 文案自动进入发布队列")
            print("  4. 等待第三方API获取并发布")
            print("  5. 跳过发布管理页面的手动提交步骤")
            print()
            print("✅ 优势:")
            print("  🚀 提高效率: 减少手动操作")
            print("  ⚡ 快速响应: 审核通过后立即进入发布队列")
            print("  🎯 自动化: 减少人工干预")
            print("  📈 提升体验: 更流畅的工作流程")
            
            # 5. 测试建议
            print(f"\n5. 测试建议:")
            print("-" * 40)
            
            print("🧪 建议测试步骤:")
            print("  1. 创建一篇测试文案")
            print("  2. 完成内部审核流程")
            print("  3. 让客户审核通过")
            print("  4. 检查文案是否直接进入待发布状态")
            print("  5. 在发布管理页面确认文案状态")
            print()
            print("🔗 相关页面:")
            print("  - 发布管理: http://127.0.0.1:5000/simple/publish-status-manage")
            print("  - 系统设置: http://127.0.0.1:5000/simple/system")
            print("  - 客户审核: http://127.0.0.1:5000/client-review/...")
            
            # 6. 注意事项
            print(f"\n6. 注意事项:")
            print("-" * 40)
            
            print("⚠️ 使用注意事项:")
            print("  1. 确保发布API配置正确")
            print("  2. 监控发布队列状态")
            print("  3. 处理发布失败的文案")
            print("  4. 如需要人工控制，可随时关闭此功能")
            print()
            print("🔧 如需关闭自动发布:")
            print("  - 访问系统设置页面")
            print("  - 关闭'自动发布'开关")
            print("  - 或者将设置值改为 'false'")
            
        except Exception as e:
            print(f"❌ 启用过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 自动发布功能启用完成！")
    print("\n📋 总结:")
    print("1. ✅ 自动发布设置已启用")
    print("2. ✅ 客户审核通过后将自动进入发布队列")
    print("3. ✅ 跳过手动提交发布的步骤")
    print("4. ✅ 提高了工作流程效率")
    print("\n🚀 现在客户审核通过的文案将自动进入待发布状态！")

if __name__ == '__main__':
    enable_auto_publish()
