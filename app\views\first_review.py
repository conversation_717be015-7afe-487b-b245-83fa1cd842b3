"""
初审管理视图
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import and_

from app.models import db
from app.models.content import Content, ContentHistory, RejectionReason
from app.forms.content import ContentReviewForm
from app.utils.decorators import permission_required

# 创建蓝图
first_review_bp = Blueprint('first_review', __name__, url_prefix='/first-review')

@first_review_bp.route('/')
@login_required
@permission_required('first_review.view')
def index():
    """初审管理首页"""
    # 获取所有待初审的文案
    contents = Content.query.filter(Content.workflow_status == 'draft').all()
    return render_template('first_review/index.html', contents=contents, title='初审管理')

@first_review_bp.route('/<int:content_id>')
@login_required
@permission_required('first_review.view')
def view(content_id):
    """查看待初审文案详情"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'draft':
        flash('该文案不在初审状态', 'warning')
        return redirect(url_for('first_review.index'))
    
    form = ContentReviewForm()
    return render_template('first_review/view.html', content=content, form=form, title='文案初审')

@first_review_bp.route('/<int:content_id>/approve', methods=['POST'])
@login_required
@permission_required('first_review.approve')
def approve(content_id):
    """初审通过"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'draft':
        flash('该文案不在初审状态', 'warning')
        return redirect(url_for('first_review.index'))
    
    # 更新文案状态为初审通过
    import logging
    logging.warning(f'DEBUG: first_review.py 审核开始 - workflow_status={content.workflow_status}, internal_review_status={content.internal_review_status}')

    # 检查是否是驳回状态，如果是，按驳回状态逻辑处理
    if content.internal_review_status == 'final_rej_text_ok':
        logging.warning(f'DEBUG: first_review.py 检测到 final_rej_text_ok 状态，进入驳回处理分支')
        # 驳回状态：图片已修复，文案审核通过，直接进入终审
        content.workflow_status = 'final_review'
        content.internal_review_status = 'pending'
    elif content.internal_review_status in ['final_rej_text', 'final_rej_both']:
        logging.warning(f'DEBUG: first_review.py 检测到其他驳回状态 {content.internal_review_status}，进入驳回处理分支')
        # 驳回状态：文案审核通过，直接进入终审
        content.workflow_status = 'final_review'
        content.internal_review_status = 'pending'
    else:
        logging.warning(f'DEBUG: first_review.py 进入正常初审分支，internal_review_status={content.internal_review_status}')
        # 正常初审通过 - 检查是否已有图片
        if content.image_urls and content.image_urls.strip() and content.image_urls != '[]':
            # 已有图片，根据系统设置决定下一步
            from app.models.system_setting import SystemSetting

            # 检查是否启用最终审核
            enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')

            if enable_final_review == '1':
                # 启用最终审核，进入最终审核阶段
                content.workflow_status = 'final_review'
                content.internal_review_status = 'pending'
                print(f"初审通过且已有图片，启用最终审核，状态设置为: final_review")
            else:
                # 关闭最终审核，直接进入客户审核阶段
                from app.models.client import Client
                client = Client.query.get(content.client_id)

                if client and client.need_review:
                    # 需要客户审核
                    content.workflow_status = 'pending_client_review'
                    content.client_review_status = 'pending'
                    content.internal_review_status = 'final_approved'
                    print(f"初审通过且已有图片，关闭最终审核，需要客户审核，状态设置为: pending_client_review")
                else:
                    # 不需要客户审核，直接进入待发布
                    content.workflow_status = 'pending_publish'
                    content.client_review_status = 'approved'
                    content.internal_review_status = 'final_approved'
                    print(f"初审通过且已有图片，关闭最终审核，不需要客户审核，状态设置为: pending_publish")
        else:
            # 没有图片，需要上传图片
            content.workflow_status = 'first_reviewed'
            content.internal_review_status = 'first_approved'

    content.reviewer_id = current_user.id
    content.review_time = db.func.now()
    
    db.session.commit()
    flash('文案初审已通过', 'success')
    return redirect(url_for('first_review.index'))

@first_review_bp.route('/<int:content_id>/reject', methods=['POST'])
@login_required
@permission_required('first_review.reject')
def reject(content_id):
    """初审拒绝"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'draft':
        flash('该文案不在初审状态', 'warning')
        return redirect(url_for('first_review.index'))
    
    form = ContentReviewForm()
    if form.validate_on_submit():
        # 记录拒绝理由
        reason = RejectionReason(
            content_id=content_id,
            reason=form.reason.data,
            created_by=current_user.id
        )
        db.session.add(reason)
        db.session.commit()
        
        flash('文案已被拒绝', 'info')
        return redirect(url_for('first_review.index'))
    
    flash('表单验证失败', 'danger')
    return redirect(url_for('first_review.view', content_id=content_id))

@first_review_bp.route('/<int:content_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('first_review.edit')
def edit(content_id):
    """编辑待初审文案"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'draft':
        flash('该文案不在初审状态', 'warning')
        return redirect(url_for('first_review.index'))
    
    if request.method == 'POST':
        # 保存文案历史版本
        history = ContentHistory(
            content_id=content.id,
            title=content.title,
            content=content.content,
            editor_id=current_user.id
        )
        db.session.add(history)
        
        # 更新文案内容
        content.title = request.form.get('title')
        content.content = request.form.get('content')
        content.topics = request.form.get('topics')
        content.location = request.form.get('location')
        
        db.session.commit()
        flash('文案已更新', 'success')
        return redirect(url_for('first_review.view', content_id=content_id))
    
    return render_template('first_review/edit.html', content=content, title='编辑文案')

@first_review_bp.route('/batch-approve', methods=['POST'])
@login_required
@permission_required('first_review.batch')
def batch_approve():
    """批量通过初审"""
    content_ids = request.form.getlist('content_ids')
    if not content_ids:
        flash('未选择任何文案', 'warning')
        return redirect(url_for('first_review.index'))
    
    contents = Content.query.filter(
        and_(
            Content.id.in_(content_ids),
            Content.workflow_status == 'draft'
        )
    ).all()
    
    for content in contents:
        # 批量初审通过 - 检查是否是驳回状态
        import logging
        logging.warning(f'DEBUG: first_review.py 批量审核 - workflow_status={content.workflow_status}, internal_review_status={content.internal_review_status}')

        if content.internal_review_status == 'final_rej_text_ok':
            logging.warning(f'DEBUG: first_review.py 批量审核检测到 final_rej_text_ok 状态，进入驳回处理分支')
            # 驳回状态：图片已修复，文案审核通过，直接进入终审
            content.workflow_status = 'final_review'
            content.internal_review_status = 'pending'
        elif content.internal_review_status in ['final_rej_text', 'final_rej_both']:
            logging.warning(f'DEBUG: first_review.py 批量审核检测到其他驳回状态 {content.internal_review_status}，进入驳回处理分支')
            # 驳回状态：文案审核通过，直接进入终审
            content.workflow_status = 'final_review'
            content.internal_review_status = 'pending'
        else:
            logging.warning(f'DEBUG: first_review.py 批量审核进入正常初审分支')
            # 正常初审通过 - 检查是否已有图片
            if content.image_urls and content.image_urls.strip() and content.image_urls != '[]':
                # 已有图片，直接进入终审
                content.workflow_status = 'final_review'
                content.internal_review_status = 'pending'
            else:
                # 没有图片，需要上传图片
                content.workflow_status = 'first_reviewed'
                content.internal_review_status = 'first_approved'

        content.reviewer_id = current_user.id
        content.review_time = db.func.now()
    
    db.session.commit()
    flash(f'已批量通过 {len(contents)} 篇文案的初审', 'success')
    return redirect(url_for('first_review.index')) 