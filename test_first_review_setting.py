#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试初审开关设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def test_first_review_setting():
    """测试初审开关设置"""
    print("🔧 测试初审开关设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查当前初审开关状态
            print("1. 检查当前初审开关状态:")
            print("-" * 40)
            
            enable_first_review = SystemSetting.get_value('ENABLE_FIRST_REVIEW', '1')
            print(f"当前初审开关状态: {enable_first_review}")
            print(f"含义: {'启用初审' if enable_first_review == '1' else '关闭初审'}")
            
            # 2. 测试开关切换
            print(f"\n2. 测试开关切换:")
            print("-" * 40)
            
            # 测试关闭初审
            print("测试关闭初审开关...")
            SystemSetting.set_value('ENABLE_FIRST_REVIEW', '0', '关闭初审功能')
            new_value = SystemSetting.get_value('ENABLE_FIRST_REVIEW')
            print(f"设置后的值: {new_value}")
            print(f"预期效果: 生成文案后直接进入图片上传阶段")
            
            # 测试开启初审
            print("\n测试开启初审开关...")
            SystemSetting.set_value('ENABLE_FIRST_REVIEW', '1', '启用初审功能')
            new_value = SystemSetting.get_value('ENABLE_FIRST_REVIEW')
            print(f"设置后的值: {new_value}")
            print(f"预期效果: 生成文案后进入初审阶段")
            
            # 3. 测试文案生成逻辑
            print(f"\n3. 测试文案生成逻辑:")
            print("-" * 40)
            
            # 模拟文案生成时的状态判断
            def get_initial_workflow_status():
                enable_first_review = SystemSetting.get_value('ENABLE_FIRST_REVIEW', '1')
                
                if enable_first_review == '1':
                    return 'pending_review'
                else:
                    return 'first_reviewed'
            
            # 测试启用初审的情况
            SystemSetting.set_value('ENABLE_FIRST_REVIEW', '1')
            status_enabled = get_initial_workflow_status()
            print(f"启用初审时，文案初始状态: {status_enabled}")
            print(f"  -> 文案会显示在: 初审文案页面 (http://127.0.0.1:5000/simple/review-content)")
            
            # 测试关闭初审的情况
            SystemSetting.set_value('ENABLE_FIRST_REVIEW', '0')
            status_disabled = get_initial_workflow_status()
            print(f"关闭初审时，文案初始状态: {status_disabled}")
            print(f"  -> 文案会显示在: 图片上传页面 (http://127.0.0.1:5000/simple/image-upload)")
            
            # 4. 工作流状态说明
            print(f"\n4. 工作流状态说明:")
            print("-" * 40)
            
            workflow_states = {
                'draft': '草稿状态',
                'pending_review': '待初审',
                'first_reviewed': '初审通过',
                'image_uploaded': '图片已上传',
                'pending_final_review': '待最终审核',
                'pending_client_review': '待客户审核',
                'pending_publish': '待发布',
                'published': '已发布'
            }
            
            for state, description in workflow_states.items():
                print(f"  {state}: {description}")
            
            # 5. 修复说明
            print(f"\n5. 修复说明:")
            print("-" * 40)
            print("修复内容:")
            print("  ✅ 在 generate_single_content 函数中添加了系统设置检查")
            print("  ✅ 根据 ENABLE_FIRST_REVIEW 设置确定文案初始状态")
            print("  ✅ 启用初审: workflow_status = 'pending_review'")
            print("  ✅ 关闭初审: workflow_status = 'first_reviewed'")
            
            print("\n修复位置:")
            print("  📁 文件: app/utils/content_generator.py")
            print("  🔧 函数: generate_single_content")
            print("  📝 行数: 311-341")
            
            # 6. 测试步骤
            print(f"\n6. 测试步骤:")
            print("-" * 40)
            print("请按以下步骤验证修复:")
            print("  1. 🌐 打开系统设置页面")
            print("  2. 🔧 关闭'启用初审'开关")
            print("  3. 📝 生成新的文案内容")
            print("  4. 🔍 检查文案是否直接出现在图片上传页面")
            print("  5. ✅ 确认不再出现在初审文案页面")
            
            print("\n预期结果:")
            print("  ❌ 关闭初审开关后，新生成的文案不会出现在初审页面")
            print("  ✅ 关闭初审开关后，新生成的文案直接出现在图片上传页面")
            print("  🔄 开启初审开关后，新生成的文案会出现在初审页面")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 初审开关设置测试完成！")
    print("\n关键修复点:")
    print("1. ✅ 文案生成时检查系统设置")
    print("2. ✅ 根据设置确定初始工作流状态")
    print("3. ✅ 确保开关生效")
    print("\n现在初审开关应该能正常工作了！")

if __name__ == '__main__':
    test_first_review_setting()
