#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单创建待发布测试数据
"""

import sys
import os
import json
from datetime import datetime, date, time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.content import Content
from app.models.user import User

def create_simple_pending_data():
    """创建简单的待发布测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 获取第一个用户作为创建者
            user = User.query.first()
            if not user:
                print("❌ 数据库中没有用户，无法创建文案")
                return

            print(f"使用用户 {user.username} (ID: {user.id}) 作为创建者")
            # 创建测试文案数据
            test_contents = [
                {
                    'title': '【高优先级】小红书种草神器推荐',
                    'content': '最近发现了一个超好用的产品！真的是太惊喜了～\n\n用了一段时间，效果真的很棒，强烈推荐给大家！\n\n#种草分享 #好物推荐',
                    'topics': json.dumps(['种草分享', '好物推荐', '生活分享']),
                    'location': '上海·静安区',
                    'workflow_status': 'pending_publish',
                    'publish_status': 'unpublished',
                    'publish_priority': 'high',
                    'display_date': date(2025, 7, 28),
                    'display_time': time(9, 0, 0)
                },
                {
                    'title': '【高优先级】护肤心得分享',
                    'content': '分享一下最近的护肤心得～\n\n坚持使用了一个月，皮肤状态真的有明显改善！\n\n姐妹们可以试试看！\n\n#护肤分享 #美容心得',
                    'topics': json.dumps(['护肤分享', '美容心得', '生活分享']),
                    'location': '深圳·南山区',
                    'workflow_status': 'pending_publish',
                    'publish_status': 'unpublished',
                    'publish_priority': 'high',
                    'display_date': date(2025, 7, 28),
                    'display_time': time(10, 0, 0)
                },
                {
                    'title': '【中优先级】今日穿搭分享',
                    'content': '今天的穿搭分享来啦～\n\n这套搭配简约又时尚，很适合日常出街！\n\n大家觉得怎么样呢？\n\n#穿搭分享 #时尚搭配',
                    'topics': json.dumps(['穿搭分享', '时尚搭配', '日常穿搭']),
                    'location': '北京·朝阳区',
                    'workflow_status': 'pending_publish',
                    'publish_status': 'unpublished',
                    'publish_priority': 'normal',
                    'display_date': date(2025, 7, 28),
                    'display_time': time(11, 0, 0)
                },
                {
                    'title': '【低优先级】美食探店记录',
                    'content': '今天去了一家超棒的餐厅！\n\n环境很好，菜品也很精致，服务态度也很棒～\n\n推荐给喜欢美食的朋友们！\n\n#美食探店 #餐厅推荐',
                    'topics': json.dumps(['美食探店', '餐厅推荐', '生活记录']),
                    'location': '广州·天河区',
                    'workflow_status': 'pending_publish',
                    'publish_status': 'unpublished',
                    'publish_priority': 'low',
                    'display_date': date(2025, 7, 28),
                    'display_time': time(12, 0, 0)
                }
            ]
            
            created_count = 0
            for content_data in test_contents:
                content = Content(
                    title=content_data['title'],
                    content=content_data['content'],
                    topics=content_data['topics'],
                    location=content_data['location'],
                    workflow_status=content_data['workflow_status'],
                    publish_status=content_data['publish_status'],
                    publish_priority=content_data['publish_priority'],
                    display_date=content_data['display_date'],
                    display_time=content_data['display_time'],
                    client_id=None,  # 设置为None避免外键约束
                    task_id=None,
                    batch_id=None,
                    template_id=None,
                    created_by=user.id,
                    created_at=datetime.now(),
                    is_deleted=False
                )
                db.session.add(content)
                created_count += 1
            
            db.session.commit()
            
            print(f"✅ 成功创建 {created_count} 篇待发布测试文案！")
            
            # 验证插入的数据
            contents = Content.query.filter(
                Content.workflow_status == 'pending_publish',
                Content.is_deleted == False
            ).order_by(
                Content.publish_priority.desc(),
                Content.display_date.asc(),
                Content.display_time.asc(),
                Content.created_at.asc()
            ).all()
            
            print("\n当前待发布的文案（按API排序）:")
            print("ID\t标题\t\t\t\t优先级\t展示时间")
            print("-" * 80)
            for content in contents:
                display_time = f"{content.display_date} {content.display_time}" if content.display_date and content.display_time else "未设置"
                print(f"{content.id}\t{content.title[:25]}...\t{content.publish_priority}\t{display_time}")
            
            print(f"\n现在可以测试API了！API会返回第一个文案（ID: {contents[0].id if contents else 'N/A'}）")
            
        except Exception as e:
            print(f"创建数据失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_simple_pending_data()
