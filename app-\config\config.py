# -*- coding: utf-8 -*-
"""
配置文件
"""

import os
from datetime import timedelta

# 数据库配置集中管理
DB_CONFIG = {
    'ENGINE': 'mysql',
    'DRIVER': 'pymysql',
    'HOST': 'localhost',
    'USER': 'xhsrw666',
    'PASSWORD': 'xhsrw666',
    'NAME': 'xhsrw666',
    'CHARSET': 'utf8mb4'
}

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DEBUG = True

    # 模板配置 - 禁用缓存以便开发时立即看到修改
    TEMPLATES_AUTO_RELOAD = True
    SEND_FILE_MAX_AGE_DEFAULT = 0
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = f"{DB_CONFIG['ENGINE']}+{DB_CONFIG['DRIVER']}://{DB_CONFIG['USER']}:{DB_CONFIG['PASSWORD']}@{DB_CONFIG['HOST']}/{DB_CONFIG['NAME']}?charset={DB_CONFIG['CHARSET']}"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'connect_args': {
            'charset': 'utf8mb4',
            'use_unicode': True,
            'init_command': "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
            'autocommit': False
        }
    }
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'uploads')
    UPLOADED_PHOTOS_DEST = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # 分页配置
    POSTS_PER_PAGE = 20
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
    
    # 系统配置
    SYSTEM_NAME = '小红书文案生成系统'
    SYSTEM_VERSION = '1.0.0'
    
    # SimpleMDE配置
    SIMPLEMDE_JS_IIFE = True
    SIMPLEMDE_USE_CDN = True
    
    # 审核流程配置
    AUTO_REVIEW_ENABLED = False  # 自动审核开关
    AUTO_FINAL_REVIEW_ENABLED = False  # 自动最终审核开关
    AUTO_REVIEW_DELAY = 3600  # 自动审核延迟时间（秒）
    
    # 发布超时配置
    PUBLISH_TIMEOUT = 86400  # 发布超时时间（秒，默认24小时）
    
    # 通知配置
    NOTIFICATION_ENABLED = True
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建上传目录
        if not os.path.exists(Config.UPLOAD_FOLDER):
            os.makedirs(Config.UPLOAD_FOLDER)
        
        # 创建日志目录
        log_dir = os.path.dirname(Config.LOG_FILE)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ECHO = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_ECHO = False
    
    # 生产环境应该使用环境变量设置密钥
    SECRET_KEY = os.environ.get('SECRET_KEY')

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    # 测试环境使用SQLite内存数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
} 