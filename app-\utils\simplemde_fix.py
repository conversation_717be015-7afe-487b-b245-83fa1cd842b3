"""
修复flask_simplemde扩展的Markup导入问题
"""

from markupsafe import Markup
from flask import Blueprint, current_app, url_for
import os
import json

class SimpleMDE:
    """Flask-SimpleMDE扩展的修复版本"""

    def __init__(self, app=None):
        self.app = None
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        self.app = app
        # 设置默认配置
        app.config.setdefault('SIMPLEMDE_JS_IIFE', True)
        app.config.setdefault('SIMPLEMDE_USE_CDN', True)
        app.config.setdefault('SIMPLEMDE_OPTIONS', {})

        # 注册扩展
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions['simplemde'] = self

        # 添加Jinja2上下文处理器
        @app.context_processor
        def simplemde_context():
            return {
                'simplemde': self
            }

    @property
    def css(self):
        """返回SimpleMDE CSS标记"""
        if self.app.config.get('SIMPLEMDE_USE_CDN', True):
            css = '<link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">'
        else:
            css = '<link rel="stylesheet" href="%s">' % url_for('simplemde.static', filename='simplemde.min.css')
        return Markup(css)

    @property
    def js(self):
        """返回SimpleMDE JS标记"""
        if self.app.config.get('SIMPLEMDE_USE_CDN', True):
            js = '<script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>'
        else:
            js = '<script src="%s"></script>' % url_for('simplemde.static', filename='simplemde.min.js')

        options = self.app.config.get('SIMPLEMDE_OPTIONS', {})
        if options:
            js += '<script>var simplemde_options = %s;</script>' % json.dumps(options)
        
        return Markup(js) 