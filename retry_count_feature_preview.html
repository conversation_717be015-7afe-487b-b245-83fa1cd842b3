<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布重试次数功能预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="card-title mb-0">
                            <i class="bi bi-arrow-repeat"></i> 发布重试次数功能
                        </h4>
                        <small>智能控制发布重试，避免无限重试，让超时处理策略真正发挥作用</small>
                    </div>
                    <div class="card-body">
                        <!-- 功能概述 -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb"></i> 功能概述</h6>
                            <p class="mb-0">
                                新增发布重试次数控制功能，当文案发布超时时，系统会记录重试次数。
                                超过设定的最大重试次数后，根据超时处理策略进行相应处理，避免无限重试。
                            </p>
                        </div>

                        <!-- 系统设置新增项 -->
                        <div class="mb-4">
                            <h5><i class="bi bi-gear"></i> 系统设置新增</h5>
                            <div class="border p-3 rounded bg-light">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold">发布超时时间</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" value="120" readonly>
                                            <span class="input-group-text">秒</span>
                                        </div>
                                        <small class="text-muted">API发布超时时间（秒）</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold text-success">发布最大重试次数 <span class="badge bg-success">新增</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" value="3" min="0" max="10">
                                            <span class="input-group-text">次</span>
                                        </div>
                                        <small class="text-muted">超过此次数后按策略处理</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold">发布超时处理策略</label>
                                        <select class="form-select">
                                            <option value="keep_timeout" selected>保持超时状态（需手动处理）</option>
                                            <option value="auto_reset">自动重置为待发布</option>
                                            <option value="auto_fail">自动标记为发布失败</option>
                                        </select>
                                        <small class="text-muted">超过重试次数后的处理方式</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 工作流程图 -->
                        <div class="mb-4">
                            <h5><i class="bi bi-diagram-3"></i> 工作流程</h5>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                        <div class="text-center">
                                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-play-fill"></i>
                                            </div>
                                            <div class="mt-2"><strong>文案发布</strong></div>
                                            <small class="text-muted">开始发布</small>
                                        </div>
                                        <i class="bi bi-arrow-right text-muted"></i>
                                        <div class="text-center">
                                            <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-clock"></i>
                                            </div>
                                            <div class="mt-2"><strong>发布超时</strong></div>
                                            <small class="text-muted">超过120秒</small>
                                        </div>
                                        <i class="bi bi-arrow-right text-muted"></i>
                                        <div class="text-center">
                                            <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-arrow-repeat"></i>
                                            </div>
                                            <div class="mt-2"><strong>重试计数</strong></div>
                                            <small class="text-muted">次数 +1</small>
                                        </div>
                                        <i class="bi bi-arrow-right text-muted"></i>
                                        <div class="text-center">
                                            <div class="bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-question-circle"></i>
                                            </div>
                                            <div class="mt-2"><strong>判断次数</strong></div>
                                            <small class="text-muted">≤ 3次？</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0"><i class="bi bi-check-circle"></i> 重试次数 ≤ 3次</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>继续重试：</strong></p>
                                            <ul class="mb-0">
                                                <li>重置为待发布状态</li>
                                                <li>清除发布时间</li>
                                                <li>记录重试信息</li>
                                                <li>重新进入发布队列</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0"><i class="bi bi-x-circle"></i> 重试次数 > 3次</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="mb-2"><strong>按策略处理：</strong></p>
                                            <ul class="mb-0">
                                                <li><strong>保持超时：</strong>进入超时状态</li>
                                                <li><strong>自动重置：</strong>重置重试次数</li>
                                                <li><strong>自动失败：</strong>标记为失败</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 重试次数说明 -->
                        <div class="mb-4">
                            <h5><i class="bi bi-info-circle"></i> 重试次数设置说明</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <h2 class="text-warning">0次</h2>
                                            <p class="mb-0">不重试，直接按超时策略处理</p>
                                            <small class="text-muted">适合测试环境</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <h2 class="text-success">1-3次</h2>
                                            <p class="mb-0">推荐设置，平衡重试和问题识别</p>
                                            <small class="text-muted">适合生产环境</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-info">
                                        <div class="card-body text-center">
                                            <h2 class="text-info">4-10次</h2>
                                            <p class="mb-0">适合网络不稳定的环境</p>
                                            <small class="text-muted">需要更多重试</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 功能优势 -->
                        <div class="mb-4">
                            <h5><i class="bi bi-star"></i> 功能优势</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">解决的问题</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="mb-0">
                                                <li>避免无限重试，防止资源浪费</li>
                                                <li>识别真正的发布问题文案</li>
                                                <li>让超时处理策略真正发挥作用</li>
                                                <li>提供可配置的重试策略</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">用户体验</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="mb-0">
                                                <li>系统更加智能和可靠</li>
                                                <li>减少人工干预需求</li>
                                                <li>提供详细的重试信息</li>
                                                <li>支持不同的处理策略</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 技术实现 -->
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-code-slash"></i> 技术实现
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>数据库更新</h6>
                                        <ul>
                                            <li>Content表新增 <code>publish_retry_count</code> 字段</li>
                                            <li>系统设置新增 <code>PUBLISH_MAX_RETRY_COUNT</code></li>
                                            <li>默认值为3次</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>API逻辑更新</h6>
                                        <ul>
                                            <li>超时时自动增加重试次数</li>
                                            <li>根据重试次数和策略处理</li>
                                            <li>发布成功时重置重试次数</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>界面更新</h6>
                                        <ul>
                                            <li>系统设置页面新增重试次数配置</li>
                                            <li>包含详细的设置说明</li>
                                            <li>支持0-10次范围设置</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
