# -*- coding: utf-8 -*-
"""
API接口视图
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from sqlalchemy import func, desc, or_, case
from app import csrf

from app import db
from app.models import Content, PublishRecord, SystemSetting
from app.models.image import ContentImage
from app.utils.api_auth import api_key_required

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')


# 添加CORS预检请求处理
@api_bp.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({'status': 'OK'})
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response


@api_bp.route('/contents', methods=['GET'])
@api_key_required
def get_contents():
    """
    获取文案列表
    ---
    参数:
        client_id: 客户ID (可选)
        status: 发布状态 (可选，默认为'unpublished')
        priority: 优先级 (可选)
        page: 页码 (可选，默认为1)
        per_page: 每页数量 (可选，默认为10)
        date_from: 开始日期 (可选，格式：YYYY-MM-DD)
        date_to: 结束日期 (可选，格式：YYYY-MM-DD)
    返回:
        contents: 文案列表
        total: 总数量
        page: 当前页码
        pages: 总页数
    """
    # 获取请求参数
    client_id = request.args.get('client_id', type=int)
    status = request.args.get('status', 'unpublished')
    priority = request.args.get('priority')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # 构建查询
    query = Content.query.filter(Content.is_deleted == False)
    
    # 根据参数筛选
    if client_id:
        query = query.filter(Content.client_id == client_id)
    
    if status == 'unpublished':
        query = query.filter(Content.workflow_status == 'pending_publish', 
                            Content.publish_status == 'unpublished')
    elif status == 'publishing':
        query = query.filter(Content.publish_status == 'publishing')
    elif status == 'published':
        query = query.filter(Content.workflow_status == 'published')
    elif status == 'failed':
        query = query.filter(Content.publish_status == 'failed')
    
    if priority:
        query = query.filter(Content.publish_priority == priority)
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Content.created_at >= date_from)
        except ValueError:
            return jsonify({'error': '日期格式错误，请使用YYYY-MM-DD格式'}), 400
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            # 添加一天，使得结束日期包含当天
            date_to = date_to + timedelta(days=1)
            query = query.filter(Content.created_at < date_to)
        except ValueError:
            return jsonify({'error': '日期格式错误，请使用YYYY-MM-DD格式'}), 400
    
    # 按优先级排序：高 > 中 > 低
    priority_order = {
        'high': 0,
        'normal': 1,
        'low': 2
    }
    query = query.order_by(
        case(
            {value: key for key, value in priority_order.items()},
            value=Content.publish_priority
        ),
        Content.created_at
    )
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    contents = pagination.items
    
    # 构建返回数据
    result = []
    for content in contents:
        result.append({
            'id': content.id,
            'title': content.title,
            'client_id': content.client_id,
            'client_name': content.client.name if content.client else None,
            'priority': content.publish_priority,
            'status': content.publish_status,
            'workflow_status': content.workflow_status,
            'created_at': content.created_at.strftime('%Y-%m-%d %H:%M:%S') if content.created_at else None,
            'publish_time': content.publish_time.strftime('%Y-%m-%d %H:%M:%S') if content.publish_time else None
        })
    
    return jsonify({
        'success': True,
        'contents': result,
        'total': pagination.total,
        'page': pagination.page,
        'pages': pagination.pages
    })


@api_bp.route('/content', methods=['GET'])
@csrf.exempt  # 豁免CSRF保护
@api_key_required
def get_content():
    """
    获取单篇待发布文案（完全按照发布状态管理页面的逻辑）
    ---
    返回:
        content: 文案详情（自动按优先级排序，高>中>低）
    """
    # 智能获取逻辑：优先获取待发布，无待发布时检查超时的发布中文案

    # 1. 首先尝试获取待发布的文案
    query = Content.query.filter(
        Content.workflow_status == 'pending_publish',
        Content.is_deleted == False
    )

    # 完全按照发布状态管理页面的排序逻辑：
    # 1. 优先级排序（高>中>低） - 使用CASE语句确保正确的优先级顺序
    # 2. 展示日期排序 (asc)
    # 3. 展示时间排序 (asc)
    # 4. 创建时间排序 (asc)
    from sqlalchemy import case
    priority_order = case(
        (Content.publish_priority == 'high', 1),
        (Content.publish_priority == 'normal', 2),
        (Content.publish_priority == 'low', 3),
        else_=4
    )

    query = query.order_by(
        priority_order.asc(),  # 1=high, 2=normal, 3=low
        Content.display_date.asc(),
        Content.display_time.asc(),
        Content.created_at.asc()
    )

    # 获取优先级最高的一篇文案
    content = query.first()

    # 如果没有找到待发布的文案，检查是否有超时的发布中文案
    if not content:
        print("没有待发布文案，检查超时的发布中文案...")

        # 获取超时设置
        publish_timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
        publish_timeout = int(publish_timeout_setting.value) if publish_timeout_setting else 7200  # 默认2小时

        # 计算超时时间点
        timeout_point = datetime.now() - timedelta(seconds=publish_timeout)

        # 查找发布中且已超时的文案（按优先级排序）
        timeout_content = Content.query.filter(
            Content.workflow_status == 'publishing',
            Content.publish_time < timeout_point,
            Content.is_deleted == False
        ).order_by(
            priority_order.asc(),
            Content.display_date.asc(),
            Content.display_time.asc(),
            Content.created_at.asc()
        ).first()

        if timeout_content:
            print(f"发现超时文案 {timeout_content.id}，自动重置为待发布")

            # 单个文案超时处理：重置为待发布状态
            timeout_content.workflow_status = 'pending_publish'
            timeout_content.publish_status = 'unpublished'
            timeout_content.publish_time = None
            timeout_content.status_update_time = datetime.now()

            # 设置提示信息（通过publish_error字段临时存储）
            timeout_content.publish_error = f"超时自动重置 ({datetime.now().strftime('%m-%d %H:%M')})"

            db.session.commit()

            # 使用重置后的文案
            content = timeout_content
            print(f"使用重置后的超时文案: {content.id}")
        else:
            return jsonify({
                'success': False,
                'error': '没有可用的待发布文案',
                'message': '当前没有处于待发布状态(pending_publish)的文案，也没有超时的发布中文案'
            }), 404
    
    # 重要：获取文案后立即更新状态为发布中
    # 这样可以防止同一文案被重复获取
    content.workflow_status = 'publishing'  # 工作流状态改为发布中
    content.publish_status = 'publishing'   # 发布状态改为发布中
    content.publish_time = datetime.now()   # 记录获取时间
    content.status_update_time = datetime.now()

    try:
        db.session.commit()
        print(f"文案 {content.id} 状态已更新为发布中")
    except Exception as e:
        db.session.rollback()
        print(f"更新文案状态失败: {e}")
        return jsonify({
            'success': False,
            'error': '更新文案状态失败',
            'message': str(e)
        }), 500

    # 获取图片信息 - 支持两种存储方式
    images = []

    # 方式1：从ContentImage表获取图片
    content_images = ContentImage.get_by_content(content.id)
    if content_images:
        for img in content_images:
            # 构建完整的图片URL
            if img.image_path.startswith('http'):
                image_url = img.image_path
            else:
                # 相对路径，需要构建完整URL
                from flask import url_for
                image_url = url_for('static', filename=f'uploads/{img.image_path}', _external=True)
            images.append(image_url)

    # 方式2：从Content.image_urls字段获取图片（如果ContentImage表没有数据）
    if not images and hasattr(content, 'image_urls_list'):
        images = content.image_urls_list or []

    # 构建返回数据 - 包含发布状态管理页面显示的所有信息
    content_data = {
        'id': content.id,
        'title': content.title or '',
        'content': content.content or '',
        'topics': content.topics_list if hasattr(content, 'topics_list') else [],
        'location': content.location or '',
        'images': images,  # 使用正确获取的图片列表
        'image_count': len(images),  # 添加图片数量
        'client_id': content.client_id,
        'client_name': content.client.name if content.client else '未知客户',
        'task_id': content.task_id,
        'task_name': content.task.name if content.task else '未知任务',
        'batch_id': content.batch_id,
        'batch_name': content.batch.name if content.batch else '未知批次',
        'template_id': content.template_id,
        'template_title': content.template.title if content.template else '未知模板',
        'priority': content.publish_priority or 'normal',
        'priority_display': {
            'high': '高',
            'normal': '中',
            'low': '低'
        }.get(content.publish_priority, '中'),
        'workflow_status': content.workflow_status,
        'publish_status': content.publish_status,
        'created_at': content.created_at.isoformat() if content.created_at else None,
        'publish_time': content.publish_time.isoformat() if content.publish_time else None,
        'display_date': content.display_date.isoformat() if content.display_date else None,
        'display_time': content.display_time.strftime('%H:%M:%S') if content.display_time else None,
        'ext_data': content.ext_data or {}
    }

    return jsonify({
        'success': True,
        'message': f'成功获取待发布文案，优先级：{content_data["priority_display"]}',
        'content': content_data
    })


@api_bp.route('/content/<int:content_id>', methods=['GET'])
@api_key_required
def get_content_by_id(content_id):
    """
    根据ID获取文案详情
    ---
    参数:
        content_id: 文案ID (路径参数)
    返回:
        content: 文案详情
    """
    content = Content.query.get(content_id)
    if not content:
        return jsonify({'error': f'文案不存在：{content_id}'}), 404
    
    # 构建返回数据
    content_data = {
        'id': content.id,
        'title': content.title,
        'content': content.content,
        'topics': content.topics_list,
        'location': content.location,
        'images': content.image_urls_list,
        'client_id': content.client_id,
        'client_name': content.client.name if content.client else None,
        'created_at': content.created_at.isoformat() if content.created_at else None,
        'priority': content.publish_priority,
        'status': content.publish_status,
        'workflow_status': content.workflow_status,
        'ext_data': content.ext_data
    }
    
    return jsonify({
        'success': True,
        'content': content_data
    })


@api_bp.route('/content/<int:content_id>/status', methods=['PUT', 'POST'])
@csrf.exempt  # 豁免CSRF保护
@api_key_required
def update_content_status(content_id):
    print(f"=== 状态更新API被调用: content_id={content_id} ===")
    """
    更新文案发布状态
    ---
    参数:
        content_id: 文案ID (路径参数)
        status: 发布状态 (必填，success/failed)
        publish_url: 发布链接 (可选)
        platform: 发布平台 (可选)
        account: 发布账号 (可选)
        message: 提示信息 (可选，支持成功和失败的提示)
        error_message: 失败原因 (可选，兼容旧版本)
        ext_info: 扩展信息 (可选，JSON格式)
    返回:
        success: 是否成功
        message: 提示信息
    """
    try:
        print(f"收到状态更新请求: content_id={content_id}")

        # 获取请求参数
        data = request.get_json() or {}
        status = data.get('status')
        publish_url = data.get('publish_url')
        platform = data.get('platform')
        account = data.get('account')
        message = data.get('message')  # 新的通用提示信息字段
        error_message = data.get('error_message')  # 兼容旧版本
        ext_info = data.get('ext_info')

        print(f"请求数据: {data}")

        # 验证必填参数
        if not status:
            return jsonify({
                'success': False,
                'error': '缺少必填参数：status'
            }), 400

        # 获取文案
        content = Content.query.get(content_id)
        if not content:
            return jsonify({
                'success': False,
                'error': f'文案不存在：{content_id}'
            }), 404

        print(f"找到文案: id={content.id}, title={content.title}, current_status={content.workflow_status}")

        # 更新文案状态和提示信息
        if status == 'success':
            content.publish_status = 'published'
            content.workflow_status = 'published'
            # 成功状态不需要在Content表中存储提示信息，提示信息存储在PublishRecord中
        elif status == 'failed':
            content.publish_status = 'failed'
            content.workflow_status = 'publish_failed'  # 更明确的失败状态
            # 失败状态不需要在Content表中存储提示信息，提示信息存储在PublishRecord中

        else:
            return jsonify({
                'success': False,
                'error': f'无效的状态值：{status}',
                'message': '状态值必须是 success 或 failed'
            }), 400

        # 提示信息存储在PublishRecord中，不需要在Content表中存储

        # 更新状态更新时间
        content.status_update_time = datetime.now()

        print(f"更新文案状态: content_id={content_id}, status={status}, workflow_status={content.workflow_status}")
        print(f"设置提示信息: '{content.publish_error}'")

        # 先提交Content表的更新
        db.session.commit()
        print(f"Content表更新已提交")

        # 创建发布记录
        # 确保字段长度不超过数据库限制
        platform_value = (platform or '')[:50] if platform else ''
        account_value = (account or '')[:50] if account else ''  # 限制为50字符
        publish_url_value = (publish_url or '')[:255] if publish_url else ''

        # 处理提示信息：优先使用message，其次使用error_message
        message_value = message or error_message or ''
        message_value = message_value[:1000] if message_value else ''  # 限制信息长度

        publish_record = PublishRecord(
            content_id=content_id,
            status=status,
            platform=platform_value,
            account=account_value,
            publish_url=publish_url_value,
            publish_time=datetime.now(),
            error_message=message_value,
            ext_info=ext_info or '{}'
        )

        db.session.add(publish_record)
        db.session.commit()

        print(f"发布记录创建成功: content_id={content_id}, status={status}")

        return jsonify({
            'success': True,
            'message': '状态已更新'
        })

    except Exception as e:
        db.session.rollback()
        print(f"状态更新API失败: {e}")
        return jsonify({
            'success': False,
            'error': '状态更新失败',
            'message': str(e)
        }), 500


@api_bp.route('/check-timeouts', methods=['POST'])
@csrf.exempt  # 豁免CSRF保护
@api_key_required
def check_timeouts():
    """
    检查并处理发布超时的文案
    ---
    返回:
        timeout_count: 处理的超时文案数量
    """
    # 获取超时设置
    publish_timeout_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
    publish_timeout = int(publish_timeout_setting.value) if publish_timeout_setting else 7200  # 默认2小时

    # 获取超时处理策略
    timeout_action_setting = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
    timeout_action = timeout_action_setting.value if timeout_action_setting else 'keep_timeout'

    # 计算超时时间点
    timeout_point = datetime.now() - timedelta(seconds=publish_timeout)

    # 查找所有发布中且已超时的文案
    timeout_contents = Content.query.filter(
        Content.workflow_status == 'publishing',
        Content.publish_time < timeout_point
    ).all()

    timeout_count = 0
    for content in timeout_contents:
        # 根据策略处理超时文案
        if timeout_action == 'auto_reset':
            # 自动重置为待发布
            content.workflow_status = 'pending_publish'
            content.publish_status = 'unpublished'
            content.publish_time = None
        elif timeout_action == 'auto_fail':
            # 自动标记为发布失败
            content.workflow_status = 'publish_failed'
            content.publish_status = 'failed'
        else:
            # 默认：保持超时状态
            content.workflow_status = 'publish_timeout'
            content.publish_status = 'publish_timeout'

        content.status_update_time = datetime.now()
        timeout_count += 1

        print(f"文案 {content.id} 发布超时，处理策略: {timeout_action}")

    if timeout_count > 0:
        try:
            db.session.commit()
            print(f"处理了 {timeout_count} 篇超时文案")
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'error': '处理超时文案失败',
                'message': str(e)
            }), 500

    return jsonify({
        'success': True,
        'message': f'检查完成，处理了 {timeout_count} 篇超时文案',
        'timeout_count': timeout_count,
        'timeout_seconds': publish_timeout,
        'timeout_action': timeout_action
    })


@api_bp.route('/contents/batch', methods=['POST'])
@api_key_required
def batch_update():
    """
    批量更新文案状态
    ---
    参数:
        content_ids: 文案ID列表 (必填)
        action: 操作类型 (必填，status/priority)
        status: 状态值 (当action为status时必填)
        priority: 优先级值 (当action为priority时必填)
    返回:
        success: 是否成功
        count: 更新数量
        message: 提示信息
    """
    # 获取请求参数
    data = request.get_json() or {}
    content_ids = data.get('content_ids', [])
    action = data.get('action')
    status = data.get('status')
    priority = data.get('priority')
    
    # 验证必填参数
    if not content_ids:
        return jsonify({'error': '缺少必填参数：content_ids'}), 400
    
    if not action:
        return jsonify({'error': '缺少必填参数：action'}), 400
    
    if action == 'status' and not status:
        return jsonify({'error': '当action为status时，status参数必填'}), 400
    
    if action == 'priority' and not priority:
        return jsonify({'error': '当action为priority时，priority参数必填'}), 400
    
    # 批量更新
    count = 0
    now = datetime.now()
    
    for content_id in content_ids:
        content = Content.query.get(content_id)
        if not content:
            continue
        
        # 根据操作类型执行不同的操作
        if action == 'status':
            # 更新发布状态
            old_status = content.publish_status
            content.publish_status = status
            
            # 如果标记为发布成功，同时更新工作流状态为已发布
            if status == 'published':
                content.workflow_status = 'published'
                content.publish_time = now
                
                # 创建发布记录
                publish_record = PublishRecord(
                    content_id=content.id,
                    status='success',
                    platform='API批量操作',
                    account='API',
                    publish_time=now,
                    ext_info='{"source": "api_batch"}'
                )
                db.session.add(publish_record)
            
            # 如果从发布中状态重置为待发布，清除发布时间
            if old_status == 'publishing' and status == 'unpublished':
                content.publish_time = None
            
            count += 1
            
        elif action == 'priority':
            # 更新发布优先级
            content.publish_priority = priority
            count += 1
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'count': count,
        'message': f'已成功更新{count}篇文案'
    })


@api_bp.route('/stats', methods=['GET'])
@api_key_required
def get_stats():
    """
    获取文案统计数据
    ---
    返回:
        stats: 统计数据
    """
    # 统计各状态文案数量
    status_counts = db.session.query(
        Content.publish_status,
        func.count(Content.id).label('count')
    ).group_by(Content.publish_status).all()
    
    # 统计各优先级文案数量
    priority_counts = db.session.query(
        Content.publish_priority,
        func.count(Content.id).label('count')
    ).group_by(Content.publish_priority).all()
    
    # 统计各客户文案数量
    client_counts = db.session.query(
        Content.client_id,
        func.count(Content.id).label('count')
    ).group_by(Content.client_id).all()
    
    # 构建返回数据
    status_stats = {status: count for status, count in status_counts}
    priority_stats = {priority: count for priority, count in priority_counts}
    client_stats = {client_id: count for client_id, count in client_counts if client_id}
    
    return jsonify({
        'success': True,
        'stats': {
            'status': status_stats,
            'priority': priority_stats,
            'client': client_stats,
            'total': Content.query.count()
        }
    })


@api_bp.route('/settings', methods=['GET'])
@csrf.exempt  # 豁免CSRF保护
@api_key_required
def get_settings():
    """
    获取系统设置
    ---
    返回:
        settings: 系统设置
    """
    # 获取API相关的系统设置
    settings = {}
    
    # 发布超时设置
    publish_timeout = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT').first()
    if publish_timeout:
        settings['publish_timeout'] = int(publish_timeout.value)
    else:
        settings['publish_timeout'] = 86400  # 默认24小时
    
    # 超时处理策略
    timeout_action = SystemSetting.query.filter_by(key='PUBLISH_TIMEOUT_ACTION').first()
    if timeout_action:
        settings['timeout_action'] = timeout_action.value
    else:
        settings['timeout_action'] = 'keep_timeout'  # 默认保持超时状态
    
    return jsonify({
        'success': True,
        'settings': settings
    }) 