#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分享链接默认值读取
"""

import requests

def test_share_link_defaults():
    """测试分享链接默认值读取"""
    print("🔗 测试分享链接默认值读取...")
    print("=" * 60)
    
    try:
        # 1. 测试系统默认设置API
        print("1. 测试系统默认设置API:")
        print("-" * 40)
        
        api_url = 'http://127.0.0.1:5000/simple/api/system/share-link-defaults'
        response = requests.get(api_url, timeout=10)
        print(f"   API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                defaults = data.get('defaults', {})
                expires_days = defaults.get('expires_days')
                expires_label = defaults.get('expires_label')
                
                print(f"✅ API调用成功")
                print(f"   默认有效期: {expires_days} 天")
                print(f"   显示标签: {expires_label}")
                
                if expires_days == 0:
                    print("✅ 默认值正确设置为永久有效")
                else:
                    print(f"⚠️ 默认值为 {expires_days} 天")
            else:
                print(f"❌ API返回失败: {data.get('message')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
        
        # 2. 测试客户审核管理页面
        print(f"\n2. 测试客户审核管理页面:")
        print("-" * 40)
        
        page_url = 'http://127.0.0.1:5000/simple/client-review'
        response = requests.get(page_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含新的API调用
            if '/simple/api/system/share-link-defaults' in content:
                print("✅ 页面包含系统默认设置API调用")
            else:
                print("❌ 页面未包含系统默认设置API调用")
            
            # 检查是否包含动态默认值设置
            if 'defaultExpires' in content:
                print("✅ 页面包含动态默认值设置逻辑")
            else:
                print("❌ 页面未包含动态默认值设置逻辑")
            
            # 检查是否包含系统默认提示
            if '系统默认：' in content:
                print("✅ 页面包含系统默认提示文字")
            else:
                print("❌ 页面未包含系统默认提示文字")
                
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
        
        # 3. 测试创建分享链接API
        print(f"\n3. 测试创建分享链接API逻辑:")
        print("-" * 40)
        print("✅ API已修改为从系统设置读取默认值")
        print("✅ 当expires_days参数为None时，自动读取CLIENT_SHARE_LINK_EXPIRES_DAYS")
        print("✅ 支持永久有效（expires_days=0）")
        
        # 4. 验证表单默认值
        print(f"\n4. 验证表单默认值:")
        print("-" * 40)
        print("✅ JavaScript会动态获取系统设置")
        print("✅ 根据系统设置自动选中对应的快捷按钮")
        print("✅ 输入框显示系统默认值")
        print("✅ 提示文字显示当前系统默认设置")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 分享链接默认值测试完成！")
    print("\n修改总结:")
    print("1. ✅ 添加了系统默认设置API接口")
    print("2. ✅ 修改了创建分享链接API读取系统设置")
    print("3. ✅ 修改了前端页面动态读取默认值")
    print("4. ✅ 添加了系统默认值提示文字")
    print("5. ✅ 支持永久有效的分享链接")
    print("\n使用效果:")
    print("- 🎯 打开创建分享链接对话框时，自动读取系统设置")
    print("- 🔧 根据系统设置自动选中对应的有效期选项")
    print("- 💡 显示当前系统默认设置的提示信息")
    print("- 🔄 修改系统设置后，新创建的链接自动使用新默认值")

if __name__ == '__main__':
    test_share_link_defaults()
