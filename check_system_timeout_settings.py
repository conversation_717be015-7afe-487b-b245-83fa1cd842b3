#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查系统中的审核超时相关设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def check_system_timeout_settings():
    """检查系统中的审核超时相关设置"""
    print("🔍 检查系统中的审核超时相关设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看所有系统设置
            print("1. 当前所有系统设置:")
            print("-" * 40)
            
            all_settings = SystemSetting.query.all()
            timeout_related_settings = []
            
            for setting in all_settings:
                print(f"  {setting.key}: {setting.value} ({setting.description})")
                
                # 识别审核超时相关的设置
                if any(keyword in setting.key.lower() for keyword in ['timeout', 'review', 'deadline']):
                    if 'publish' not in setting.key.lower():  # 排除发布超时设置
                        timeout_related_settings.append(setting)
            
            # 2. 识别需要清理的审核超时设置
            print(f"\n2. 识别的审核超时相关设置:")
            print("-" * 40)
            
            if timeout_related_settings:
                for setting in timeout_related_settings:
                    print(f"  🔍 {setting.key}: {setting.value}")
                    print(f"      描述: {setting.description}")
                    print(f"      ID: {setting.id}")
                    print()
            else:
                print("  ✅ 没有发现需要清理的审核超时设置")
            
            # 3. 检查是否有REVIEW_TIMEOUT_HOURS设置
            print(f"3. 检查REVIEW_TIMEOUT_HOURS设置:")
            print("-" * 40)
            
            review_timeout_setting = SystemSetting.query.filter_by(key='REVIEW_TIMEOUT_HOURS').first()
            if review_timeout_setting:
                print(f"  🔍 发现REVIEW_TIMEOUT_HOURS设置:")
                print(f"      ID: {review_timeout_setting.id}")
                print(f"      值: {review_timeout_setting.value}")
                print(f"      描述: {review_timeout_setting.description}")
                print(f"      更新时间: {review_timeout_setting.updated_at}")
                print(f"  ❌ 建议删除：此设置已被客户级别设置替代")
            else:
                print(f"  ✅ 未发现REVIEW_TIMEOUT_HOURS设置")
            
            # 4. 检查其他可能的审核相关设置
            print(f"\n4. 检查其他审核相关设置:")
            print("-" * 40)
            
            other_review_keys = [
                'review_timeout_hours',
                'CLIENT_REVIEW_TIMEOUT_HOURS', 
                'AUTO_REVIEW_DELAY',
                'auto_review_delay',
                'auto_confirm_minutes'
            ]
            
            found_settings = []
            for key in other_review_keys:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    found_settings.append(setting)
                    print(f"  🔍 {key}: {setting.value} (ID: {setting.id})")
            
            if not found_settings:
                print(f"  ✅ 未发现其他审核相关设置")
            
            # 5. 建议的清理操作
            print(f"\n5. 建议的清理操作:")
            print("-" * 40)
            
            all_review_settings = timeout_related_settings + ([review_timeout_setting] if review_timeout_setting else []) + found_settings
            
            if all_review_settings:
                print(f"建议删除以下设置（已被客户级别设置替代）:")
                for setting in all_review_settings:
                    print(f"  ❌ {setting.key} (ID: {setting.id})")
                
                print(f"\n删除原因:")
                print(f"  1. 客户级别设置更灵活，可以为不同客户配置不同规则")
                print(f"  2. 避免全局设置和客户设置之间的混淆")
                print(f"  3. 简化系统设置界面，减少冗余选项")
                print(f"  4. 提高用户体验，设置更加直观")
            else:
                print(f"  ✅ 没有需要清理的审核超时设置")
            
            # 6. 保留的设置
            print(f"\n6. 应该保留的超时设置:")
            print("-" * 40)
            
            publish_timeout_settings = [
                'PUBLISH_TIMEOUT',
                'PUBLISH_TIMEOUT_ACTION'
            ]
            
            for key in publish_timeout_settings:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"  ✅ {key}: {setting.value}")
                    print(f"      原因: 发布超时是系统级功能，需要全局设置")
            
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 系统审核超时设置检查完成！")
    print("\n总结:")
    print("1. 🔍 已识别所有审核超时相关设置")
    print("2. 📋 提供了清理建议")
    print("3. ✅ 区分了需要保留和删除的设置")
    print("4. 🎯 客户级别设置已完全替代全局审核超时设置")

if __name__ == '__main__':
    check_system_timeout_settings()
