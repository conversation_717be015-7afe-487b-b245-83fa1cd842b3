<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ content.title or '文案详情' }} - {{ client.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* 响应式头部 */
        .responsive-header {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(255, 36, 66, 0.3);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        /* 桌面版头部 */
        @media (min-width: 768px) {
            .responsive-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 2rem 0;
                position: relative;
                margin-bottom: 2rem;
            }
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        @media (min-width: 768px) {
            .back-btn {
                background: #6c757d;
                color: white;
                border-radius: 10px;
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                text-decoration: none;
            }
            
            .back-btn:hover {
                background: #5a6268;
                color: white;
            }
        }

        /* 头部审核按钮 */
        .header-action-btn {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-approve-header {
            background: rgba(40, 167, 69, 0.9);
            color: white;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .btn-approve-header:hover {
            background: rgba(40, 167, 69, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        }

        .btn-reject-header {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        }

        .btn-reject-header:hover {
            background: rgba(220, 53, 69, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
        }

        /* 移动端头部按钮优化 */
        @media (max-width: 767px) {
            .header-action-btn {
                padding: 6px 10px;
                font-size: 12px;
                border-radius: 15px;
            }
        }
        
        .header-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        
        @media (min-width: 768px) {
            .header-title {
                font-size: 2rem;
                text-align: left;
            }
        }
        
        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-top: 0.25rem;
        }
        
        @media (min-width: 768px) {
            .header-subtitle {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
        }
        
        /* 内容容器 */
        .content-container {
            background: white;
            margin: 0.5rem;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        @media (min-width: 768px) {
            .content-container {
                max-width: 1200px;
                margin: 0 auto 2rem;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
        }
        
        /* 文章头部 */
        .article-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        @media (min-width: 768px) {
            .article-header {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                padding: 2rem;
                border-radius: 15px 15px 0 0;
            }
        }
        
        .article-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            line-height: 1.4;
            margin: 0 0 1rem 0;
        }
        
        @media (min-width: 768px) {
            .article-title {
                font-size: 1.8rem;
                margin-bottom: 1rem;
            }
        }
        
        .article-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: #999;
        }
        
        @media (min-width: 768px) {
            .article-meta {
                font-size: 0.9rem;
                color: #666;
            }
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        @media (min-width: 768px) {
            .status-badge {
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: 600;
                font-size: 0.85rem;
            }
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        /* 状态旁边的审核按钮 */
        .status-action-buttons {
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .status-action-buttons:hover {
            opacity: 1;
        }

        .status-action-btn {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            border: none;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        @media (min-width: 768px) {
            .status-action-btn {
                padding: 6px 12px;
                border-radius: 15px;
                font-size: 13px;
            }
        }

        .btn-approve-status {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .btn-approve-status:hover {
            background: rgba(40, 167, 69, 0.9);
            color: white;
            border-color: #28a745;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .btn-reject-status {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .btn-reject-status:hover {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            border-color: #dc3545;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        }

        /* 文章内容 */
        .article-content {
            padding: 1.5rem;
        }
        
        @media (min-width: 768px) {
            .article-content {
                padding: 2rem;
            }
        }
        
        .content-text {
            font-size: 1rem;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        @media (min-width: 768px) {
            .content-text {
                font-size: 1.1rem;
                line-height: 1.8;
                margin-bottom: 2rem;
            }
        }
        
        /* 图片展示 */
        .images-section {
            padding: 0 1.5rem 1.5rem;
        }
        
        @media (min-width: 768px) {
            .images-section {
                margin-bottom: 2rem;
            }
        }
        
        .images-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        @media (min-width: 768px) {
            .images-title {
                font-size: 1.2rem;
            }
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
        }
        
        @media (min-width: 768px) {
            .images-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }
        }
        
        .image-item {
            aspect-ratio: 1;
            border-radius: 12px;
            overflow: hidden;
            background: #f8f9fa;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .image-item:active {
            transform: scale(0.95);
        }
        
        @media (min-width: 768px) {
            .image-item {
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .image-item:hover {
                transform: scale(1.02);
            }
            
            .image-item:active {
                transform: scale(1.0);
            }
        }
        
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 无图片状态 */
        .no-images {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }

        .no-images i {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .no-images p {
            margin: 0;
            font-size: 1rem;
        }

        /* 详细信息 */
        .details-section {
            padding: 1.5rem;
            background: #f8f9fa;
            margin: 0.5rem;
            border-radius: 16px;
        }
        
        @media (min-width: 768px) {
            .details-section {
                max-width: 1200px;
                margin: 0 auto 2rem;
                border-radius: 15px;
                padding: 2rem;
            }
        }
        
        .details-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        @media (min-width: 768px) {
            .details-title {
                font-size: 1.3rem;
                margin-bottom: 1.5rem;
            }
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        @media (min-width: 768px) {
            .detail-item {
                padding: 1rem 0;
                border-bottom: 1px solid #dee2e6;
            }
        }

        .detail-label {
            font-size: 0.9rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 5px;
            min-width: 80px;
            flex-shrink: 0;
        }

        @media (min-width: 768px) {
            .detail-label {
                font-weight: 600;
                min-width: 100px;
            }
        }

        .detail-value {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
            flex: 1;
        }

        /* 标签容器 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            flex: 1;
            justify-content: flex-end;
        }

        @media (min-width: 768px) {
            .tags-container {
                gap: 8px;
            }
        }

        /* 通用标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            gap: 3px;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        @media (min-width: 768px) {
            .tag {
                padding: 4px 12px;
                font-size: 13px;
                border-radius: 20px;
                gap: 4px;
            }
        }

        /* 话题标签 */
        .tag-topic {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .tag-topic:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
        }

        /* @用户标签 */
        .tag-user {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #8b4513;
            box-shadow: 0 2px 4px rgba(252, 182, 159, 0.3);
        }

        .tag-user:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(252, 182, 159, 0.4);
        }

        /* 定位标签 */
        .tag-location {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c5aa0;
            box-shadow: 0 2px 4px rgba(168, 237, 234, 0.3);
        }

        .tag-location:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(168, 237, 234, 0.4);
        }
        
        /* 操作按钮 */
        .action-section {
            padding: 1rem;
            background: white;
            position: sticky;
            bottom: 0;
            border-top: 1px solid #f0f0f0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        }
        
        @media (min-width: 768px) {
            .action-section {
                max-width: 1200px;
                margin: 0 auto;
                border-radius: 15px;
                padding: 2rem;
                text-align: center;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                position: relative;
                bottom: auto;
            }
        }
        
        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }
        
        @media (min-width: 768px) {
            .action-buttons {
                justify-content: center;
                gap: 1rem;
            }
        }
        
        .action-btn {
            flex: 1;
            padding: 0.875rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        @media (min-width: 768px) {
            .action-btn {
                flex: none;
                padding: 1rem 2rem;
                font-size: 1.1rem;
                min-width: 150px;
                border-radius: 10px;
            }
        }
        
        .btn-approve {
            background: #ff2442;
            color: white;
        }
        
        .btn-approve:hover {
            background: #e01e3a;
            color: white;
        }
        
        @media (min-width: 768px) {
            .btn-approve {
                background: #28a745;
            }
            
            .btn-approve:hover {
                background: #218838;
            }
        }
        
        .btn-reject {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-reject:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        @media (min-width: 768px) {
            .btn-reject {
                background: #dc3545;
                color: white;
                border: none;
            }
            
            .btn-reject:hover {
                background: #c82333;
            }
        }
        
        .btn-disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        /* 图片预览模态框 */
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .image-modal img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
        
        .image-modal-close {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: white;
        }
        
        @media (min-width: 768px) {
            .safe-area-bottom {
                display: none;
            }
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .images-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .article-header, .article-content, .images-section, .details-section {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 响应式头部 -->
    <div class="responsive-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center gap-3">
                    <button class="back-btn d-md-none" onclick="goBack()">
                        <i class="bi bi-arrow-left"></i>
                    </button>
                    <a href="javascript:goBack()" class="back-btn d-none d-md-inline-flex">
                        <i class="bi bi-arrow-left"></i>
                        返回列表
                    </a>
                    <div>
                        <h1 class="header-title">{{ client.name }}</h1>
                        <div class="header-subtitle d-md-block">文案详情</div>
                    </div>
                </div>

                <!-- 右侧预留空间 -->
                <div class="d-flex align-items-center gap-2">
                    <!-- 右上角不显示审核按钮，只在文章状态旁显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 内容容器 -->
    <div class="content-container">
        <!-- 文章头部 -->
        <div class="article-header">
            <h2 class="article-title">{{ content.title or '无标题' }}</h2>
            <div class="article-meta">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div class="d-flex align-items-center gap-2">
                        <span>{{ content.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        {% set status_map = {
                            'pending': {'class': 'status-pending', 'text': '待审核'},
                            'pending_client_review': {'class': 'status-pending', 'text': '待审核'},
                            'approved': {'class': 'status-approved', 'text': '待发布' if content.workflow_status == 'ready_to_publish' else '已通过'},
                            'client_approved': {'class': 'status-approved', 'text': '待发布' if content.workflow_status == 'ready_to_publish' else '已通过'},
                            'rejected': {'class': 'status-rejected', 'text': '已驳回'},
                            'client_rejected': {'class': 'status-rejected', 'text': '已驳回'}
                        } %}
                        {% if content.workflow_status == 'pending_client_review' %}
                            <span class="status-badge status-pending">待审核</span>
                        {% elif content.client_review_status == 'approved' %}
                            <span class="status-badge status-approved">已通过</span>
                        {% elif content.client_review_status == 'rejected' %}
                            <span class="status-badge status-rejected">已驳回</span>
                        {% else %}
                            <span class="status-badge status-pending">待审核</span>
                        {% endif %}
                    </div>

                    <!-- 状态旁边的审核按钮 -->
                    {% if content.workflow_status == 'pending_client_review' and content.client_review_status == 'pending' %}
                    <div class="d-flex align-items-center gap-2 status-action-buttons">
                        <button class="status-action-btn btn-approve-status" onclick="approveContent()" title="通过审核">
                            <i class="bi bi-check-lg"></i>
                            <span class="d-none d-md-inline ms-1">通过</span>
                        </button>
                        <button class="status-action-btn btn-reject-status" onclick="showRejectModal()" title="驳回审核">
                            <i class="bi bi-x-lg"></i>
                            <span class="d-none d-md-inline ms-1">驳回</span>
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 文章内容 -->
        {% if content.content %}
        <div class="article-content">
            <div class="content-text">{{ content.content }}</div>
        </div>
        {% endif %}

        <!-- 图片展示 -->
        {% set image_list = content.images.filter_by(is_deleted=False).all() if content.images else [] %}
        {% if image_list %}
        <div class="images-section">
            <div class="images-title">配图 ({{ image_list|length }}张)</div>
            <div class="images-grid">
                {% for image in image_list %}
                <div class="image-item" onclick="showImageModal('/static/uploads/{{ image.image_path }}')">
                    <img src="/static/uploads/{{ image.image_path }}" alt="文案配图" loading="lazy"
                         onerror="this.style.display='none'; console.error('图片加载失败:', '/static/uploads/{{ image.image_path }}');">
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="images-section">
            <div class="images-title">配图</div>
            <div class="no-images">
                <i class="bi bi-image text-muted"></i>
                <p class="text-muted">暂无配图</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 详细信息 -->
    <div class="details-section">
        <div class="details-title">详细信息</div>
        <div class="detail-item">
            <span class="detail-label">任务名称</span>
            <span class="detail-value">{{ content.task.name if content.task else '默认任务' }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">创建时间</span>
            <span class="detail-value">{{ content.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
        </div>
        {% if content.display_date %}
        <div class="detail-item">
            <span class="detail-label">发布日期</span>
            <span class="detail-value">{{ content.display_date.strftime('%Y-%m-%d') }}</span>
        </div>
        {% endif %}

        <!-- 话题标签 -->
        {% if content.topics_list and content.topics_list is iterable and content.topics_list is not string %}
        <div class="detail-item">
            <span class="detail-label">
                <i class="bi bi-tags"></i> 话题标签
            </span>
            <div class="tags-container">
                {% for topic in content.topics_list %}
                <span class="tag tag-topic">#{{ topic }}</span>
                {% endfor %}
            </div>
        </div>
        {% elif content.topics %}
        <div class="detail-item">
            <span class="detail-label">
                <i class="bi bi-tags"></i> 话题标签
            </span>
            <div class="tags-container">
                <span class="tag tag-topic">#{{ content.topics }}</span>
            </div>
        </div>
        {% endif %}

        <!-- @用户 -->
        {% if content.at_users_list and content.at_users_list is iterable and content.at_users_list is not string %}
        <div class="detail-item">
            <span class="detail-label">
                <i class="bi bi-at"></i> @用户
            </span>
            <div class="tags-container">
                {% for user in content.at_users_list %}
                <span class="tag tag-user">@{{ user.replace('@', '') }}</span>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 定位信息 -->
        {% if content.location_list and content.location_list is iterable and content.location_list is not string %}
        <div class="detail-item">
            <span class="detail-label">
                <i class="bi bi-geo-alt"></i> 定位信息
            </span>
            <div class="tags-container">
                {% for location in content.location_list %}
                <span class="tag tag-location">
                    <i class="bi bi-geo-alt-fill"></i> {{ location }}
                </span>
                {% endfor %}
            </div>
        </div>
        {% elif content.location %}
        <div class="detail-item">
            <span class="detail-label">
                <i class="bi bi-geo-alt"></i> 定位信息
            </span>
            <div class="tags-container">
                <span class="tag tag-location">
                    <i class="bi bi-geo-alt-fill"></i> {{ content.location }}
                </span>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 操作按钮 -->
    {% if content.workflow_status == 'pending_client_review' and content.client_review_status == 'pending' %}
    <div class="action-section">
        <div class="action-buttons">
            <button class="action-btn btn-approve" onclick="approveContent()">
                <i class="bi bi-check-lg me-2"></i>通过审核
            </button>
            <button class="action-btn btn-reject" onclick="showRejectModal()">
                <i class="bi bi-x-lg me-2"></i>驳回修改
            </button>
        </div>
    </div>
    {% endif %}

    <!-- 图片预览模态框 -->
    <div class="image-modal" id="imageModal" onclick="hideImageModal()">
        <button class="image-modal-close" onclick="hideImageModal()">
            <i class="bi bi-x"></i>
        </button>
        <img id="modalImage" src="" alt="图片预览">
    </div>

    <!-- 驳回理由模态框 - 支持驳回类型选择 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle"></i> 驳回文案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 驳回类型选择 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-list-check"></i> 请选择问题类型：
                        </label>
                        <div class="row g-2">
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="content">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="content" id="client_type_content" checked>
                                            <label class="form-check-label w-100" for="client_type_content">
                                                <i class="bi bi-file-text fs-4 text-primary d-block mb-1"></i>
                                                <strong class="small">文案问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    标题、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="image">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="image" id="client_type_image">
                                            <label class="form-check-label w-100" for="client_type_image">
                                                <i class="bi bi-image fs-4 text-success d-block mb-1"></i>
                                                <strong class="small">图片问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    质量、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="both">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="both" id="client_type_both">
                                            <label class="form-check-label w-100" for="client_type_both">
                                                <i class="bi bi-exclamation-circle fs-4 text-danger d-block mb-1"></i>
                                                <strong class="small">两者都有问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    全部重做
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 流转说明 -->
                    <div class="alert alert-info mb-3" id="clientFlowDescription">
                        <i class="bi bi-info-circle"></i>
                        <strong>流转说明：</strong>
                        <span id="clientFlowText">选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容</span>
                    </div>

                    <!-- 快捷理由选择 - 动态更新 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">常用驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3" id="clientQuickReasons">
                            <!-- 快捷理由按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 详细理由输入 -->
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-bold">详细说明：</label>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请详细说明需要修改的地方，以便创作者更好地理解和改进..." required></textarea>
                        <div class="form-text">
                            <i class="bi bi-lightbulb"></i>
                            建议提供具体的修改建议，这样可以提高修改效率
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i> 取消
                    </button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">
                        <i class="bi bi-check"></i> 确认驳回
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const shareKey = '{{ share_key }}';
        const contentId = {{ content.id }};

        // 返回上一页
        function goBack() {
            const accessKey = getAccessKey();
            let listUrl = `/client-review/${shareKey}`;
            if (accessKey) {
                listUrl += `?key=${accessKey}`;
            }

            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = listUrl;
            }
        }

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 显示图片预览
        function showImageModal(imageUrl) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageUrl;
            modal.style.display = 'flex';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 隐藏图片预览
        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // 显示驳回模态框
        function showRejectModal() {
            document.getElementById('rejectReason').value = '';

            // 重置驳回类型选择为默认值（文案问题）
            document.getElementById('client_type_content').checked = true;

            // 重置卡片选中状态
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => card.classList.remove('selected'));
            document.querySelector('#rejectModal .rejection-type-card[data-type="content"]').classList.add('selected');

            // 初始化驳回类型选择事件
            initClientRejectionTypeSelection();

            // 更新快捷理由和流转说明
            updateClientQuickReasonsAndFlow('content');

            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 初始化客户审核的驳回类型选择
        function initClientRejectionTypeSelection() {
            // 为客户审核的驳回类型卡片添加点击事件
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.dataset.type;
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                        // 更新卡片样式
                        typeCards.forEach(c => c.classList.remove('selected'));
                        this.classList.add('selected');
                        // 更新快捷理由和流转说明
                        updateClientQuickReasonsAndFlow(type);
                    }
                });
            });
        }

        // 更新客户审核的快捷理由和流转说明
        function updateClientQuickReasonsAndFlow(type) {
            const quickReasonsContainer = document.getElementById('clientQuickReasons');
            const flowText = document.getElementById('clientFlowText');

            // 清空现有的快捷理由
            quickReasonsContainer.innerHTML = '';

            // 根据驳回类型生成快捷理由
            let reasons = [];
            let flowDescription = '';

            switch(type) {
                case 'content':
                    reasons = [
                        '标题不够吸引人，建议重新设计',
                        '内容逻辑不清晰，需要重新组织',
                        '话题标签不合适，需要调整',
                        '文案长度不合适，需要调整',
                        '内容与品牌调性不符，需要调整风格',
                        '缺少关键信息，需要补充完整'
                    ];
                    flowDescription = '选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容';
                    break;
                case 'image':
                    reasons = [
                        '图片质量不符合要求，需要更换',
                        '图片内容与文案不匹配',
                        '图片数量不足，需要补充',
                        '图片尺寸或格式不合适',
                        '图片清晰度不够，影响展示效果',
                        '图片风格与品牌不符'
                    ];
                    flowDescription = '选择"图片问题"：文案将回到<strong>图片管理</strong>，需要重新上传或调整图片';
                    break;
                case 'both':
                    reasons = [
                        '文案和图片都需要重新设计',
                        '整体质量不符合标准，需要全面优化',
                        '内容和视觉效果都需要提升',
                        '文案与图片配合度不够，需要重新匹配',
                        '整体创意需要重新构思',
                        '品牌一致性问题，需要全面调整'
                    ];
                    flowDescription = '选择"两者都有问题"：文案将回到<strong>草稿状态</strong>，需要重新编写文案并上传图片';
                    break;
            }

            // 生成快捷理由按钮
            reasons.forEach(reason => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-outline-secondary btn-sm quick-reason-btn';
                button.textContent = reason.split('，')[0]; // 只显示简短描述
                button.onclick = () => fillRejectReason(reason);
                quickReasonsContainer.appendChild(button);
            });

            // 更新流转说明
            flowText.innerHTML = flowDescription;
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 通过审核
        function approveContent() {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent('approve', '');
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            // 获取选择的驳回类型
            const rejectionType = document.querySelector('input[name="rejection_type"]:checked').value;

            reviewContent('reject', reason, rejectionType);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 审核内容
        function reviewContent(action, comment, rejectionType) {
            // 禁用所有审核按钮防止重复提交
            const approveBtn = document.querySelector('.btn-approve');
            const rejectBtn = document.querySelector('.btn-reject');
            const approveHeaderBtn = document.querySelector('.btn-approve-header');
            const rejectHeaderBtn = document.querySelector('.btn-reject-header');
            const approveStatusBtn = document.querySelector('.btn-approve-status');
            const rejectStatusBtn = document.querySelector('.btn-reject-status');

            if (approveBtn) approveBtn.disabled = true;
            if (rejectBtn) rejectBtn.disabled = true;
            if (approveHeaderBtn) approveHeaderBtn.disabled = true;
            if (rejectHeaderBtn) rejectHeaderBtn.disabled = true;
            if (approveStatusBtn) approveStatusBtn.disabled = true;
            if (rejectStatusBtn) rejectStatusBtn.disabled = true;

            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);

            // 如果是驳回操作，添加驳回类型
            if (action === 'reject' && rejectionType) {
                formData.append('rejection_type', rejectionType);
            }

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');

                    // 立即更新页面状态
                    updatePageStatus(action);
                } else {
                    showToast(data.message, 'error');
                    // 重新启用所有按钮
                    if (approveBtn) approveBtn.disabled = false;
                    if (rejectBtn) rejectBtn.disabled = false;
                    if (approveHeaderBtn) approveHeaderBtn.disabled = false;
                    if (rejectHeaderBtn) rejectHeaderBtn.disabled = false;
                    if (approveStatusBtn) approveStatusBtn.disabled = false;
                    if (rejectStatusBtn) rejectStatusBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                // 重新启用所有按钮
                if (approveBtn) approveBtn.disabled = false;
                if (rejectBtn) rejectBtn.disabled = false;
                if (approveHeaderBtn) approveHeaderBtn.disabled = false;
                if (rejectHeaderBtn) rejectHeaderBtn.disabled = false;
                if (approveStatusBtn) approveStatusBtn.disabled = false;
                if (rejectStatusBtn) rejectStatusBtn.disabled = false;
            });
        }

        // 更新页面状态
        function updatePageStatus(action) {
            const statusBadge = document.querySelector('.status-badge');
            const actionSection = document.querySelector('.action-section');
            const headerButtons = document.querySelectorAll('.header-action-btn');
            const statusButtons = document.querySelector('.status-action-buttons');

            if (action === 'approve') {
                if (statusBadge) {
                    statusBadge.className = 'status-badge status-approved';
                    statusBadge.textContent = '待发布';
                }
            } else if (action === 'reject') {
                if (statusBadge) {
                    statusBadge.className = 'status-badge status-rejected';
                    statusBadge.textContent = '已驳回';
                }
            }

            // 隐藏所有操作按钮
            if (actionSection) {
                actionSection.style.display = 'none';
            }

            // 隐藏头部审核按钮
            headerButtons.forEach(btn => {
                btn.style.display = 'none';
            });

            // 隐藏状态旁边的审核按钮
            if (statusButtons) {
                statusButtons.style.display = 'none';
            }

            // 额外确保所有审核按钮都被隐藏
            const allApproveButtons = document.querySelectorAll('.btn-approve-status, .btn-approve-header');
            const allRejectButtons = document.querySelectorAll('.btn-reject-status, .btn-reject-header');

            allApproveButtons.forEach(btn => {
                btn.style.display = 'none';
            });

            allRejectButtons.forEach(btn => {
                btn.style.display = 'none';
            });
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 阻止图片模态框的事件冒泡
        document.getElementById('modalImage').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideImageModal();
            }
        });

        // 客户审核页面的驳回类型选择逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 驳回类型选择逻辑
            const typeCards = document.querySelectorAll('.rejection-type-card');
            const typeRadios = document.querySelectorAll('input[name="rejection_type"]');
            const quickReasonsContainer = document.getElementById('clientQuickReasons');

            // 不同类型的快捷理由
            const quickReasonsByType = {
                content: [
                    '标题不够吸引人，建议重新设计',
                    '内容质量不符合要求，需要重新编写',
                    '文案风格与品牌调性不符',
                    '话题选择不当，建议调整',
                    '内容过于简单，需要丰富细节'
                ],
                image: [
                    '图片质量需要提升，建议更换高质量图片',
                    '图片内容与文案不匹配',
                    '图片数量不足，建议增加',
                    '图片尺寸或格式不符合要求',
                    '图片版权问题，需要更换'
                ],
                both: [
                    '文案和图片整体质量都需要提升',
                    '内容与图片的配合度不够',
                    '整体效果不符合预期，需要重新制作',
                    '品牌调性体现不够，文案和图片都需调整'
                ]
            };



            // 监听单选框变化
            typeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    updateClientRejectionTypeUI();
                });
            });

            // 卡片点击事件
            typeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.dataset.type;
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                    updateClientRejectionTypeUI();
                });
            });

            function updateClientRejectionTypeUI() {
                const selectedType = document.querySelector('input[name="rejection_type"]:checked').value;

                // 更新卡片样式
                typeCards.forEach(card => {
                    const radio = card.querySelector('input[type="radio"]');
                    if (radio.checked) {
                        card.classList.add('selected');
                    } else {
                        card.classList.remove('selected');
                    }
                });

                // 更新快捷理由
                updateClientQuickReasons(selectedType);
            }

            function updateClientQuickReasons(type) {
                quickReasonsContainer.innerHTML = '';

                if (quickReasonsByType[type]) {
                    quickReasonsByType[type].forEach(reason => {
                        const btn = document.createElement('button');
                        btn.type = 'button';
                        btn.className = 'btn btn-outline-secondary btn-sm quick-reason-btn';
                        btn.textContent = reason;
                        btn.onclick = function() {
                            fillRejectReason(reason);
                            // 切换按钮选中状态
                            this.classList.toggle('selected');
                        };
                        quickReasonsContainer.appendChild(btn);
                    });
                }
            }

            // 初始化UI
            updateClientRejectionTypeUI();
        });
    </script>

    <style>
    .rejection-type-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #e9ecef;
    }

    .rejection-type-card:hover {
        border-color: #007bff;
        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
    }

    .rejection-type-card.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }

    .rejection-type-card .form-check-input {
        transform: scale(1.2);
    }

    .quick-reason-btn {
        transition: all 0.2s ease;
    }

    .quick-reason-btn:hover {
        transform: translateY(-1px);
    }

    .quick-reason-btn.selected {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    </style>
</body>
</html>
