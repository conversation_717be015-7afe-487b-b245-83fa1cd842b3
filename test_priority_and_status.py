#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优先级排序和状态更新
"""

import requests
import json

def test_priority_and_status():
    """测试优先级排序和状态更新"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔄 测试优先级排序和状态更新...")
    print("=" * 60)
    
    # 1. 测试获取文案（验证优先级排序）
    print("\n1. 测试获取文案（验证优先级排序）...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                priority = content.get('priority')
                priority_display = content.get('priority_display')
                
                print(f"✅ 获取成功！")
                print(f"文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                print(f"优先级: {priority} ({priority_display})")
                print(f"工作流状态: {content.get('workflow_status')}")
                
                # 验证是否获取到了高优先级的文案
                if priority == 'high':
                    print("✅ 优先级排序正确：获取到了高优先级文案")
                else:
                    print(f"⚠️ 优先级排序可能有问题：获取到的是{priority_display}优先级文案")
                
                # 2. 测试状态更新
                print(f"\n2. 测试状态更新 (ID: {content_id})...")
                
                update_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/test/123456',
                    'platform': '小红书',
                    'account': '测试账号@123'
                }
                
                print(f"发送数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=update_data,
                    timeout=10
                )
                
                print(f"状态更新状态码: {update_response.status_code}")
                print(f"响应内容类型: {update_response.headers.get('Content-Type', 'unknown')}")
                
                if update_response.status_code == 200:
                    try:
                        result = update_response.json()
                        print("✅ 状态更新成功！")
                        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    except json.JSONDecodeError as e:
                        print(f"❌ 响应不是有效的JSON: {e}")
                        print(f"响应内容: {update_response.text[:300]}...")
                else:
                    print("❌ 状态更新失败！")
                    print(f"响应内容: {update_response.text[:300]}...")
                
                # 3. 再次获取文案，验证下一个优先级
                print(f"\n3. 再次获取文案，验证下一个优先级...")
                
                response2 = requests.get(f'{base_url}/content', headers=headers, timeout=10)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        content2 = data2.get('content')
                        print(f"✅ 获取下一篇文案成功！")
                        print(f"文案ID: {content2.get('id')}")
                        print(f"标题: {content2.get('title')}")
                        print(f"优先级: {content2.get('priority')} ({content2.get('priority_display')})")
                    else:
                        print(f"ℹ️ 没有更多待发布文案: {data2.get('message')}")
                else:
                    print(f"❌ 获取下一篇文案失败: {response2.status_code}")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")

if __name__ == '__main__':
    test_priority_and_status()
