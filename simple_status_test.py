#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的状态更新测试
"""

import requests
import json

def simple_status_test():
    """简单的状态更新测试"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔄 简单状态更新测试...")
    
    # 直接测试一个已知的文案ID
    content_id = 94  # 从日志中看到的ID
    
    print(f"测试文案ID: {content_id}")
    
    update_data = {
        'status': 'success',
        'publish_url': 'https://xiaohongshu.com/test/123',
        'platform': '小红书',
        'account': '测试账号'
    }
    
    print(f"发送数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f'{base_url}/content/{content_id}/status',
            headers=headers,
            json=update_data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ 成功!")
                print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except:
                print("响应不是JSON格式")
        else:
            print("❌ 失败!")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == '__main__':
    simple_status_test()
