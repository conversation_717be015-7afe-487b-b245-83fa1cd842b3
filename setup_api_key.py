#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置API密钥脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.system_setting import SystemSetting
from app.utils.api_auth import generate_api_key

def setup_api_key():
    """设置API密钥"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已经存在API密钥
            existing_key = SystemSetting.query.filter_by(key='API_KEY').first()
            
            if existing_key:
                print(f"当前API密钥: {existing_key.value}")
                choice = input("是否要重新生成API密钥? (y/N): ").strip().lower()
                if choice != 'y':
                    print("保持现有API密钥")
                    return existing_key.value
            
            # 生成新的API密钥
            new_api_key = generate_api_key(32)
            
            if existing_key:
                # 更新现有密钥
                existing_key.value = new_api_key
                existing_key.description = 'API访问密钥'
                print("API密钥已更新")
            else:
                # 创建新密钥
                new_setting = SystemSetting(
                    key='API_KEY',
                    value=new_api_key,
                    description='API访问密钥'
                )
                db.session.add(new_setting)
                print("API密钥已创建")
            
            db.session.commit()
            
            print(f"新的API密钥: {new_api_key}")
            print("\n请将此密钥复制到测试页面的API密钥输入框中")
            print("=" * 50)
            print(f"API密钥: {new_api_key}")
            print("=" * 50)
            
            return new_api_key
            
        except Exception as e:
            print(f"设置API密钥失败: {e}")
            db.session.rollback()
            return None

if __name__ == '__main__':
    setup_api_key()
