#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建带图片的待发布文案
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.content import Content
from app.models.image import ContentImage

def create_pending_with_images():
    """创建带图片的待发布文案"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🖼️ 创建带图片的待发布文案...")
            print("=" * 50)
            
            # 方法1：将现有的带图片文案改为待发布状态
            print("\n1. 查找有图片的文案...")
            
            # 获取所有有图片的文案ID
            content_ids_with_images = db.session.query(ContentImage.content_id).filter_by(is_deleted=False).distinct().all()
            content_ids = [cid[0] for cid in content_ids_with_images]
            
            if content_ids:
                print(f"找到 {len(content_ids)} 篇有图片的文案: {content_ids}")
                
                # 选择第一篇文案改为待发布状态
                target_content_id = content_ids[0]
                content = Content.query.get(target_content_id)
                
                if content:
                    print(f"\n2. 将文案 {target_content_id} 改为待发布状态...")
                    print(f"原状态: {content.workflow_status}")
                    print(f"标题: {content.title}")
                    
                    # 更新状态为待发布
                    content.workflow_status = 'pending_publish'
                    content.publish_status = 'unpublished'
                    content.publish_priority = 'high'  # 设置为高优先级
                    content.publish_time = None  # 清除发布时间
                    
                    db.session.commit()
                    
                    print(f"✅ 文案 {target_content_id} 已更新为待发布状态")
                    
                    # 验证图片
                    images = ContentImage.get_by_content(target_content_id)
                    print(f"该文案有 {len(images)} 张图片:")
                    for img in images:
                        print(f"  - {img.image_path}")
                    
                    print(f"\n现在可以测试API了！文案ID: {target_content_id}")
                    
                else:
                    print(f"❌ 找不到文案 {target_content_id}")
            else:
                print("❌ 没有找到有图片的文案")
                
                # 方法2：为现有的待发布文案添加示例图片
                print("\n尝试为现有待发布文案添加示例图片...")
                
                pending_content = Content.query.filter(
                    Content.workflow_status == 'pending_publish',
                    Content.is_deleted == False
                ).first()
                
                if pending_content:
                    print(f"为文案 {pending_content.id} 添加示例图片...")
                    
                    # 创建示例图片记录
                    sample_image = ContentImage(
                        content_id=pending_content.id,
                        image_path='https://picsum.photos/400/400?random=1',
                        original_name='sample_image_1.jpg',
                        file_size=102400,  # 100KB
                        image_order=1
                    )
                    
                    db.session.add(sample_image)
                    db.session.commit()
                    
                    print(f"✅ 已为文案 {pending_content.id} 添加示例图片")
                else:
                    print("❌ 没有找到待发布文案")
            
        except Exception as e:
            print(f"操作失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_pending_with_images()
