<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ client.name }} - 文案审核</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .content-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .content-card .card {
            border: none;
            background: transparent;
        }
        .content-card .card-body {
            padding: 1.5rem;
        }
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        .image-thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
        }
        .filter-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .btn-group-custom .btn {
            border-radius: 20px;
            margin: 0 2px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
        }
        .loading {
            text-align: center;
            padding: 2rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="bi bi-person-check"></i> {{ client.name }}</h1>
                    <p class="mb-0">文案审核中心</p>
                </div>
                <div class="col-md-4 text-end">
                    {% if stats and stats.days_remaining is not none %}
                        {% if stats.days_remaining > 0 %}
                            <span class="badge bg-success fs-6">
                                <i class="bi bi-clock"></i> 还有 {{ stats.days_remaining }} 天有效
                            </span>
                        {% else %}
                            <span class="badge bg-danger fs-6">
                                <i class="bi bi-exclamation-triangle"></i> 链接即将过期
                            </span>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-warning" id="pendingCount">-</h3>
                        <small class="text-muted">待审核</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-success" id="approvedCount">-</h3>
                        <small class="text-muted">已通过</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-danger" id="rejectedCount">-</h3>
                        <small class="text-muted">已驳回</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h3 class="text-info" id="totalCount">-</h3>
                        <small class="text-muted">总计</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="taskNameFilter" placeholder="输入任务名称">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">批次名称</label>
                        <input type="text" class="form-control" id="batchNameFilter" placeholder="输入批次名称">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">审核状态</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="pending">待审核</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已驳回</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-primary me-2" onclick="loadContents()">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文案列表 -->
        <div id="contentsList">
            <div class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载文案列表...</p>
            </div>
        </div>

        <!-- 分页 -->
        <nav id="pagination" style="display: none;">
            <ul class="pagination justify-content-center">
                <!-- 分页内容将通过JavaScript动态生成 -->
            </ul>
        </nav>
    </div>

    <!-- 文案详情模态框 -->
    <div class="modal fade" id="contentModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文案详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="contentModalBody">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑文案模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑文案</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <div class="mb-3">
                            <label for="editTitle" class="form-label">标题</label>
                            <input type="text" class="form-control" id="editTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="editContent" class="form-label">内容</label>
                            <textarea class="form-control" id="editContent" rows="8" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTopics" class="form-label">话题标签</label>
                            <input type="text" class="form-control" id="editTopics" placeholder="例如：#美食 #生活">
                        </div>
                        <div class="mb-3">
                            <label for="editLocation" class="form-label">位置信息</label>
                            <input type="text" class="form-control" id="editLocation" placeholder="位置信息">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEdit()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">驳回文案</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">驳回理由</label>
                        <textarea class="form-control" id="rejectReason" rows="4" placeholder="请输入驳回理由..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">确认驳回</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="previewImage" src="" class="img-fluid" style="max-height: 70vh;">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const shareKey = '{{ share_key }}';
        let currentPage = 1;
        let currentContentId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadContents();
        });

        // 加载统计信息
        function loadStats() {
            fetch(`/client-review/api/${shareKey}/stats`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('pendingCount').textContent = stats.pending_count || 0;
                        document.getElementById('approvedCount').textContent = stats.approved_count || 0;
                        document.getElementById('rejectedCount').textContent = stats.rejected_count || 0;
                        document.getElementById('totalCount').textContent = stats.total_count || 0;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                    // 显示错误状态
                    document.getElementById('pendingCount').textContent = '?';
                    document.getElementById('approvedCount').textContent = '?';
                    document.getElementById('rejectedCount').textContent = '?';
                    document.getElementById('totalCount').textContent = '?';
                });
        }

        // 加载文案列表
        function loadContents(page = 1) {
            currentPage = page;
            
            // 获取筛选参数
            const taskName = document.getElementById('taskNameFilter').value;
            const batchName = document.getElementById('batchNameFilter').value;
            const status = document.getElementById('statusFilter').value;
            
            // 构建查询参数
            const params = new URLSearchParams({
                page: page,
                per_page: 10
            });
            
            if (taskName) params.append('task_name', taskName);
            if (batchName) params.append('batch_name', batchName);
            if (status) params.append('status', status);
            
            // 显示加载状态
            document.getElementById('contentsList').innerHTML = `
                <div class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载文案列表...</p>
                </div>
            `;
            
            fetch(`/client-review/api/${shareKey}/contents?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderContents(data.contents);
                        renderPagination(data.pagination);
                    } else {
                        showError(data.message);
                    }
                })
                .catch(error => {
                    console.error('加载文案列表失败:', error);
                    showError('加载文案列表失败，请重试');
                });
        }

        // 渲染文案列表
        function renderContents(contents) {
            const container = document.getElementById('contentsList');
            
            if (contents.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-inbox fs-1"></i>
                        <h5 class="mt-3">暂无文案</h5>
                        <p>当前筛选条件下没有找到文案</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            contents.forEach(content => {
                const statusBadge = getStatusBadge(content.workflow_status, content.client_review_status);
                const imageGallery = renderImageGallery(content.images);
                
                html += `
                    <div class="content-card" onclick="viewContent(${content.id})" data-content-id="${content.id}">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">${content.title}</h6>
                                            ${statusBadge}
                                        </div>
                                        <p class="card-text text-muted">${content.content.substring(0, 150)}${content.content.length > 150 ? '...' : ''}</p>
                                        <div class="small text-muted mb-2">
                                            ${content.task_name ? `<span class="badge bg-light text-dark me-1">${content.task_name}</span>` : ''}
                                            ${content.batch_name ? `<span class="badge bg-light text-dark me-1">${content.batch_name}</span>` : ''}
                                            <span class="ms-2"><i class="bi bi-calendar"></i> ${formatDate(content.created_at)}</span>
                                        </div>
                                        ${renderRejectionReason(content.latest_rejection)}
                                        ${imageGallery}
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="btn-group-custom" onclick="event.stopPropagation()">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewContent(${content.id})">
                                                <i class="bi bi-eye"></i> 查看
                                            </button>
                                            ${(content.workflow_status === 'pending_client_review' && content.client_review_status === 'pending') ? `
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="editContent(${content.id})">
                                                    <i class="bi bi-pencil"></i> 编辑
                                                </button>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="approveContent(${content.id})">
                                                    <i class="bi bi-check"></i> 通过
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="rejectContent(${content.id})">
                                                    <i class="bi bi-x"></i> 驳回
                                                </button>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 获取状态徽章
        function getStatusBadge(workflowStatus, clientReviewStatus) {
            // 优先检查客户审核状态
            if (clientReviewStatus === 'approved') {
                return '<span class="status-badge bg-success">已通过</span>';
            } else if (clientReviewStatus === 'rejected') {
                return '<span class="status-badge bg-danger">已驳回</span>';
            }

            // 然后检查工作流状态
            switch (workflowStatus) {
                case 'pending_client_review':
                    return '<span class="status-badge bg-warning text-dark">待审核</span>';
                case 'draft':
                    return '<span class="status-badge bg-secondary">草稿</span>';
                case 'first_reviewed':
                    return '<span class="status-badge bg-info">初审通过</span>';
                case 'image_uploaded':
                    return '<span class="status-badge bg-primary">图片已上传</span>';
                case 'image_completed':
                    return '<span class="status-badge bg-success">图片完成</span>';
                case 'final_reviewed':
                    return '<span class="status-badge bg-success">终审通过</span>';
                case 'published':
                    return '<span class="status-badge bg-success">已发布</span>';
                default:
                    return '<span class="status-badge bg-secondary">未知状态</span>';
            }
        }

        // 渲染图片画廊
        function renderImageGallery(images) {
            if (!images || images.length === 0) {
                return '<p class="small text-muted"><i class="bi bi-image"></i> 暂无配图</p>';
            }
            
            let html = '<div class="image-gallery mt-2" onclick="event.stopPropagation()">';
            images.slice(0, 6).forEach(image => {
                html += `<img src="/static/uploads/${image.thumbnail_path}" class="image-thumbnail" onclick="showImage('/static/uploads/${image.image_path}')" title="${image.original_name}">`;
            });
            
            if (images.length > 6) {
                html += `<div class="image-thumbnail d-flex align-items-center justify-content-center bg-light text-muted">+${images.length - 6}</div>`;
            }
            
            html += '</div>';
            return html;
        }

        // 渲染驳回理由
        function renderRejectionReason(rejection) {
            if (!rejection) {
                return '';
            }

            const rejectionType = rejection.is_client ? '客户驳回' : '内部驳回';
            const badgeClass = rejection.is_client ? 'bg-danger' : 'bg-warning';

            return `
                <div class="rejection-reason mt-2 p-2" style="background-color: #fff5f5; border-left: 3px solid #dc3545; border-radius: 4px;" onclick="event.stopPropagation()">
                    <div class="d-flex justify-content-between align-items-start mb-1">
                        <span class="badge ${badgeClass}">${rejectionType}</span>
                        <small class="text-muted">${formatDate(rejection.created_at)}</small>
                    </div>
                    <div class="rejection-text small text-dark">
                        ${rejection.reason}
                    </div>
                </div>
            `;
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        }

        // 渲染分页
        function renderPagination(pagination) {
            const container = document.getElementById('pagination');
            
            if (pagination.pages <= 1) {
                container.style.display = 'none';
                return;
            }
            
            let html = '';
            
            // 上一页
            if (pagination.has_prev) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadContents(${pagination.page - 1})">上一页</a></li>`;
            }
            
            // 页码
            for (let i = 1; i <= pagination.pages; i++) {
                if (i === pagination.page) {
                    html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    html += `<li class="page-item"><a class="page-link" href="#" onclick="loadContents(${i})">${i}</a></li>`;
                }
            }
            
            // 下一页
            if (pagination.has_next) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="loadContents(${pagination.page + 1})">下一页</a></li>`;
            }
            
            container.querySelector('.pagination').innerHTML = html;
            container.style.display = 'block';
        }

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 查看文案详情
        function viewContent(contentId) {
            const accessKey = getAccessKey();
            let detailUrl = `/client-review/${shareKey}/content/${contentId}`;
            if (accessKey) {
                detailUrl += `?key=${accessKey}`;
            }
            window.location.href = detailUrl;
        }

        // 编辑文案
        function editContent(contentId) {
            currentContentId = contentId;
            // 这里可以实现编辑文案的逻辑
            const modal = new bootstrap.Modal(document.getElementById('editModal'));
            modal.show();
        }

        // 保存编辑
        function saveEdit() {
            const title = document.getElementById('editTitle').value.trim();
            const content = document.getElementById('editContent').value.trim();
            const topics = document.getElementById('editTopics').value.trim();
            const location = document.getElementById('editLocation').value.trim();
            
            if (!title || !content) {
                alert('标题和内容不能为空');
                return;
            }
            
            const formData = new FormData();
            formData.append('title', title);
            formData.append('content', content);
            formData.append('topics', topics);
            formData.append('location', location);
            
            fetch(`/client-review/api/${shareKey}/contents/${currentContentId}/edit`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    loadContents(currentPage);
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                alert('编辑失败: ' + error.message);
            });
        }

        // 审核通过
        function approveContent(contentId) {
            if (!confirm('确定要通过这篇文案吗？')) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'approve');
            
            fetch(`/client-review/api/${shareKey}/contents/${contentId}/review`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    loadContents(currentPage);
                    loadStats();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                alert('审核失败: ' + error.message);
            });
        }

        // 驳回文案
        function rejectContent(contentId) {
            currentContentId = contentId;
            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'reject');
            formData.append('review_comment', reason);
            
            fetch(`/client-review/api/${shareKey}/contents/${currentContentId}/review`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
                    document.getElementById('rejectReason').value = '';
                    loadContents(currentPage);
                    loadStats();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                alert('驳回失败: ' + error.message);
            });
        }

        // 显示图片
        function showImage(imageSrc) {
            document.getElementById('previewImage').src = imageSrc;
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
        }

        // 重置筛选条件
        function resetFilters() {
            document.getElementById('taskNameFilter').value = '';
            document.getElementById('batchNameFilter').value = '';
            document.getElementById('statusFilter').value = '';
            loadContents(1);
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('contentsList').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
