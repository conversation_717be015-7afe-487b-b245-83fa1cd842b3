#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试publish_error字段修复
"""

import requests
import json

def test_publish_error_fix():
    """测试publish_error字段修复"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔧 测试publish_error字段修复...")
    print("=" * 60)
    
    try:
        # 获取一篇文案
        response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                
                print(f"获取文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                
                # 发送带有明确提示信息的成功状态
                test_message = "🎯 字段修复测试！这条信息必须显示在页面上！"
                success_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/field-fix-test',
                    'platform': '小红书',
                    'account': '字段修复测试账号',
                    'message': test_message
                }
                
                print(f"发送提示信息: {test_message}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=success_data,
                    timeout=10
                )
                
                if update_response.status_code == 200:
                    result = update_response.json()
                    if result.get('success'):
                        print("✅ 状态更新成功！")
                        print(f"\n现在请立即查看页面：")
                        print(f"http://127.0.0.1:5000/simple/publish-status-manage?status=published")
                        print(f"\n查找文案ID {content_id}，应该能看到：")
                        print(f"提示信息列: 时间戳 + '{test_message}'")
                        print(f"\n如果还是看不到，说明publish_error字段仍然没有被正确更新到数据库")
                    else:
                        print(f"❌ 状态更新失败: {result.get('error')}")
                else:
                    print(f"❌ 请求失败: {update_response.status_code}")
                    print(f"响应: {update_response.text}")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 字段修复测试完成！")
    print("\n请检查Flask日志，应该能看到：")
    print("1. ✅ 设置提示信息: '...'")
    print("2. ✅ UPDATE语句包含publish_error字段")
    print("3. ✅ Content表更新已提交")

if __name__ == '__main__':
    test_publish_error_fix()
