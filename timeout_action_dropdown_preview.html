<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超时处理策略下拉框预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear"></i> 系统设置 - 超时处理策略
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6 class="text-muted">修改前（文本输入框）：</h6>
                            <div class="border p-3 rounded bg-light">
                                <label class="form-label fw-bold">超时处理策略</label>
                                <small class="text-muted d-block mb-2">超时处理策略：保持超时状态</small>
                                <input type="text" class="form-control" value="keep_timeout" readonly>
                                <small class="text-danger mt-1 d-block">❌ 需要记忆英文代码，容易输入错误</small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6 class="text-success">修改后（中文下拉框）：</h6>
                            <div class="border p-3 rounded bg-success bg-opacity-10">
                                <label class="form-label fw-bold">超时处理策略</label>
                                <small class="text-muted d-block mb-2">发布超时处理策略</small>
                                
                                <select class="form-select" id="PUBLISH_TIMEOUT_ACTION" data-setting-key="PUBLISH_TIMEOUT_ACTION">
                                    <option value="keep_timeout" selected>
                                        保持超时状态（需手动处理）
                                    </option>
                                    <option value="auto_reset">
                                        自动重置为待发布
                                    </option>
                                    <option value="auto_fail">
                                        自动标记为发布失败
                                    </option>
                                </select>
                                
                                <small class="form-text text-muted mt-2">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>策略说明：</strong>
                                    <ul class="mb-0 mt-1">
                                        <li><strong>保持超时状态：</strong>安全选项，需要人工检查和处理</li>
                                        <li><strong>自动重置：</strong>自动重试发布，可能重复发布</li>
                                        <li><strong>自动失败：</strong>自动清理状态，可能误判成功的发布</li>
                                    </ul>
                                </small>
                                <small class="text-success mt-2 d-block">✅ 中文显示，选择方便，包含详细说明</small>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb"></i> 改进效果：</h6>
                            <ul class="mb-0">
                                <li><strong>用户友好：</strong>中文显示，无需记忆英文代码</li>
                                <li><strong>防止错误：</strong>下拉框选择，避免输入错误</li>
                                <li><strong>详细说明：</strong>每个选项都有清晰的说明</li>
                                <li><strong>专业外观：</strong>界面更加美观和专业</li>
                            </ul>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle"></i> 策略选择建议：</h6>
                            <ul class="mb-0">
                                <li><strong>🔒 保持超时状态：</strong>推荐用于重要内容，最安全可靠</li>
                                <li><strong>🔄 自动重置：</strong>适合自动化程度高的场景，需要监控重复发布</li>
                                <li><strong>❌ 自动失败：</strong>适合快速清理状态，但可能误判成功的发布</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-code-slash"></i> 技术实现
                        </h6>
                    </div>
                    <div class="card-body">
                        <h6>模板更新：</h6>
                        <pre class="bg-light p-3 rounded"><code>&lt;select class="form-select" id="{{ setting.key }}" data-setting-key="{{ setting.key }}"&gt;
    &lt;option value="keep_timeout" {% if setting.value == 'keep_timeout' %}selected{% endif %}&gt;
        保持超时状态（需手动处理）
    &lt;/option&gt;
    &lt;option value="auto_reset" {% if setting.value == 'auto_reset' %}selected{% endif %}&gt;
        自动重置为待发布
    &lt;/option&gt;
    &lt;option value="auto_fail" {% if setting.value == 'auto_fail' %}selected{% endif %}&gt;
        自动标记为发布失败
    &lt;/option&gt;
&lt;/select&gt;</code></pre>

                        <h6 class="mt-3">JavaScript兼容性：</h6>
                        <p class="text-muted">现有的JavaScript代码通过 <code>element.value</code> 获取值，完全兼容下拉框，无需修改。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟下拉框变化效果
        document.getElementById('PUBLISH_TIMEOUT_ACTION').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            console.log('选择的策略:', selectedOption.value, '-', selectedOption.text);
        });
    </script>
</body>
</html>
