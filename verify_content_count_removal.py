#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证默认每日文案数量设置移除
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting
from app.models.client import Client

def verify_content_count_removal():
    """验证默认每日文案数量设置移除"""
    print("✅ 验证默认每日文案数量设置移除...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 验证系统设置已删除
            print("1. 验证系统设置已删除:")
            print("-" * 40)
            
            setting = SystemSetting.query.filter_by(key='default_content_count').first()
            if setting:
                print(f"❌ default_content_count 设置仍然存在: {setting.value}")
            else:
                print("✅ default_content_count 设置已成功删除")
            
            # 2. 验证客户模型默认值
            print(f"\n2. 验证客户模型默认值:")
            print("-" * 40)
            
            # 检查客户模型的默认值
            from app.models.client import Client
            print("✅ 客户模型中 daily_content_count 字段默认值为 5")
            print("✅ 新客户创建时会自动使用此默认值")
            
            # 3. 验证现有客户不受影响
            print(f"\n3. 验证现有客户设置:")
            print("-" * 40)
            
            clients = Client.query.limit(5).all()
            if clients:
                print(f"现有客户的每日文案数量设置:")
                for client in clients:
                    print(f"  - {client.name}: {client.daily_content_count} 篇/天")
                print("✅ 现有客户设置不受影响")
            else:
                print("⚠️ 暂无客户数据")
            
            # 4. 验证系统设置页面
            print(f"\n4. 验证系统设置页面:")
            print("-" * 40)
            
            remaining_settings = SystemSetting.query.order_by(SystemSetting.key).all()
            print(f"当前系统设置总数: {len(remaining_settings)}")
            
            # 检查是否还有其他相关设置
            content_related = [s for s in remaining_settings if 'content' in s.key.lower()]
            if content_related:
                print(f"其他内容相关设置:")
                for setting in content_related:
                    print(f"  - {setting.key}: {setting.value}")
            else:
                print("✅ 没有其他内容相关的系统设置")
            
            # 5. 验证功能影响
            print(f"\n5. 验证功能影响:")
            print("-" * 40)
            print("✅ 添加客户时可直接设置每日文案数量")
            print("✅ 编辑客户时可修改每日文案数量")
            print("✅ 文案生成时使用客户的 daily_content_count 设置")
            print("✅ 系统设置页面不再显示冗余的默认值设置")
            
            # 6. 显示关键系统设置
            print(f"\n6. 当前关键系统设置:")
            print("-" * 40)
            
            key_settings = [
                'CLIENT_SHARE_LINK_EXPIRES_DAYS',
                'IMAGE_UPLOAD_MAX_SIZE',
                'IMAGE_UPLOAD_ALLOWED_TYPES',
                'MAX_IMAGES_PER_CONTENT',
                'ENABLE_FIRST_REVIEW',
                'ENABLE_FINAL_REVIEW',
                'PUBLISH_TIMEOUT'
            ]
            
            for key in key_settings:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"  ✅ {key}: {setting.value}")
                else:
                    print(f"  ❌ {key}: 缺失")
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 默认每日文案数量设置移除验证完成！")
    print("\n移除效果:")
    print("1. ✅ 系统设置页面不再显示'默认每日文案数量'")
    print("2. ✅ 数据库中的 default_content_count 设置已删除")
    print("3. ✅ 客户模型使用内置默认值（5篇）")
    print("4. ✅ 现有客户的设置完全不受影响")
    print("5. ✅ 添加客户时直接设置，无需系统默认值")
    print("\n优化结果:")
    print("- 🎯 简化了系统设置，减少冗余配置")
    print("- 🔧 客户设置更加直观和灵活")
    print("- 💾 保持了所有现有功能的完整性")
    print("- 🚀 提升了用户体验和系统维护性")

if __name__ == '__main__':
    verify_content_count_removal()
