"""
终审管理视图
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import and_
import json

from app.models import db
from app.models.content import Content, ContentHistory, RejectionReason
from app.forms.content import ContentReviewForm
from app.utils.decorators import permission_required

# 创建蓝图
final_review_bp = Blueprint('final_review', __name__, url_prefix='/final-review')

@final_review_bp.route('/')
@login_required
@permission_required('final_review.view')
def index():
    """终审管理首页"""
    # 获取所有待终审的文案（图片已上传）
    contents = Content.query.filter(Content.workflow_status == 'images_uploaded').all()
    return render_template('final_review/index.html', contents=contents, title='终审管理')

@final_review_bp.route('/<int:content_id>')
@login_required
@permission_required('final_review.view')
def view(content_id):
    """查看待终审文案详情"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'images_uploaded':
        flash('该文案不在终审状态', 'warning')
        return redirect(url_for('final_review.index'))
    
    # 解析图片
    image_urls = []
    if content.image_urls:
        try:
            image_urls = json.loads(content.image_urls)
        except:
            image_urls = []
    
    form = ContentReviewForm()
    return render_template('final_review/view.html', 
                          content=content, 
                          image_urls=image_urls, 
                          form=form, 
                          title='文案终审')

@final_review_bp.route('/<int:content_id>/approve', methods=['POST'])
@login_required
@permission_required('final_review.approve')
def approve(content_id):
    """终审通过"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'images_uploaded':
        flash('该文案不在终审状态', 'warning')
        return redirect(url_for('final_review.index'))
    
    # 更新文案状态
    content.workflow_status = 'final_reviewed'
    content.internal_review_status = 'final_approved'
    
    # 检查是否需要客户审核
    if content.client and content.client.need_review:
        content.workflow_status = 'waiting_client_review'
    else:
        # 不需要客户审核，直接设为待发布
        content.workflow_status = 'ready_to_publish'
        content.client_review_status = 'approved'  # 自动设为客户已通过
    
    content.reviewer_id = current_user.id
    content.review_time = db.func.now()
    
    db.session.commit()
    flash('文案终审已通过', 'success')
    return redirect(url_for('final_review.index'))

@final_review_bp.route('/<int:content_id>/reject', methods=['POST'])
@login_required
@permission_required('final_review.reject')
def reject(content_id):
    """终审拒绝"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'images_uploaded':
        flash('该文案不在终审状态', 'warning')
        return redirect(url_for('final_review.index'))
    
    form = ContentReviewForm()
    if form.validate_on_submit():
        # 记录拒绝理由
        reason = RejectionReason(
            content_id=content_id,
            reason=form.reason.data,
            created_by=current_user.id
        )
        db.session.add(reason)
        
        # 将状态回退到初审通过状态，需要重新上传图片
        content.workflow_status = 'first_reviewed'
        
        db.session.commit()
        flash('文案已被拒绝', 'info')
        return redirect(url_for('final_review.index'))
    
    flash('表单验证失败', 'danger')
    return redirect(url_for('final_review.view', content_id=content_id))

@final_review_bp.route('/<int:content_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('final_review.edit')
def edit(content_id):
    """编辑待终审文案"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'images_uploaded':
        flash('该文案不在终审状态', 'warning')
        return redirect(url_for('final_review.index'))
    
    if request.method == 'POST':
        # 保存文案历史版本
        history = ContentHistory(
            content_id=content.id,
            title=content.title,
            content=content.content,
            editor_id=current_user.id
        )
        db.session.add(history)
        
        # 更新文案内容
        content.title = request.form.get('title')
        content.content = request.form.get('content')
        content.topics = request.form.get('topics')
        content.location = request.form.get('location')
        
        db.session.commit()
        flash('文案已更新', 'success')
        return redirect(url_for('final_review.view', content_id=content_id))
    
    return render_template('final_review/edit.html', content=content, title='编辑文案')

@final_review_bp.route('/batch-approve', methods=['POST'])
@login_required
@permission_required('final_review.batch')
def batch_approve():
    """批量通过终审"""
    content_ids = request.form.getlist('content_ids')
    if not content_ids:
        flash('未选择任何文案', 'warning')
        return redirect(url_for('final_review.index'))
    
    contents = Content.query.filter(
        and_(
            Content.id.in_(content_ids),
            Content.workflow_status == 'images_uploaded'
        )
    ).all()
    
    approved_count = 0
    for content in contents:
        content.internal_review_status = 'final_approved'
        
        # 检查是否需要客户审核
        if content.client and content.client.need_review:
            content.workflow_status = 'waiting_client_review'
        else:
            # 不需要客户审核，直接设为待发布
            content.workflow_status = 'ready_to_publish'
            content.client_review_status = 'approved'  # 自动设为客户已通过
        
        content.reviewer_id = current_user.id
        content.review_time = db.func.now()
        approved_count += 1
    
    db.session.commit()
    flash(f'已批量通过 {approved_count} 篇文案的终审', 'success')
    return redirect(url_for('final_review.index')) 