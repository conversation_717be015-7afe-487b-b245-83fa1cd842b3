#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为演示目的，创建一些已发布的文案数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.content import Content

def demo_published_stats():
    """为演示目的，创建一些已发布的文案数据"""
    print("🎭 创建演示数据...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找一些现有文案，修改其发布状态
            print("1. 查找现有文案:")
            print("-" * 40)
            
            # 查找客户ID为1的文案（康师傅）
            contents = Content.query.filter_by(client_id=1, is_deleted=False).all()
            
            if contents:
                print(f"🔍 找到客户1的 {len(contents)} 篇文案")
                
                # 将第一篇文案设置为已发布
                if len(contents) >= 1:
                    content1 = contents[0]
                    content1.publish_status = 'published'
                    content1.workflow_status = 'published'
                    print(f"  ✅ 设置文案 {content1.id} 为已发布状态")
                
                # 如果有第二篇，也设置为已发布
                if len(contents) >= 2:
                    content2 = contents[1]
                    content2.publish_status = 'published'
                    content2.workflow_status = 'published'
                    print(f"  ✅ 设置文案 {content2.id} 为已发布状态")
                
                try:
                    db.session.commit()
                    print(f"  💾 已保存更改")
                except Exception as e:
                    db.session.rollback()
                    print(f"  ❌ 保存失败: {e}")
                    return
            else:
                print("❌ 没有找到客户1的文案")
            
            # 2. 验证统计结果
            print(f"\n2. 验证统计结果:")
            print("-" * 40)
            
            from app.models.client import Client
            from sqlalchemy import func
            
            clients = Client.query.filter_by(status=True).limit(3).all()
            
            for client in clients:
                # 查询该客户的已发布文案数量
                published_count = db.session.query(func.count(Content.id)).filter(
                    Content.client_id == client.id,
                    Content.publish_status == 'published',
                    Content.is_deleted == False
                ).scalar() or 0
                
                # 查询该客户的总文案数量
                total_count = db.session.query(func.count(Content.id)).filter(
                    Content.client_id == client.id,
                    Content.is_deleted == False
                ).scalar() or 0
                
                print(f"📋 客户: {client.name} (ID: {client.id})")
                print(f"  - 已发布数量: {published_count}")
                print(f"  - 总文案数量: {total_count}")
                print()
            
            # 3. 访问提示
            print("3. 访问客户管理页面:")
            print("-" * 40)
            
            print("🔗 现在可以访问客户管理页面查看统计效果:")
            print("  http://127.0.0.1:5000/simple/clients")
            print()
            print("✅ 预期效果:")
            print("  - 客户列表中会显示'已发布数量'列")
            print("  - 康师傅客户应该显示已发布数量 > 0")
            print("  - 其他客户显示已发布数量为 0")
            print("  - 已发布数量用蓝色徽章显示")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 演示数据创建完成！")
    print("\n📋 总结:")
    print("1. ✅ 为康师傅客户创建了已发布文案")
    print("2. ✅ 统计功能可以正常显示")
    print("3. 🔗 可以访问客户管理页面查看效果")

if __name__ == '__main__':
    demo_published_stats()
