#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建超级管理员用户
"""

from flask import current_app
from app.models import db
from app.models.user import User, Role
from app.commands import system_cli

@system_cli.command('create-superuser')
def create_superuser():
    """创建超级管理员用户"""
    print("开始创建超级管理员用户...")
    
    # 检查是否已存在超级管理员
    super_admin_role = Role.query.filter_by(name='超级管理员').first()
    if not super_admin_role:
        print("错误: 超级管理员角色不存在，请先运行 'flask menu init' 初始化菜单权限")
        return
    
    # 检查是否已存在超级管理员用户
    existing_super_admin = User.query.join(User.roles).filter(Role.name == '超级管理员').first()
    if existing_super_admin:
        print(f"超级管理员用户已存在: {existing_super_admin.username}")
        return
    
    # 创建超级管理员用户
    username = input("请输入超级管理员用户名 (默认: admin): ").strip() or 'admin'
    email = input("请输入超级管理员邮箱: ").strip()
    password = input("请输入超级管理员密码: ").strip()
    
    if not email or not password:
        print("错误: 邮箱和密码不能为空")
        return
    
    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=username).first():
        print(f"错误: 用户名 '{username}' 已存在")
        return
    
    if User.query.filter_by(email=email).first():
        print(f"错误: 邮箱 '{email}' 已存在")
        return
    
    # 创建用户
    user = User(
        username=username,
        email=email,
        real_name='超级管理员',
        is_active=True
    )
    user.password = password
    
    # 分配超级管理员角色
    user.roles.append(super_admin_role)
    
    db.session.add(user)
    db.session.commit()
    
    print(f"超级管理员用户创建成功: {username}")
    print("您现在可以使用此账户登录系统")

if __name__ == '__main__':
    from app import create_app
    app = create_app()
    with app.app_context():
        create_superuser() 