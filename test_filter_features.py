#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试筛选功能
"""

import requests

def test_filter_features():
    """测试筛选功能"""
    print("🔍 测试筛选功能...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查客户筛选下拉框
            if 'client-filter' in response.text:
                print("✅ 找到客户筛选下拉框")
            else:
                print("❌ 未找到客户筛选下拉框")
            
            # 检查优先级筛选下拉框
            if 'priority-filter' in response.text:
                print("✅ 找到优先级筛选下拉框")
            else:
                print("❌ 未找到优先级筛选下拉框")
            
            # 检查筛选函数
            if 'applyFilters' in response.text:
                print("✅ 找到筛选应用函数")
            else:
                print("❌ 未找到筛选应用函数")
            
            # 检查全部客户选项
            if '全部客户' in response.text:
                print("✅ 找到全部客户选项")
            else:
                print("❌ 未找到全部客户选项")
            
            # 检查全部优先级选项
            if '全部优先级' in response.text:
                print("✅ 找到全部优先级选项")
            else:
                print("❌ 未找到全部优先级选项")
            
            # 检查优先级选项
            priority_options = ['高优先级', '普通优先级', '低优先级']
            found_priority_options = 0
            for option in priority_options:
                if option in response.text:
                    found_priority_options += 1
            
            print(f"✅ 找到 {found_priority_options}/{len(priority_options)} 个优先级选项")
            
            # 检查状态筛选按钮组
            if 'btn-group' in response.text:
                print("✅ 找到状态筛选按钮组")
            else:
                print("❌ 未找到状态筛选按钮组")
            
            # 检查状态筛选按钮
            status_buttons = ['全部', '待发布', '发布中', '已发布', '发布失败', '发布超时']
            found_status_buttons = 0
            for button_text in status_buttons:
                if button_text in response.text:
                    found_status_buttons += 1
            
            print(f"✅ 找到 {found_status_buttons}/{len(status_buttons)} 个状态筛选按钮")
            
            # 检查响应式布局
            if 'col-md-8' in response.text and 'col-md-4' in response.text:
                print("✅ 找到响应式布局")
            else:
                print("❌ 未找到响应式布局")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 筛选功能测试完成！")
    print("\n新增筛选功能：")
    print("1. ✅ 客户筛选 - 下拉框选择特定客户的文案")
    print("2. ✅ 优先级筛选 - 下拉框选择特定优先级的文案")
    print("3. ✅ 组合筛选 - 多个筛选条件可以同时使用")
    print("4. ✅ 状态筛选 - 原有的发布状态筛选保留")
    print("5. ✅ 响应式布局 - 左侧状态按钮，右侧筛选下拉框")
    print("\n筛选组合示例：")
    print("- 待发布 + 高优先级 + 特定客户")
    print("- 已发布 + 普通优先级 + 全部客户")
    print("- 发布失败 + 低优先级 + 特定客户")
    print("\n使用方法：")
    print("1. 选择发布状态（点击状态按钮）")
    print("2. 选择客户（下拉框选择）")
    print("3. 选择优先级（下拉框选择）")
    print("4. 系统自动应用筛选条件并刷新页面")

if __name__ == '__main__':
    test_filter_features()
