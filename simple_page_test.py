#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单页面测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def simple_page_test():
    """简单页面测试"""
    print("🔍 简单页面测试...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        with app.test_client() as client:
            try:
                # 先登录
                login_response = client.post('/auth/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=True)
                
                print(f"登录状态: {login_response.status_code}")
                
                if login_response.status_code == 200:
                    # 访问系统设置页面
                    response = client.get('/simple/system')
                    print(f"系统设置页面状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        content = response.get_data(as_text=True)
                        
                        # 检查关键内容
                        key_elements = [
                            '功能开关',
                            '参数设置', 
                            'form-switch',
                            'ENABLE_FIRST_REVIEW',
                            'PUBLISH_TIMEOUT'
                        ]
                        
                        print("\n关键元素检查:")
                        for element in key_elements:
                            if element in content:
                                print(f"✅ {element}")
                            else:
                                print(f"❌ {element}")
                        
                        # 保存页面内容用于调试
                        with open('system_page_content.html', 'w', encoding='utf-8') as f:
                            f.write(content)
                        print(f"\n✅ 页面内容已保存到 system_page_content.html")
                        
                        # 显示页面片段
                        print(f"\n页面内容片段:")
                        print("-" * 40)
                        lines = content.split('\n')
                        for i, line in enumerate(lines[20:40]):  # 显示第20-40行
                            print(f"{i+21:3d}: {line}")
                        print("-" * 40)
                        
                    else:
                        print(f"❌ 页面访问失败: {response.status_code}")
                else:
                    print("❌ 登录失败")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 简单页面测试完成！")

if __name__ == '__main__':
    simple_page_test()
