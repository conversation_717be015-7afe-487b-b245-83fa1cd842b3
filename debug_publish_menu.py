#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试发布页面菜单显示问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.user import User

def debug_publish_menu():
    """调试发布页面菜单显示问题"""
    print("🔍 调试发布页面菜单显示问题...")
    print("=" * 60)
    
    app = create_app()
    
    with app.test_client() as client:
        try:
            # 1. 检查用户权限
            print("1. 检查用户权限:")
            print("-" * 40)
            
            with app.app_context():
                # 获取所有用户
                users = User.query.all()
                print(f"📋 系统用户列表 (共{len(users)}个):")
                
                for user in users:
                    print(f"\n用户: {user.username} (ID: {user.id})")
                    roles = [role.name for role in user.roles] if hasattr(user, 'roles') else ['未知']
                    print(f"  角色: {', '.join(roles)}")
                    print(f"  状态: {'激活' if user.is_active else '禁用'}")
                    
                    # 检查权限
                    permissions = [
                        'dashboard_access', 'template_manage', 'client_manage',
                        'content_generate', 'content_manage', 'image_manage',
                        'review.final', 'publish.manage', 'user_manage', 'system_settings'
                    ]
                    
                    print(f"  权限检查:")
                    for perm in permissions:
                        has_perm = user.has_permission(perm)
                        status = "✅" if has_perm else "❌"
                        print(f"    {status} {perm}")
            
            # 2. 测试发布管理页面
            print(f"\n2. 测试发布管理页面:")
            print("-" * 40)
            
            # 模拟登录用户访问
            response = client.get('/simple/publish/manage')
            print(f"📡 请求: GET /simple/publish/manage")
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                html_content = response.get_data(as_text=True)
                print(f"✅ 页面加载成功")
                
                # 检查菜单项
                menu_items = [
                    ('控制台', 'bi-speedometer2'),
                    ('模板管理', 'bi-file-text'),
                    ('客户管理', 'bi-people'),
                    ('内容生成', 'bi-pencil-square'),
                    ('初审文案', 'bi-clipboard-check'),
                    ('图片上传', 'bi-image'),
                    ('最终审核', 'bi-check2-square'),
                    ('客户审核', 'bi-person-check'),
                    ('发布管理', 'bi-send'),
                    ('发布状态', 'bi-list-check'),
                    ('用户管理', 'bi-person-gear'),
                    ('系统设置', 'bi-gear')
                ]
                
                print(f"\n📋 菜单项检查:")
                for menu_name, icon_class in menu_items:
                    if menu_name in html_content and icon_class in html_content:
                        print(f"  ✅ {menu_name}")
                    else:
                        print(f"  ❌ {menu_name}")
                        
            elif response.status_code == 302:
                print(f"🔄 页面重定向 (可能需要登录)")
                location = response.headers.get('Location', '未知')
                print(f"重定向到: {location}")
            else:
                print(f"❌ 页面加载失败: {response.status_code}")
            
            # 3. 测试发布状态页面
            print(f"\n3. 测试发布状态页面:")
            print("-" * 40)
            
            response = client.get('/simple/publish/status')
            print(f"📡 请求: GET /simple/publish/status")
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                html_content = response.get_data(as_text=True)
                print(f"✅ 页面加载成功")
                
                print(f"\n📋 菜单项检查:")
                for menu_name, icon_class in menu_items:
                    if menu_name in html_content and icon_class in html_content:
                        print(f"  ✅ {menu_name}")
                    else:
                        print(f"  ❌ {menu_name}")
                        
            elif response.status_code == 302:
                print(f"🔄 页面重定向 (可能需要登录)")
                location = response.headers.get('Location', '未知')
                print(f"重定向到: {location}")
            else:
                print(f"❌ 页面加载失败: {response.status_code}")
            
            # 4. 问题分析
            print(f"\n4. 问题分析:")
            print("-" * 40)
            
            print("🔍 可能的原因:")
            print("  1. 用户未登录或权限不足")
            print("  2. 模板中的权限检查逻辑有问题")
            print("  3. CSS样式导致菜单项被隐藏")
            print("  4. JavaScript动态隐藏了菜单项")
            print("  5. 模板缓存问题")
            
            print(f"\n💡 解决建议:")
            print("  1. 确保用户已登录且有相应权限")
            print("  2. 检查模板中的权限检查条件")
            print("  3. 检查CSS样式是否隐藏了菜单")
            print("  4. 清除浏览器缓存和模板缓存")
            print("  5. 检查JavaScript是否影响菜单显示")
            
            # 5. 测试建议
            print(f"\n5. 测试建议:")
            print("-" * 40)
            
            print("🔗 手动测试步骤:")
            print("  1. 确保以管理员身份登录")
            print("  2. 访问发布管理页面:")
            print("     http://127.0.0.1:5000/simple/publish/manage")
            print("  3. 访问发布状态页面:")
            print("     http://127.0.0.1:5000/simple/publish/status")
            print("  4. 打开开发者工具 (F12)")
            print("  5. 检查Elements标签:")
            print("     - 查看侧边栏HTML结构")
            print("     - 检查菜单项是否存在但被隐藏")
            print("  6. 检查Console标签:")
            print("     - 查看是否有JavaScript错误")
            print("  7. 检查Network标签:")
            print("     - 确认页面请求成功")
            
            print(f"\n🐛 调试技巧:")
            print("  1. 在浏览器中右键点击菜单区域")
            print("  2. 选择'检查元素'")
            print("  3. 查看HTML结构中是否有隐藏的菜单项")
            print("  4. 检查CSS样式中的display和visibility属性")
            print("  5. 在Console中执行:")
            print("     document.querySelectorAll('.nav-item').length")
            print("     查看实际渲染的菜单项数量")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 发布页面菜单调试完成！")
    print("\n📋 调试总结:")
    print("1. ✅ 检查了用户权限")
    print("2. ✅ 测试了页面访问")
    print("3. ✅ 分析了可能原因")
    print("4. ✅ 提供了解决建议")
    print("\n🚀 请按照建议进行手动测试和调试！")

if __name__ == '__main__':
    debug_publish_menu()
