#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试showAlert函数修复
"""

import requests

def test_showalert_fix():
    """测试showAlert函数修复"""
    print("🔧 测试showAlert函数修复...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查showAlert函数是否存在
            if 'function showAlert' in response.text:
                print("✅ 找到showAlert函数定义")
            else:
                print("❌ 未找到showAlert函数定义")
            
            # 检查showAlert函数的调用
            if 'showAlert(' in response.text:
                print("✅ 找到showAlert函数调用")
            else:
                print("❌ 未找到showAlert函数调用")
            
            # 检查提示框样式
            if 'alert-dismissible' in response.text:
                print("✅ 找到提示框样式")
            else:
                print("❌ 未找到提示框样式")
            
            # 检查优先级弹窗功能
            if 'showPriorityModal' in response.text:
                print("✅ 找到优先级弹窗函数")
            else:
                print("❌ 未找到优先级弹窗函数")
            
            # 检查优先级更新函数
            if 'updatePriority' in response.text:
                print("✅ 找到优先级更新函数")
            else:
                print("❌ 未找到优先级更新函数")
            
            # 检查批量设置函数
            if 'batchSetPriority' in response.text:
                print("✅ 找到批量设置函数")
            else:
                print("❌ 未找到批量设置函数")
            
            # 检查优先级弹窗HTML
            if 'priorityModal' in response.text:
                print("✅ 找到优先级弹窗HTML")
            else:
                print("❌ 未找到优先级弹窗HTML")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 showAlert函数修复测试完成！")
    print("\n修复内容：")
    print("1. ✅ 添加了showAlert函数定义")
    print("2. ✅ 支持不同类型的提示信息（success, danger, warning, info）")
    print("3. ✅ 提示框自动定位到右上角")
    print("4. ✅ 3秒后自动消失")
    print("5. ✅ 支持手动关闭按钮")
    print("\n现在优先级设置功能应该可以正常工作：")
    print("1. 点击齿轮图标弹出优先级选择弹窗")
    print("2. 选择优先级后显示成功提示")
    print("3. 网络错误时显示错误提示")
    print("4. 批量设置时显示相应提示")

if __name__ == '__main__':
    test_showalert_fix()
