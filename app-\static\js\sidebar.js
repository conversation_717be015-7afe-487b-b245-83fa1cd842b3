/**
 * 侧边栏功能脚本
 */

$(document).ready(function() {
    // 初始化侧边栏
    initSidebar();
    
    // 初始化移动端菜单
    initMobileMenu();
    
    // 初始化活动菜单项
    updateActiveMenuItem();
    
    // 监听页面变化，更新活动菜单项
    $(document).on('ajaxPageLoaded', function() {
        updateActiveMenuItem();
    });
});

/**
 * 初始化侧边栏
 */
function initSidebar() {
    const sidebar = $('#sidebar');
    const sidebarToggle = $('#sidebarToggle');
    
    // 从localStorage获取侧边栏状态
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        sidebar.addClass('collapsed');
    }
    
    // 侧边栏切换事件
    sidebarToggle.on('click', function() {
        sidebar.toggleClass('collapsed');
        
        // 保存状态到localStorage
        const collapsed = sidebar.hasClass('collapsed');
        localStorage.setItem('sidebarCollapsed', collapsed);
        
        // 触发窗口resize事件，让其他组件知道布局变化
        $(window).trigger('resize');
    });
}

/**
 * 初始化移动端菜单
 */
function initMobileMenu() {
    const sidebar = $('#sidebar');
    const mobileMenuToggle = $('#mobileMenuToggle');
    
    // 创建遮罩层
    if (!$('.sidebar-overlay').length) {
        $('body').append('<div class="sidebar-overlay"></div>');
    }
    
    const overlay = $('.sidebar-overlay');
    
    // 移动端菜单切换
    mobileMenuToggle.on('click', function() {
        sidebar.toggleClass('mobile-visible');
        overlay.toggleClass('visible');
    });
    
    // 点击遮罩层关闭菜单
    overlay.on('click', function() {
        sidebar.removeClass('mobile-visible');
        overlay.removeClass('visible');
    });
    
    // 点击菜单项后关闭移动端菜单
    sidebar.find('.nav-link').on('click', function() {
        if ($(window).width() <= 992) {
            sidebar.removeClass('mobile-visible');
            overlay.removeClass('visible');
        }
    });
}

/**
 * 更新活动菜单项 - 已禁用，由标签页管理器接管
 */
function updateActiveMenuItem() {
    // 注释掉自动菜单激活逻辑，由标签页管理器统一管理
    console.log('菜单激活由标签页管理器接管，跳过自动激活');
    return;

    // 以下代码已禁用
    /*
    const currentPath = window.location.pathname;
    const navLinks = $('.sidebar .nav-link');

    // 移除所有活动状态
    navLinks.removeClass('active');

    // 查找匹配的菜单项
    let activeLink = null;
    let maxMatchLength = 0;

    navLinks.each(function() {
        const href = $(this).attr('href');
        if (href && href !== '/' && currentPath.startsWith(href)) {
            if (href.length > maxMatchLength) {
                maxMatchLength = href.length;
                activeLink = $(this);
            }
        }
    });

    // 如果没有找到匹配项，检查是否是首页
    if (!activeLink && (currentPath === '/' || currentPath === '/dashboard')) {
        activeLink = navLinks.filter('[href="/"], [href="/dashboard"]').first();
    }

    // 设置活动状态
    if (activeLink) {
        activeLink.addClass('active');
    }
    */
}

/**
 * 设置面包屑导航
 */
function setBreadcrumb(items) {
    const breadcrumbContainer = $('.breadcrumb-container .breadcrumb');
    
    // 清空现有面包屑（保留首页）
    breadcrumbContainer.find('li:not(:first)').remove();
    
    // 添加新的面包屑项
    items.forEach(function(item, index) {
        const isLast = index === items.length - 1;
        const li = $('<li class="breadcrumb-item"></li>');
        
        if (isLast) {
            li.addClass('active').text(item.name);
        } else {
            const link = $('<a></a>').attr('href', item.url).text(item.name);
            if (item.url && !item.url.startsWith('http')) {
                link.attr('data-ajax-link', '');
            }
            li.append(link);
        }
        
        breadcrumbContainer.append(li);
    });
}

/**
 * 更新通知徽章
 */
function updateNotificationBadge(count) {
    const badges = $('#sidebarNotificationBadge, #headerNotificationBadge, #notificationBadge');
    
    if (count > 0) {
        badges.text(count).show();
    } else {
        badges.hide();
    }
}

/**
 * 显示加载状态
 */
function showLoading() {
    const contentWrapper = $('.content-wrapper');
    if (!contentWrapper.find('.loading-overlay').length) {
        const loadingHtml = `
            <div class="loading-overlay">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <div class="mt-2">加载中...</div>
                </div>
            </div>
        `;
        contentWrapper.append(loadingHtml);
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    $('.loading-overlay').remove();
}

/**
 * 响应式处理
 */
$(window).on('resize', function() {
    const sidebar = $('#sidebar');
    const overlay = $('.sidebar-overlay');
    
    if ($(window).width() > 992) {
        // 桌面端：隐藏移动端菜单
        sidebar.removeClass('mobile-visible');
        overlay.removeClass('visible');
    }
});

// 全局函数，供其他脚本调用
window.sidebarUtils = {
    setBreadcrumb: setBreadcrumb,
    updateNotificationBadge: updateNotificationBadge,
    showLoading: showLoading,
    hideLoading: hideLoading,
    updateActiveMenuItem: updateActiveMenuItem
};
