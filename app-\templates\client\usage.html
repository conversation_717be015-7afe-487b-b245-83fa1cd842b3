{% extends "base_simple.html" %}

{% block title %}客户使用记录 - {{ client.name }}{% endblock %}

{% block content_auth %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">客户使用记录 - {{ client.name }}</h2>
        <a href="{{ url_for('client.client_list') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回客户列表
        </a>
    </div>

    <div class="row">
        <!-- 统计卡片 -->
        <div class="col-md-3 mb-4">
            <div class="card border-left-primary shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">文案总数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">215</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-left-success shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">已发布文案</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">142</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-left-info shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">审核通过率</div>
                            <div class="row no-gutters align-items-center">
                                <div class="col-auto">
                                    <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800">78%</div>
                                </div>
                                <div class="col">
                                    <div class="progress progress-sm mr-2">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: 78%" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-left-warning shadow h-100">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">待处理请求</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">18</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-comments fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用记录表格 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">使用记录</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="btnExport">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>日期</th>
                            <th>文案生成数</th>
                            <th>审核通过数</th>
                            <th>审核拒绝数</th>
                            <th>发布数量</th>
                            <th>访问次数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 示例数据，实际应该从后端获取 -->
                        <tr>
                            <td>2023-06-01</td>
                            <td>12</td>
                            <td>10</td>
                            <td>2</td>
                            <td>8</td>
                            <td>24</td>
                        </tr>
                        <tr>
                            <td>2023-06-02</td>
                            <td>15</td>
                            <td>12</td>
                            <td>3</td>
                            <td>10</td>
                            <td>32</td>
                        </tr>
                        <tr>
                            <td>2023-06-03</td>
                            <td>8</td>
                            <td>7</td>
                            <td>1</td>
                            <td>6</td>
                            <td>18</td>
                        </tr>
                        <tr>
                            <td>2023-06-04</td>
                            <td>10</td>
                            <td>8</td>
                            <td>2</td>
                            <td>7</td>
                            <td>22</td>
                        </tr>
                        <tr>
                            <td>2023-06-05</td>
                            <td>14</td>
                            <td>11</td>
                            <td>3</td>
                            <td>9</td>
                            <td>28</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 图表展示 -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">文案状态分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">近7天文案生成趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 文案状态分布饼图
        var statusCtx = document.getElementById('statusChart').getContext('2d');
        var statusChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['已发布', '待审核', '已拒绝', '草稿'],
                datasets: [{
                    data: [142, 35, 28, 10],
                    backgroundColor: [
                        '#4e73df',
                        '#f6c23e',
                        '#e74a3b',
                        '#858796'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
        
        // 近7天文案生成趋势图
        var trendCtx = document.getElementById('trendChart').getContext('2d');
        var trendChart = new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['6月1日', '6月2日', '6月3日', '6月4日', '6月5日', '6月6日', '6月7日'],
                datasets: [{
                    label: '文案生成数',
                    data: [12, 15, 8, 10, 14, 16, 9],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    pointBackgroundColor: '#4e73df',
                    lineTension: 0.3,
                    fill: true
                }, {
                    label: '审核通过数',
                    data: [10, 12, 7, 8, 11, 13, 7],
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0.05)',
                    pointBackgroundColor: '#1cc88a',
                    lineTension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true
                        }
                    }]
                }
            }
        });
        
        // 导出按钮点击事件
        document.getElementById('btnExport').addEventListener('click', function() {
            alert('导出功能将在实际开发中实现');
        });
    });
</script>
{% endblock %} 