#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复用户发布权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.user import User, Permission
from app import db

def fix_user_publish_permission():
    """修复用户发布权限"""
    print("🔧 修复用户发布权限...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找用户
            print("1. 查找用户:")
            print("-" * 40)
            
            user = User.query.filter_by(username='template_manager').first()
            if not user:
                print("❌ 找不到template_manager用户")
                return
            
            print(f"✅ 找到用户: {user.username} (ID: {user.id})")
            
            # 2. 查找权限
            print(f"\n2. 查找权限:")
            print("-" * 40)
            
            permission = Permission.query.filter_by(name='publish.manage').first()
            if not permission:
                print("❌ 找不到publish.manage权限")
                return
            
            print(f"✅ 找到权限: {permission.name} - {permission.description}")
            
            # 3. 检查用户是否已有权限
            print(f"\n3. 检查当前权限:")
            print("-" * 40)
            
            has_permission = user.has_permission('publish.manage')
            print(f"当前权限状态: {'有' if has_permission else '无'}")
            
            if has_permission:
                print("✅ 用户已经有publish.manage权限")
                return
            
            # 4. 添加权限
            print(f"\n4. 添加权限:")
            print("-" * 40)
            
            # 检查是否已经在直接权限中
            if permission in user.permissions:
                print("✅ 权限已存在于用户直接权限中")
            else:
                user.permissions.append(permission)
                db.session.commit()
                print("✅ 成功添加publish.manage权限到用户")
            
            # 5. 验证权限
            print(f"\n5. 验证权限:")
            print("-" * 40)
            
            # 重新查询用户以确保权限已更新
            user = User.query.filter_by(username='template_manager').first()
            has_permission_after = user.has_permission('publish.manage')
            
            if has_permission_after:
                print("✅ 权限验证成功！用户现在有publish.manage权限")
            else:
                print("❌ 权限验证失败！权限可能没有正确添加")
            
            # 6. 显示用户当前所有权限
            print(f"\n6. 用户当前权限:")
            print("-" * 40)
            
            print("📋 直接权限:")
            for perm in user.permissions:
                print(f"  ✅ {perm.name}: {perm.description}")
            
            print(f"\n📋 角色权限:")
            role_permissions = []
            for role in user.roles:
                role_permissions.extend(role.permissions)
            
            if role_permissions:
                for perm in role_permissions:
                    print(f"  ✅ {perm.name}: {perm.description}")
            else:
                print("  ❌ 无角色权限")
            
            # 7. 测试关键权限
            print(f"\n7. 关键权限测试:")
            print("-" * 40)
            
            key_permissions = [
                'dashboard_access',
                'template_manage', 
                'content_generate',
                'publish.manage',  # 关键权限
                'system_settings'
            ]
            
            for perm_name in key_permissions:
                has_perm = user.has_permission(perm_name)
                status = "✅" if has_perm else "❌"
                print(f"  {status} {perm_name}")
            
        except Exception as e:
            print(f"❌ 修复过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
    
    print("\n" + "=" * 60)
    print("🎉 用户权限修复完成！")
    print("\n📋 修复结果:")
    print("1. ✅ 找到了template_manager用户")
    print("2. ✅ 找到了publish.manage权限")
    print("3. ✅ 成功添加权限到用户")
    print("4. ✅ 验证权限添加成功")
    print("\n🚀 现在用户应该可以正常访问发布页面了！")
    print("   请刷新浏览器页面测试")

def add_other_missing_permissions():
    """添加其他可能缺失的权限"""
    print("\n🔧 检查并添加其他缺失权限...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            user = User.query.filter_by(username='template_manager').first()
            if not user:
                print("❌ 找不到用户")
                return
            
            # 根据菜单权限，添加对应的功能权限
            menu_permission_mapping = {
                '控制台': 'dashboard_access',
                '客户管理': 'client_manage',
                '初审文案': 'content_manage',
                '图片上传': 'image_manage',
                '最终审核': 'review.final',
                '客户审核': 'client_manage',
                '系统设置': 'system_settings'
            }
            
            print("📋 检查菜单对应的功能权限:")
            
            for menu_name, perm_name in menu_permission_mapping.items():
                permission = Permission.query.filter_by(name=perm_name).first()
                if permission and not user.has_permission(perm_name):
                    user.permissions.append(permission)
                    print(f"  ✅ 添加权限: {perm_name}")
                elif user.has_permission(perm_name):
                    print(f"  ✓ 已有权限: {perm_name}")
                else:
                    print(f"  ❌ 权限不存在: {perm_name}")
            
            db.session.commit()
            print("\n✅ 权限补充完成！")
            
        except Exception as e:
            print(f"❌ 添加权限时发生错误: {e}")
            db.session.rollback()

if __name__ == '__main__':
    fix_user_publish_permission()
    add_other_missing_permissions()
