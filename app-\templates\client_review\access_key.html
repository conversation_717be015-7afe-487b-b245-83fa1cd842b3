<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>访问验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 1rem;
        }
        
        .access-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .access-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }
        
        .access-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .access-subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .access-form {
            margin-bottom: 1.5rem;
        }
        
        .access-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            font-size: 1.1rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            transition: border-color 0.2s;
        }
        
        .access-input:focus {
            outline: none;
            border-color: #ff2442;
            box-shadow: 0 0 0 0.2rem rgba(255, 36, 66, 0.25);
        }
        
        .access-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .access-btn:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .access-btn:active {
            transform: translateY(0);
        }
        
        .access-btn:disabled {
            opacity: 0.6;
            transform: none;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 1rem;
            display: none;
        }
        
        .help-text {
            color: #999;
            font-size: 0.8rem;
            margin-top: 1rem;
        }
        
        /* 移动端优化 */
        @media (max-width: 480px) {
            .access-card {
                padding: 1.5rem;
                margin: 1rem;
            }
            
            .access-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
            
            .access-title {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <div class="access-card">
        <div class="access-icon">
            <i class="bi bi-shield-lock"></i>
        </div>
        
        <h1 class="access-title">访问验证</h1>
        <p class="access-subtitle">请输入4位访问密钥以继续</p>
        
        <form class="access-form" onsubmit="submitAccessKey(event)">
            <input type="text" 
                   class="access-input" 
                   id="accessKey" 
                   placeholder="输入密钥"
                   maxlength="4"
                   autocomplete="off"
                   autofocus>
            <button type="submit" class="access-btn" id="submitBtn">
                <i class="bi bi-arrow-right me-2"></i>访问内容
            </button>
        </form>
        
        <div class="error-message" id="errorMessage">
            访问密钥错误，请重新输入
        </div>
        
        <div class="help-text">
            访问密钥由分享者提供，通常为4位字符
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const shareKey = '{{ share_key }}';
        
        // 自动格式化输入
        document.getElementById('accessKey').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            // 只允许字母和数字
            value = value.replace(/[^A-Z0-9]/g, '');
            e.target.value = value;
            
            // 隐藏错误信息
            document.getElementById('errorMessage').style.display = 'none';
        });
        
        // 提交访问密钥
        function submitAccessKey(event) {
            event.preventDefault();
            
            const accessKey = document.getElementById('accessKey').value.trim();
            const submitBtn = document.getElementById('submitBtn');
            const errorMessage = document.getElementById('errorMessage');
            
            if (!accessKey) {
                showError('请输入访问密钥');
                return;
            }
            
            if (accessKey.length !== 4) {
                showError('访问密钥必须是4位字符');
                return;
            }
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>验证中...';
            
            // 跳转到带访问密钥的URL
            const targetUrl = `/client-review/${shareKey}?key=${accessKey}`;
            window.location.href = targetUrl;
        }
        
        // 显示错误信息
        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            
            // 输入框震动效果
            const input = document.getElementById('accessKey');
            input.style.animation = 'shake 0.5s';
            setTimeout(() => {
                input.style.animation = '';
            }, 500);
        }
        
        // 检查URL参数中是否有错误信息
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        if (error === 'invalid_key') {
            showError('访问密钥错误，请重新输入');
        }
    </script>
    
    <style>
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    </style>
</body>
</html>
