#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接插入测试数据
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

def insert_test_data():
    """插入测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 使用原生SQL插入数据，避免模型依赖
            sql = """
            INSERT INTO contents (
                title, content, topics, location, 
                workflow_status, publish_status, publish_priority, 
                created_at, client_id, task_id, batch_id, template_id, created_by
            ) VALUES 
            ('小红书种草神器推荐', 
             '最近发现了一个超好用的产品！真的是太惊喜了～\n\n用了一段时间，效果真的很棒，强烈推荐给大家！\n\n#种草分享 #好物推荐', 
             '["种草分享", "好物推荐", "生活分享"]', 
             '上海·静安区', 
             'pending_publish', 'unpublished', 'high', 
             NOW(), 1, 1, 1, 1, 1),
            ('今日穿搭分享', 
             '今天的穿搭分享来啦～\n\n这套搭配简约又时尚，很适合日常出街！\n\n大家觉得怎么样呢？\n\n#穿搭分享 #时尚搭配', 
             '["穿搭分享", "时尚搭配", "日常穿搭"]', 
             '北京·朝阳区', 
             'pending_publish', 'unpublished', 'normal', 
             NOW(), 1, 1, 1, 1, 1),
            ('护肤心得分享', 
             '分享一下最近的护肤心得～\n\n坚持使用了一个月，皮肤状态真的有明显改善！\n\n姐妹们可以试试看！\n\n#护肤分享 #美容心得', 
             '["护肤分享", "美容心得", "生活分享"]', 
             '深圳·南山区', 
             'pending_publish', 'unpublished', 'high', 
             NOW(), 1, 1, 1, 1, 1)
            """
            
            # 执行插入
            db.session.execute(sql)
            db.session.commit()
            
            print("✅ 测试数据插入成功！")
            
            # 验证插入的数据
            result = db.session.execute("""
                SELECT id, title, publish_priority, workflow_status 
                FROM contents 
                WHERE workflow_status = 'pending_publish' 
                ORDER BY 
                    CASE publish_priority 
                        WHEN 'high' THEN 1 
                        WHEN 'normal' THEN 2 
                        WHEN 'low' THEN 3 
                        ELSE 4 
                    END
            """)
            
            print("\n插入的测试数据:")
            for row in result:
                print(f"ID: {row[0]}, 标题: {row[1]}, 优先级: {row[2]}, 状态: {row[3]}")
            
        except Exception as e:
            print(f"插入数据失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    insert_test_data()
