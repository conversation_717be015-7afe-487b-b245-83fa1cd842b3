#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查客户分享链接相关设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def check_client_share_settings():
    """检查客户分享链接相关设置"""
    print("🔍 检查客户分享链接相关设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查看分享相关的系统设置
            print("1. 分享相关的系统设置:")
            print("-" * 40)
            
            share_related_keys = [
                'CLIENT_SHARE_LINK_EXPIRES_DAYS',
                'client_share_enabled',
                'client_share_link_expires_days'
            ]
            
            found_settings = []
            for key in share_related_keys:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    found_settings.append(setting)
                    print(f"  🔍 {setting.key}: {setting.value}")
                    print(f"      描述: {setting.description}")
                    print(f"      ID: {setting.id}")
                    print()
            
            # 2. 检查分享链接创建时的设置选项
            print("2. 分享链接创建时的设置选项:")
            print("-" * 40)
            print("在创建分享链接时，用户可以选择:")
            print("  - 永久有效 (0天)")
            print("  - 1天")
            print("  - 3天")
            print("  - 7天")
            print("  - 15天")
            print("  - 30天")
            print("  - 90天")
            print()
            print("这些选项已经足够灵活，不需要全局默认值")
            
            # 3. 分析是否需要删除
            print("3. 分析CLIENT_SHARE_LINK_EXPIRES_DAYS设置:")
            print("-" * 40)
            
            expires_setting = SystemSetting.query.filter_by(key='CLIENT_SHARE_LINK_EXPIRES_DAYS').first()
            if expires_setting:
                print(f"当前设置值: {expires_setting.value} 天")
                print(f"设置描述: {expires_setting.description}")
                print()
                print("删除建议:")
                print("  ✅ 创建分享链接时可以直接选择有效期")
                print("  ✅ 用户可以根据具体需求灵活设置")
                print("  ✅ 不需要全局默认值，避免设置冗余")
                print("  ✅ 简化系统设置页面")
                print()
                print("❌ 建议删除此设置")
            else:
                print("✅ 未找到CLIENT_SHARE_LINK_EXPIRES_DAYS设置")
            
            # 4. 检查client_share_enabled设置
            print("4. 检查client_share_enabled设置:")
            print("-" * 40)
            
            share_enabled_setting = SystemSetting.query.filter_by(key='client_share_enabled').first()
            if share_enabled_setting:
                print(f"当前设置值: {share_enabled_setting.value}")
                print(f"设置描述: {share_enabled_setting.description}")
                print()
                print("保留建议:")
                print("  ✅ 这是系统级功能开关")
                print("  ✅ 控制整个客户分享功能的启用/禁用")
                print("  ✅ 有实际的功能意义")
                print()
                print("✅ 建议保留此设置")
            else:
                print("❌ 未找到client_share_enabled设置")
            
            # 5. 查看代码中的使用情况
            print("5. 代码中的使用情况分析:")
            print("-" * 40)
            print("CLIENT_SHARE_LINK_EXPIRES_DAYS的使用:")
            print("  - 在创建分享链接时作为默认值")
            print("  - 但用户可以在界面上直接选择有效期")
            print("  - 实际上用户很少使用默认值")
            print()
            print("client_share_enabled的使用:")
            print("  - 控制分享功能的整体开关")
            print("  - 影响分享相关的界面显示")
            print("  - 有实际的功能控制作用")
            
            # 6. 建议的操作
            print(f"\n6. 建议的操作:")
            print("-" * 40)
            
            if expires_setting:
                print("建议删除:")
                print(f"  🗑️ CLIENT_SHARE_LINK_EXPIRES_DAYS (ID: {expires_setting.id})")
                print("     原因: 创建分享链接时可以直接设置，不需要全局默认值")
            
            if share_enabled_setting:
                print("\n建议保留:")
                print(f"  ✅ client_share_enabled (ID: {share_enabled_setting.id})")
                print("     原因: 系统级功能开关，有实际控制作用")
            
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 客户分享链接设置检查完成！")
    print("\n总结:")
    print("1. 🔍 已识别分享相关的系统设置")
    print("2. 📋 分析了每个设置的必要性")
    print("3. ✅ 区分了需要保留和删除的设置")
    print("4. 🎯 CLIENT_SHARE_LINK_EXPIRES_DAYS可以删除")

if __name__ == '__main__':
    check_client_share_settings()
