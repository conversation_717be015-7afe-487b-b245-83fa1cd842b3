"""
查询优化工具模块
用于优化数据库查询性能
"""
from sqlalchemy import func, desc, asc
from sqlalchemy.orm import joinedload, contains_eager, subqueryload
from flask import current_app

from app import db


def optimize_query(query, model, include_relations=None, page=None, per_page=None, order_by=None, order_direction='asc'):
    """
    优化查询，添加关联加载、分页和排序
    
    参数:
        query: 原始查询对象
        model: 模型类
        include_relations: 需要预加载的关联关系列表
        page: 页码
        per_page: 每页数量
        order_by: 排序字段
        order_direction: 排序方向，'asc'或'desc'
    
    返回:
        优化后的查询对象
    """
    # 添加关联预加载
    if include_relations:
        for relation in include_relations:
            query = query.options(joinedload(getattr(model, relation)))
    
    # 添加排序
    if order_by:
        if hasattr(model, order_by):
            column = getattr(model, order_by)
            if order_direction.lower() == 'desc':
                query = query.order_by(desc(column))
            else:
                query = query.order_by(asc(column))
    
    # 添加分页
    if page is not None and per_page is not None:
        query = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return query


def bulk_insert(model, data_list, chunk_size=100):
    """
    批量插入数据，提高插入性能
    
    参数:
        model: 模型类
        data_list: 数据列表，每个元素是一个字典
        chunk_size: 每次插入的数量
    
    返回:
        插入的记录数量
    """
    if not data_list:
        return 0
    
    # 分块插入
    total_count = 0
    for i in range(0, len(data_list), chunk_size):
        chunk = data_list[i:i + chunk_size]
        db.session.bulk_insert_mappings(model, chunk)
        db.session.commit()
        total_count += len(chunk)
    
    return total_count


def bulk_update(model, data_list, primary_key='id', chunk_size=100):
    """
    批量更新数据，提高更新性能
    
    参数:
        model: 模型类
        data_list: 数据列表，每个元素是一个字典，必须包含主键
        primary_key: 主键字段名，默认为'id'
        chunk_size: 每次更新的数量
    
    返回:
        更新的记录数量
    """
    if not data_list:
        return 0
    
    # 分块更新
    total_count = 0
    for i in range(0, len(data_list), chunk_size):
        chunk = data_list[i:i + chunk_size]
        db.session.bulk_update_mappings(model, chunk)
        db.session.commit()
        total_count += len(chunk)
    
    return total_count


def get_or_create(model, defaults=None, **kwargs):
    """
    获取或创建记录，减少查询次数
    
    参数:
        model: 模型类
        defaults: 默认值字典，用于创建记录
        **kwargs: 查询条件
    
    返回:
        (记录, 是否创建)元组
    """
    instance = model.query.filter_by(**kwargs).first()
    if instance:
        return instance, False
    else:
        params = dict(kwargs)
        if defaults:
            params.update(defaults)
        instance = model(**params)
        db.session.add(instance)
        db.session.commit()
        return instance, True


def optimize_count_query(query):
    """
    优化计数查询，提高性能
    
    参数:
        query: 原始查询对象
    
    返回:
        记录数量
    """
    # 使用子查询优化计数
    count_q = query.statement.with_only_columns([func.count()]).order_by(None)
    count = query.session.execute(count_q).scalar()
    return count


def optimize_in_query(model, ids, include_relations=None):
    """
    优化IN查询，提高性能
    
    参数:
        model: 模型类
        ids: ID列表
        include_relations: 需要预加载的关联关系列表
    
    返回:
        查询结果列表
    """
    if not ids:
        return []
    
    # 分块查询，避免IN子句过长
    chunk_size = 500
    result = []
    
    for i in range(0, len(ids), chunk_size):
        chunk_ids = ids[i:i + chunk_size]
        query = model.query.filter(model.id.in_(chunk_ids))
        
        # 添加关联预加载
        if include_relations:
            for relation in include_relations:
                query = query.options(joinedload(getattr(model, relation)))
        
        chunk_result = query.all()
        result.extend(chunk_result)
    
    return result 