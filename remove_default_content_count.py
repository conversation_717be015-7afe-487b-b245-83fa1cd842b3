#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
移除默认每日文案数量系统设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting
from app import db

def remove_default_content_count():
    """移除默认每日文案数量系统设置"""
    print("🗑️ 移除默认每日文案数量系统设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 查找default_content_count设置
            setting = SystemSetting.query.filter_by(key='default_content_count').first()
            
            if setting:
                print(f"找到设置:")
                print(f"  键名: {setting.key}")
                print(f"  值: {setting.value}")
                print(f"  描述: {setting.description}")
                
                # 删除设置
                db.session.delete(setting)
                db.session.commit()
                
                print(f"✅ 已删除 default_content_count 系统设置")
                
            else:
                print("❌ 未找到 default_content_count 设置")
            
            # 验证删除结果
            remaining_setting = SystemSetting.query.filter_by(key='default_content_count').first()
            if remaining_setting:
                print("❌ 设置删除失败，仍然存在")
            else:
                print("✅ 设置删除验证成功")
            
            # 显示剩余的系统设置
            print(f"\n剩余的系统设置:")
            print("-" * 40)
            
            all_settings = SystemSetting.query.order_by(SystemSetting.key).all()
            print(f"总共 {len(all_settings)} 个系统设置:")
            
            for setting in all_settings:
                print(f"  - {setting.key}: {setting.value}")
            
        except Exception as e:
            print(f"❌ 删除失败: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 默认每日文案数量设置移除完成！")
    print("\n移除原因:")
    print("1. ✅ 客户添加时可以直接设置每日文案数量")
    print("2. ✅ 不需要系统级别的默认值")
    print("3. ✅ 简化系统设置，减少冗余配置")
    print("4. ✅ 客户模型中已有 daily_content_count 字段（默认值5）")
    print("\n影响说明:")
    print("- 🔧 系统设置页面不再显示此项")
    print("- 📝 添加客户时直接设置每日文案数量")
    print("- 💾 现有客户的设置不受影响")
    print("- 🎯 新客户使用模型默认值（5篇）")

if __name__ == '__main__':
    remove_default_content_count()
