"""
客户管理表单
"""
from flask_wtf import FlaskForm
from wtforms import StringField, BooleanField, IntegerField, TextAreaField, TimeField, SelectField
from wtforms.validators import DataRequired, Email, Optional, NumberRange, Length
from datetime import time


class ClientForm(FlaskForm):
    """客户表单"""
    name = StringField('客户名称', validators=[DataRequired(), Length(max=100)])
    contact = StringField('联系人', validators=[Optional(), Length(max=50)])
    phone = StringField('联系电话', validators=[Length(max=20)])
    email = StringField('电子邮箱', validators=[Optional(), Email(), Length(max=100)])
    
    need_review = BooleanField('需要客户审核', default=True)
    daily_content_count = IntegerField('每日展示数量', validators=[NumberRange(min=1, max=100)], default=5)
    display_start_time = TimeField('展示开始时间', format='%H:%M', validators=[Optional()], default=time(8, 30))
    interval_min = IntegerField('最小间隔时间(分钟)', validators=[NumberRange(min=1, max=1440)], default=10)
    interval_max = IntegerField('最大间隔时间(分钟)', validators=[NumberRange(min=1, max=1440)], default=30)
    
    # 扩展字段
    address = StringField('地址', validators=[Optional(), Length(max=200)])
    remark = TextAreaField('备注', validators=[Optional(), Length(max=500)])
    
    status = BooleanField('启用状态', default=True)


class ClientShareForm(FlaskForm):
    """客户分享链接表单"""
    has_password = BooleanField('设置访问密码', default=True)
    expires_days = SelectField('有效期(天)', choices=[
        (0, '永久有效'),
        (1, '1天'),
        (3, '3天'),
        (7, '7天'),
        (15, '15天'),
        (30, '30天'),
        (90, '90天')
    ], coerce=int, default=0)
    
    # 权限设置
    view_permission = BooleanField('查看权限', default=True)
    edit_permission = BooleanField('编辑权限', default=True)
    review_permission = BooleanField('审核权限', default=True) 