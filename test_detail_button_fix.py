#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试详情按钮修复
"""

import requests

def test_detail_button_fix():
    """测试详情按钮修复"""
    print("🔧 测试详情按钮修复...")
    print("=" * 50)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查页面是否包含修复后的按钮结构
            if 'viewDetailsFromButton' in response.text:
                print("✅ 找到修复后的JavaScript函数")
            else:
                print("❌ 未找到修复后的JavaScript函数")
            
            if 'data-content-id' in response.text:
                print("✅ 找到修复后的按钮data属性")
            else:
                print("❌ 未找到修复后的按钮data属性")
            
            # 检查是否有详情按钮
            detail_button_count = response.text.count('详情</button>')
            print(f"✅ 找到 {detail_button_count} 个详情按钮")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 详情按钮修复测试完成！")
    print("\n修复内容：")
    print("1. ✅ 使用data属性传递数据，避免JavaScript语法错误")
    print("2. ✅ 文案内容通过隐藏div传递，避免特殊字符问题")
    print("3. ✅ 使用viewDetailsFromButton函数处理点击事件")
    print("\n现在可以正常点击详情按钮查看文案详情了！")

if __name__ == '__main__':
    test_detail_button_fix()
