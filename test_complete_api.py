#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整的API测试脚本
"""

import requests
import json
import time

def test_complete_api():
    """完整测试API功能"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🚀 开始完整API测试...")
    print("=" * 60)
    
    # 1. 测试连接
    print("\n1. 测试API连接...")
    try:
        response = requests.get(f'{base_url}/settings', headers=headers)
        if response.status_code == 200:
            data = response.json()
            print("✅ API连接成功！")
            print(f"发布超时设置: {data.get('publish_timeout', 'N/A')}秒")
        else:
            print(f"❌ API连接失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return
    
    # 2. 获取待发布文案
    print("\n2. 获取待发布文案...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                print("✅ 获取成功！")
                print(f"文案ID: {content.get('id')}")
                print(f"标题: {content.get('title')}")
                print(f"优先级: {content.get('priority_display', content.get('priority'))}")
                print(f"工作流状态: {content.get('workflow_status')}")
                print(f"发布状态: {content.get('publish_status')}")
                print(f"获取时间: {content.get('publish_time')}")
                
                content_id = content.get('id')
                
                # 3. 等待一段时间（模拟发布过程）
                print(f"\n3. 等待30秒（模拟发布过程）...")
                time.sleep(30)
                
                # 4. 检查超时
                print("\n4. 检查发布超时...")
                timeout_response = requests.post(f'{base_url}/check-timeouts', headers=headers)
                if timeout_response.status_code == 200:
                    timeout_data = timeout_response.json()
                    if timeout_data.get('success'):
                        print("✅ 超时检查成功！")
                        print(f"处理的超时文案数量: {timeout_data.get('timeout_count')}")
                        print(f"超时时间设置: {timeout_data.get('timeout_seconds')}秒")
                        print(f"处理策略: {timeout_data.get('timeout_action')}")
                    else:
                        print(f"❌ 超时检查失败: {timeout_data.get('error')}")
                else:
                    print(f"❌ 超时检查请求失败: {timeout_response.status_code}")
                
                # 5. 更新发布状态（成功）
                print(f"\n5. 更新发布状态为成功...")
                update_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/test/123',
                    'platform': '小红书',
                    'account': '测试账号'
                }
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=update_data
                )
                
                if update_response.status_code == 200:
                    update_result = update_response.json()
                    if update_result.get('success'):
                        print("✅ 状态更新成功！")
                    else:
                        print(f"❌ 状态更新失败: {update_result.get('error')}")
                else:
                    print(f"❌ 状态更新请求失败: {update_response.status_code}")
                
                # 6. 再次获取文案（应该获取下一篇）
                print(f"\n6. 再次获取文案（应该获取下一篇）...")
                response2 = requests.get(f'{base_url}/content', headers=headers)
                if response2.status_code == 200:
                    data2 = response2.json()
                    if data2.get('success'):
                        content2 = data2.get('content')
                        print("✅ 获取下一篇文案成功！")
                        print(f"文案ID: {content2.get('id')}")
                        print(f"标题: {content2.get('title')}")
                        print(f"优先级: {content2.get('priority_display', content2.get('priority'))}")
                    else:
                        print(f"ℹ️ 没有更多待发布文案: {data2.get('message')}")
                else:
                    print(f"❌ 获取下一篇文案失败: {response2.status_code}")
                
            else:
                print(f"❌ 获取失败: {data.get('error')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 API测试完成！")
    print("\n测试总结:")
    print("1. ✅ API连接测试")
    print("2. ✅ 获取待发布文案（自动按优先级排序）")
    print("3. ✅ 状态自动更新为发布中")
    print("4. ✅ 发布超时检查")
    print("5. ✅ 发布状态更新")
    print("6. ✅ 获取下一篇文案")

if __name__ == '__main__':
    test_complete_api()
