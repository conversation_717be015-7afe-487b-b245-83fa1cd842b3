"""
图片管理视图
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, current_app
from flask_login import login_required, current_user
from sqlalchemy import and_
import os
import json

from app.models import db
from app.models.content import Content, ContentHistory
from app.utils.decorators import permission_required
from app.utils.uploads import upload_manager

# 创建蓝图
image_management_bp = Blueprint('image_management', __name__, url_prefix='/image-management')

@image_management_bp.route('/')
@login_required
@permission_required('image.view')
def index():
    """图片管理首页"""
    # 获取所有待上传图片的文案
    from sqlalchemy import or_, and_
    contents = Content.query.filter(
        or_(
            # 正常的图片管理流程
            Content.workflow_status == 'first_reviewed',
            # 最终审核驳回（图片问题）
            and_(
                Content.workflow_status == 'first_reviewed',
                Content.internal_review_status == 'final_rej_img'
            ),
            # 最终审核驳回（两者都有问题）- 无论文案是否完成都显示
            and_(
                Content.workflow_status == 'draft',
                Content.internal_review_status == 'final_rej_both'
            ),
            # 客户驳回（图片问题）
            and_(
                Content.workflow_status == 'first_reviewed',
                Content.internal_review_status == 'client_rej_img'
            ),
            # 客户驳回（两者都有问题）- 无论文案是否完成都显示
            and_(
                Content.workflow_status == 'draft',
                Content.internal_review_status == 'client_rej_both'
            )
        )
    ).all()
    return render_template('image_management/index.html', contents=contents, title='图片管理')

@image_management_bp.route('/<int:content_id>')
@login_required
@permission_required('image.view')
def view(content_id):
    """查看待上传图片的文案详情"""
    content = Content.query.get_or_404(content_id)
    # 检查是否为图片管理状态
    valid_states = [
        # 正常的图片管理流程
        (content.workflow_status == 'first_reviewed'),
        # 最终审核驳回（图片问题）
        (content.workflow_status == 'first_reviewed' and content.internal_review_status == 'final_rej_img'),
        # 最终审核驳回（两者都有问题）
        (content.workflow_status == 'draft' and content.internal_review_status == 'final_rej_both'),
        # 客户驳回（图片问题）
        (content.workflow_status == 'first_reviewed' and content.internal_review_status == 'client_rej_img'),
        # 客户驳回（两者都有问题）
        (content.workflow_status == 'draft' and content.internal_review_status == 'client_rej_both')
    ]

    if not any(valid_states):
        flash('该文案不在图片管理状态', 'warning')
        return redirect(url_for('image_management.index'))
    
    # 解析已有图片
    image_urls = []
    if content.image_urls:
        try:
            image_urls = json.loads(content.image_urls)
        except:
            image_urls = []
    
    return render_template('image_management/view.html', 
                          content=content, 
                          image_urls=image_urls, 
                          title='图片管理')

@image_management_bp.route('/<int:content_id>/upload', methods=['POST'])
@login_required
@permission_required('image.upload')
def upload(content_id):
    """上传图片"""
    content = Content.query.get_or_404(content_id)
    # 检查是否为图片管理状态
    valid_states = [
        # 正常的图片管理流程
        (content.workflow_status == 'first_reviewed'),
        # 最终审核驳回（图片问题）
        (content.workflow_status == 'first_reviewed' and content.internal_review_status == 'final_rej_img'),
        # 最终审核驳回（两者都有问题）
        (content.workflow_status == 'draft' and content.internal_review_status == 'final_rej_both'),
        # 客户驳回（图片问题）
        (content.workflow_status == 'first_reviewed' and content.internal_review_status == 'client_rej_img'),
        # 客户驳回（两者都有问题）
        (content.workflow_status == 'draft' and content.internal_review_status == 'client_rej_both')
    ]

    if not any(valid_states):
        flash('该文案不在图片管理状态', 'warning')
        return redirect(url_for('image_management.index'))
    
    if 'images' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'})
    
    files = request.files.getlist('images')
    if not files or files[0].filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    # 解析已有图片
    image_urls = []
    if content.image_urls:
        try:
            image_urls = json.loads(content.image_urls)
        except:
            image_urls = []
    
    # 上传新图片
    upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], f'content_{content_id}')
    os.makedirs(upload_dir, exist_ok=True)
    
    new_urls = []
    for file in files:
        if file and upload_manager.allowed_file(file.filename):
            filename = upload_manager.save_file(file, upload_dir)
            file_url = url_for('static', filename=f'uploads/content_{content_id}/{filename}', _external=True)
            new_urls.append(file_url)
    
    # 更新文案图片
    image_urls.extend(new_urls)
    content.image_urls = json.dumps(image_urls)
    db.session.commit()
    
    return jsonify({
        'success': True, 
        'message': '图片上传成功', 
        'urls': new_urls
    })

@image_management_bp.route('/<int:content_id>/delete-image', methods=['POST'])
@login_required
@permission_required('image.delete')
def delete_image(content_id):
    """删除图片"""
    content = Content.query.get_or_404(content_id)
    if content.workflow_status != 'first_reviewed':
        return jsonify({'success': False, 'message': '该文案不在图片管理状态'})
    
    image_url = request.json.get('image_url')
    if not image_url:
        return jsonify({'success': False, 'message': '未指定要删除的图片'})
    
    # 解析已有图片
    image_urls = []
    if content.image_urls:
        try:
            image_urls = json.loads(content.image_urls)
            if image_url in image_urls:
                image_urls.remove(image_url)
                content.image_urls = json.dumps(image_urls)
                db.session.commit()
                
                # 尝试删除实际文件
                try:
                    file_path = image_url.split('/static/')[-1]
                    full_path = os.path.join(current_app.static_folder, file_path)
                    if os.path.exists(full_path):
                        os.remove(full_path)
                except Exception as e:
                    current_app.logger.error(f"删除文件失败: {str(e)}")
                
                return jsonify({'success': True, 'message': '图片已删除'})
        except:
            pass
    
    return jsonify({'success': False, 'message': '图片不存在'})

@image_management_bp.route('/<int:content_id>/submit', methods=['POST'])
@login_required
@permission_required('image.submit')
def submit(content_id):
    """提交图片审核"""
    content = Content.query.get_or_404(content_id)

    # 检查是否为图片管理状态
    valid_states = [
        (content.workflow_status == 'first_reviewed'),
        (content.workflow_status == 'draft' and content.internal_review_status == 'final_rej_both'),
        (content.workflow_status == 'first_reviewed' and content.internal_review_status == 'client_rej_img'),
        (content.workflow_status == 'draft' and content.internal_review_status == 'client_rej_both')
    ]

    if not any(valid_states):
        flash('该文案不在图片管理状态', 'warning')
        return redirect(url_for('image_management.index'))

    # 检查是否已上传图片
    if not content.image_urls:
        flash('请先上传图片', 'warning')
        return redirect(url_for('image_management.view', content_id=content_id))

    # 特殊处理：如果是驳回的"两者都有问题"状态（最终审核驳回或客户驳回）
    if content.internal_review_status in ['final_rej_both', 'client_rej_both']:
        # 标记图片已完成
        content.image_completed = 1

        # 检查文案是否也已完成
        if content.content_completed == 1:
            # 文案也已完成，可以进入终审
            content.workflow_status = 'images_uploaded'
            content.internal_review_status = 'pending'
            # 如果是客户驳回，需要重置客户审核状态
            if content.client_review_status == 'rejected':
                content.client_review_status = 'pending'
            flash('文案和图片都已重新编辑，已提交最终审核', 'success')
        else:
            # 文案还未完成，保持当前状态
            # 不改变workflow_status，让用户知道还需要编辑文案
            flash('图片已重新上传，请继续编辑文案', 'info')
    # 特殊处理：如果是驳回的"图片问题"状态（最终审核驳回或客户驳回）
    elif content.internal_review_status in ['final_rej_img', 'client_rej_img']:
        # 图片问题已修复，可以进入终审
        content.workflow_status = 'images_uploaded'
        content.image_completed = 1
        content.internal_review_status = 'pending'
        # 如果是客户驳回，需要重置客户审核状态
        if content.client_review_status == 'rejected':
            content.client_review_status = 'pending'
        flash('图片已重新上传，已提交最终审核', 'success')
    else:
        # 正常流程：图片上传完成
        content.workflow_status = 'images_uploaded'
        content.image_completed = 1
        # 重置审核状态（清除之前的驳回状态）
        if content.internal_review_status in ['final_rej_text']:
            content.internal_review_status = 'pending'
        flash('图片已提交审核', 'success')

    db.session.commit()
    return redirect(url_for('image_management.index'))