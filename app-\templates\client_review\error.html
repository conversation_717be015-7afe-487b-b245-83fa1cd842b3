<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问错误 - 小红书文案审核</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-title {
            color: #333;
            margin-bottom: 1rem;
        }
        .error-message {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .error-code {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 0.5rem 1rem;
            font-family: monospace;
            color: #6c757d;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            {% if error_code == 'INVALID_LINK' %}
                <i class="bi bi-link-45deg"></i>
            {% elif error_code == 'CLIENT_NOT_FOUND' %}
                <i class="bi bi-person-x"></i>
            {% elif error_code == 'EXPIRED_LINK' %}
                <i class="bi bi-clock-history"></i>
            {% else %}
                <i class="bi bi-exclamation-triangle"></i>
            {% endif %}
        </div>
        
        <h2 class="error-title">
            {% if error_code == 'INVALID_LINK' %}
                链接无效
            {% elif error_code == 'CLIENT_NOT_FOUND' %}
                客户不存在
            {% elif error_code == 'EXPIRED_LINK' %}
                链接已过期
            {% else %}
                访问错误
            {% endif %}
        </h2>
        
        <p class="error-message">
            {{ error_message }}
        </p>
        
        {% if error_code %}
        <div class="error-code">
            错误代码: {{ error_code }}
        </div>
        {% endif %}
        
        <div class="mt-4">
            {% if error_code == 'EXPIRED_LINK' %}
                <p class="text-muted small">
                    <i class="bi bi-info-circle"></i>
                    请联系管理员重新生成审核链接
                </p>
            {% elif error_code == 'INVALID_LINK' %}
                <p class="text-muted small">
                    <i class="bi bi-info-circle"></i>
                    请检查链接是否正确，或联系管理员获取新的审核链接
                </p>
            {% else %}
                <p class="text-muted small">
                    <i class="bi bi-info-circle"></i>
                    如果问题持续存在，请联系技术支持
                </p>
            {% endif %}
        </div>
        
        <div class="mt-4">
            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                <i class="bi bi-arrow-left"></i> 返回上一页
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
