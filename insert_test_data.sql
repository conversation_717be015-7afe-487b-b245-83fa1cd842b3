-- 插入测试数据用于API测试

-- 插入测试客户（如果不存在）
INSERT IGNORE INTO clients (id, name, contact, phone, email, need_review, daily_content_count, status, created_at) 
VALUES (1, '测试客户', '测试联系人', '13800138000', '<EMAIL>', 0, 10, 1, NOW());

-- 插入测试模板分类（如果不存在）
INSERT IGNORE INTO template_categories (id, name, created_at) 
VALUES (1, '测试分类', NOW());

-- 插入测试模板（如果不存在）
INSERT IGNORE INTO templates (id, title, content, category_id, creator_id, status, created_at) 
VALUES (1, '测试模板', '这是一个测试文案模板，用于API测试。', 1, 1, 1, NOW());

-- 插入测试任务（如果不存在）
INSERT IGNORE INTO tasks (id, name, description, client_id, template_id, target_count, actual_count, status, created_by, created_at) 
VALUES (1, 'API测试任务', '用于API测试的任务', 1, 1, 5, 0, 'active', 1, NOW());

-- 插入测试批次（如果不存在）
INSERT IGNORE INTO batches (id, name, description, task_id, target_count, actual_count, status, created_by, created_at) 
VALUES (1, 'API测试批次', '用于API测试的批次', 1, 5, 0, 'active', 1, NOW());

-- 插入测试文案数据
INSERT IGNORE INTO contents (
    id, title, content, topics, location, client_id, task_id, batch_id, template_id,
    workflow_status, publish_status, publish_priority, created_by, created_at
) VALUES 
(1, '小红书种草神器推荐', 
 '最近发现了一个超好用的产品！真的是太惊喜了～\n\n用了一段时间，效果真的很棒，强烈推荐给大家！\n\n#种草分享 #好物推荐', 
 '["种草分享", "好物推荐", "生活分享"]', 
 '上海·静安区', 
 1, 1, 1, 1, 
 'pending_publish', 'unpublished', 'high', 
 1, NOW()),

(2, '今日穿搭分享', 
 '今天的穿搭分享来啦～\n\n这套搭配简约又时尚，很适合日常出街！\n\n大家觉得怎么样呢？\n\n#穿搭分享 #时尚搭配', 
 '["穿搭分享", "时尚搭配", "日常穿搭"]', 
 '北京·朝阳区', 
 1, 1, 1, 1, 
 'pending_publish', 'unpublished', 'normal', 
 1, NOW()),

(3, '美食探店记录', 
 '今天去了一家超棒的餐厅！\n\n环境很好，菜品也很精致，服务态度也很棒～\n\n推荐给喜欢美食的朋友们！\n\n#美食探店 #餐厅推荐', 
 '["美食探店", "餐厅推荐", "生活记录"]', 
 '广州·天河区', 
 1, 1, 1, 1, 
 'pending_publish', 'unpublished', 'low', 
 1, NOW()),

(4, '护肤心得分享', 
 '分享一下最近的护肤心得～\n\n坚持使用了一个月，皮肤状态真的有明显改善！\n\n姐妹们可以试试看！\n\n#护肤分享 #美容心得', 
 '["护肤分享", "美容心得", "生活分享"]', 
 '深圳·南山区', 
 1, 1, 1, 1, 
 'pending_publish', 'unpublished', 'high', 
 1, NOW()),

(5, '周末生活记录', 
 '周末的美好时光～\n\n和朋友一起度过了愉快的一天，满满的幸福感！\n\n生活就是要这样简单快乐！\n\n#周末时光 #生活记录', 
 '["周末时光", "生活记录", "日常分享"]', 
 '杭州·西湖区', 
 1, 1, 1, 1, 
 'pending_publish', 'unpublished', 'normal', 
 1, NOW());

-- 显示插入的数据
SELECT 
    id, title, publish_priority, workflow_status, publish_status, location, created_at
FROM contents 
WHERE workflow_status = 'pending_publish' 
ORDER BY 
    CASE publish_priority 
        WHEN 'high' THEN 1 
        WHEN 'normal' THEN 2 
        WHEN 'low' THEN 3 
        ELSE 4 
    END,
    created_at;
