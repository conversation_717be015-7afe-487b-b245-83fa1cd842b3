#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查文案备份设置的使用情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def check_content_backup_setting():
    """检查文案备份设置的使用情况"""
    print("🔍 检查文案备份设置的使用情况...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找备份设置
            print("1. 查找备份设置:")
            print("-" * 40)
            
            backup_setting = SystemSetting.query.filter_by(key='content_backup_enabled').first()
            
            if backup_setting:
                print(f"🔍 找到设置: {backup_setting.key}")
                print(f"  当前值: {backup_setting.value}")
                print(f"  描述: {backup_setting.description}")
                print(f"  ID: {backup_setting.id}")
            else:
                print("❌ 未找到 content_backup_enabled 设置")
                return
            
            # 2. 搜索代码中的使用情况
            print(f"\n2. 搜索代码中的使用情况:")
            print("-" * 40)
            
            usage_files = []
            search_terms = ['content_backup_enabled', 'backup_enabled', 'content_backup']
            
            # 搜索可能使用备份功能的文件
            search_dirs = ['app/views', 'app/services', 'app/utils', 'app/models']
            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    for root, dirs, files in os.walk(search_dir):
                        for file in files:
                            if file.endswith('.py'):
                                file_path = os.path.join(root, file)
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                        for term in search_terms:
                                            if term in content:
                                                usage_files.append((file_path, term))
                                                break
                                except:
                                    pass
            
            if usage_files:
                print("🔍 找到使用备份设置的文件:")
                for file_path, term in usage_files:
                    print(f"  - {file_path} (包含: {term})")
            else:
                print("❌ 没有找到使用备份设置的文件")
            
            # 3. 检查是否有备份相关的功能实现
            print(f"\n3. 检查备份功能实现:")
            print("-" * 40)
            
            # 检查是否有备份相关的服务或工具
            backup_related_files = [
                'app/services/backup.py',
                'app/services/content_backup.py',
                'app/utils/backup.py',
                'app/utils/content_backup.py'
            ]
            
            backup_files_exist = []
            for file_path in backup_related_files:
                if os.path.exists(file_path):
                    backup_files_exist.append(file_path)
            
            if backup_files_exist:
                print("✅ 找到备份相关文件:")
                for file_path in backup_files_exist:
                    print(f"  - {file_path}")
            else:
                print("❌ 没有找到备份相关的实现文件")
            
            # 4. 检查数据库中是否有备份相关的表
            print(f"\n4. 检查备份相关的数据库表:")
            print("-" * 40)
            
            from sqlalchemy import text
            try:
                # 查找可能的备份表
                result = db.session.execute(text("SHOW TABLES"))
                tables = [row[0] for row in result.fetchall()]
                
                backup_tables = [table for table in tables if 'backup' in table.lower()]
                
                if backup_tables:
                    print("✅ 找到备份相关表:")
                    for table in backup_tables:
                        print(f"  - {table}")
                else:
                    print("❌ 没有找到备份相关的数据库表")
                    
            except Exception as e:
                print(f"❌ 检查数据库表失败: {e}")
            
            # 5. 分析设置的实际作用
            print(f"\n5. 设置作用分析:")
            print("-" * 40)
            
            print("📋 content_backup_enabled 设置分析:")
            print("  🔍 设置名称: 自动备份文案内容")
            print("  🔍 当前状态: 启用")
            print("  🔍 预期功能: 自动备份文案内容到备份存储")
            print()
            
            if not usage_files and not backup_files_exist:
                print("❌ 功能状态: 未实现")
                print("  - 没有找到使用该设置的代码")
                print("  - 没有找到备份功能的实现")
                print("  - 设置开关无实际作用")
                print("  - 用户看到开关但功能不工作")
            else:
                print("✅ 功能状态: 可能已实现")
                print("  - 找到了相关的代码引用")
                print("  - 需要进一步检查具体实现")
            
            # 6. 删除建议
            print(f"\n6. 删除建议:")
            print("-" * 40)
            
            if not usage_files and not backup_files_exist:
                print("🗑️ 建议删除该设置:")
                print("  ✅ 设置开关无实际功能")
                print("  ✅ 避免用户困惑")
                print("  ✅ 简化系统设置页面")
                print("  ✅ 减少系统复杂度")
                print()
                print("🔧 删除操作:")
                print("  1. 从数据库中删除 content_backup_enabled 设置")
                print("  2. 从系统设置页面移除显示")
                print("  3. 从重置函数中移除设置")
            else:
                print("⚠️ 需要谨慎处理:")
                print("  - 发现了相关代码引用")
                print("  - 建议先检查具体实现")
                print("  - 确认功能是否真的在使用")
                print("  - 如果确认无用再删除")
            
            # 7. 如果将来需要备份功能
            print(f"\n7. 将来的备份功能实现建议:")
            print("-" * 40)
            
            print("💡 如果将来要实现文案备份功能:")
            print("  1. 创建备份服务 (app/services/content_backup.py)")
            print("  2. 实现定时备份任务")
            print("  3. 添加备份存储配置")
            print("  4. 创建备份恢复功能")
            print("  5. 重新添加系统设置控制开关")
            print()
            print("📋 备份功能的价值:")
            print("  - 防止数据丢失")
            print("  - 支持版本历史")
            print("  - 便于数据恢复")
            print("  - 提高系统可靠性")
            
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 文案备份设置检查完成！")

if __name__ == '__main__':
    check_content_backup_setting()
