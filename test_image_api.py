#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图片API功能
"""

import requests
import json

def test_image_api():
    """测试图片API功能"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🖼️ 测试图片API功能...")
    print("=" * 50)
    
    # 获取待发布文案
    print("\n1. 获取待发布文案...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                print("✅ 获取成功！")
                print(f"文案ID: {content.get('id')}")
                print(f"标题: {content.get('title')}")
                print(f"图片数量: {content.get('image_count', 0)}")
                
                images = content.get('images', [])
                if images:
                    print(f"✅ 找到 {len(images)} 张图片:")
                    for i, img_url in enumerate(images, 1):
                        print(f"  图片{i}: {img_url}")
                else:
                    print("❌ 没有找到图片")
                
                # 显示完整的文案信息
                print(f"\n📝 完整文案信息:")
                print(f"内容: {content.get('content', '')[:100]}...")
                print(f"话题: {content.get('topics', [])}")
                print(f"位置: {content.get('location', '')}")
                print(f"优先级: {content.get('priority_display', '')}")
                print(f"客户: {content.get('client_name', '')}")
                
            else:
                print(f"❌ 获取失败: {data.get('error')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 图片API测试完成！")

if __name__ == '__main__':
    test_image_api()
