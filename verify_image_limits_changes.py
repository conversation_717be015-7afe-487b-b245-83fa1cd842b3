#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证图片数量限制修改
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting

def verify_image_limits_changes():
    """验证图片数量限制修改"""
    print("📸 验证图片数量限制修改...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 验证系统设置
            print("1. 验证系统设置:")
            print("-" * 40)
            
            setting = SystemSetting.query.filter_by(key='MAX_IMAGES_PER_CONTENT').first()
            if setting:
                print(f"✅ MAX_IMAGES_PER_CONTENT 设置存在")
                print(f"   当前值: {setting.value} 张")
                print(f"   描述: {setting.description}")
                
                if setting.value == '10':
                    print("✅ 设置值正确（10张）")
                else:
                    print(f"⚠️ 设置值为 {setting.value} 张")
            else:
                print("❌ MAX_IMAGES_PER_CONTENT 设置不存在")
            
            # 2. 验证API路由
            print(f"\n2. 验证API路由:")
            print("-" * 40)
            
            with app.test_client() as client:
                # 先登录
                login_response = client.post('/auth/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=True)
                
                if login_response.status_code == 200:
                    print("✅ 登录成功")
                    
                    # 测试图片限制API
                    api_response = client.get('/simple/api/system/image-limits')
                    print(f"   API状态码: {api_response.status_code}")
                    
                    if api_response.status_code == 200:
                        try:
                            data = api_response.get_json()
                            if data and data.get('success'):
                                limits = data.get('limits', {})
                                print(f"✅ API返回成功")
                                print(f"   最大图片数量: {limits.get('max_images')} 张")
                                print(f"   最大文件大小: {limits.get('max_size_mb')} MB")
                                print(f"   允许的类型: {limits.get('allowed_types')}")
                            else:
                                print(f"❌ API返回失败: {data.get('message') if data else '无数据'}")
                        except Exception as e:
                            print(f"❌ API响应解析失败: {e}")
                    else:
                        print(f"❌ API调用失败: {api_response.status_code}")
                else:
                    print("❌ 登录失败")
            
            # 3. 验证前端文件修改
            print(f"\n3. 验证前端文件修改:")
            print("-" * 40)
            
            try:
                with open('app/templates/image/upload.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键修改
                checks = [
                    ('maxImagesPerContent', '动态图片数量变量'),
                    ('loadImageLimits', '图片限制加载函数'),
                    ('updateImageLimitDisplay', '图片限制显示更新函数'),
                    ('/simple/api/system/image-limits', '图片限制API调用'),
                    ('dragOverlayText', '拖拽提示文字ID'),
                    ('${maxImagesPerContent}', '动态数量使用')
                ]
                
                for check_text, description in checks:
                    if check_text in content:
                        print(f"✅ {description}")
                    else:
                        print(f"❌ 缺少{description}")
                        
            except Exception as e:
                print(f"❌ 前端文件检查失败: {e}")
            
            # 4. 验证后端API修改
            print(f"\n4. 验证后端API修改:")
            print("-" * 40)
            
            try:
                with open('app/views/main_simple.py', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'get_image_limits' in content:
                    print("✅ 图片限制API函数已添加")
                else:
                    print("❌ 图片限制API函数未添加")
                
                if 'MAX_IMAGES_PER_CONTENT' in content:
                    print("✅ 后端使用MAX_IMAGES_PER_CONTENT设置")
                else:
                    print("❌ 后端未使用MAX_IMAGES_PER_CONTENT设置")
                    
            except Exception as e:
                print(f"❌ 后端文件检查失败: {e}")
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 图片数量限制修改验证完成！")
    print("\n修改内容:")
    print("1. ✅ 添加了 /simple/api/system/image-limits API接口")
    print("2. ✅ 前端页面添加了动态图片数量变量")
    print("3. ✅ 添加了图片限制加载和显示更新函数")
    print("4. ✅ 修改了拖拽提示文字为动态显示")
    print("5. ✅ 修改了模态框中的图片计数显示")
    print("\n预期效果:")
    print("- 🎯 系统设置中的图片数量限制会应用到上传页面")
    print("- 🔄 修改系统设置后，页面刷新会显示新的限制")
    print("- 💡 拖拽提示和计数显示都使用动态数量")
    print("- 🚫 上传时会根据系统设置验证数量限制")

if __name__ == '__main__':
    verify_image_limits_changes()
