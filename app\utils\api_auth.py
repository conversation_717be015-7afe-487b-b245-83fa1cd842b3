"""
API认证工具
"""
from functools import wraps
from flask import request, jsonify, current_app
from app.models import SystemSetting


def api_key_required(f):
    """
    API密钥验证装饰器
    用于验证API请求中的密钥是否有效
    
    使用方法：
    @api_bp.route('/some_endpoint')
    @api_key_required
    def some_endpoint():
        # 业务逻辑
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头中获取API密钥
        api_key = request.headers.get('X-API-Key')
        
        # 如果没有提供API密钥，返回401错误
        if not api_key:
            return jsonify({
                'success': False,
                'error': '缺少API密钥',
                'error_code': 'missing_api_key'
            }), 401
        
        # 从数据库获取有效的API密钥
        api_key_setting = SystemSetting.query.filter_by(key='API_KEY').first()
        
        # 如果数据库中没有设置API密钥，或者提供的密钥不匹配，返回401错误
        if not api_key_setting or api_key_setting.value != api_key:
            return jsonify({
                'success': False,
                'error': 'API密钥无效',
                'error_code': 'invalid_api_key'
            }), 401
        
        # API密钥验证通过，继续执行被装饰的函数
        return f(*args, **kwargs)
    
    return decorated_function


def generate_api_key(length=32):
    """
    生成随机API密钥
    
    参数:
        length: 密钥长度，默认32位
    
    返回:
        生成的随机密钥
    """
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits
    api_key = ''.join(secrets.choice(alphabet) for _ in range(length))
    
    return api_key 