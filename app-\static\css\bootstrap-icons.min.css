/* Bootstrap Icons - 简化版本，只包含项目中使用的图标 */
.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 常用图标 */
.bi-search::before { content: "\f52a"; }
.bi-plus::before { content: "\f4fe"; }
.bi-dash::before { content: "\f2ea"; }
.bi-x::before { content: "\f62a"; }
.bi-check::before { content: "\f26e"; }
.bi-trash::before { content: "\f5de"; }
.bi-pencil::before { content: "\f4cb"; }
.bi-eye::before { content: "\f341"; }
.bi-eye-slash::before { content: "\f340"; }
.bi-download::before { content: "\f30a"; }
.bi-upload::before { content: "\f603"; }
.bi-image::before { content: "\f3f5"; }
.bi-file::before { content: "\f377"; }
.bi-folder::before { content: "\f3d6"; }
.bi-house::before { content: "\f425"; }
.bi-person::before { content: "\f4da"; }
.bi-gear::before { content: "\f3e2"; }
.bi-list::before { content: "\f479"; }
.bi-grid::before { content: "\f3f1"; }
.bi-calendar::before { content: "\f1f6"; }
.bi-clock::before { content: "\f293"; }
.bi-envelope::before { content: "\f32f"; }
.bi-phone::before { content: "\f4e7"; }
.bi-heart::before { content: "\f414"; }
.bi-star::before { content: "\f588"; }
.bi-bookmark::before { content: "\f1a2"; }
.bi-share::before { content: "\f52e"; }
.bi-link::before { content: "\f470"; }
.bi-arrow-left::before { content: "\f12f"; }
.bi-arrow-right::before { content: "\f138"; }
.bi-arrow-up::before { content: "\f148"; }
.bi-arrow-down::before { content: "\f128"; }
.bi-chevron-left::before { content: "\f284"; }
.bi-chevron-right::before { content: "\f285"; }
.bi-chevron-up::before { content: "\f286"; }
.bi-chevron-down::before { content: "\f282"; }
.bi-caret-left::before { content: "\f230"; }
.bi-caret-right::before { content: "\f234"; }
.bi-caret-up::before { content: "\f238"; }
.bi-caret-down::before { content: "\f22c"; }
.bi-arrows-move::before { content: "\f14e"; }
.bi-arrows-expand::before { content: "\f14c"; }
.bi-fullscreen::before { content: "\f14d"; }
.bi-fullscreen-exit::before { content: "\f14b"; }
.bi-zoom-in::before { content: "\f62c"; }
.bi-zoom-out::before { content: "\f62d"; }
.bi-refresh::before { content: "\f130"; }
.bi-reload::before { content: "\f130"; }
.bi-save::before { content: "\f525"; }
.bi-copy::before { content: "\f28b"; }
.bi-clipboard::before { content: "\f290"; }
.bi-cut::before { content: "\f528"; }
.bi-scissors::before { content: "\f528"; }
.bi-filter::before { content: "\f3d4"; }
.bi-sort::before { content: "\f575"; }
.bi-sort-up::before { content: "\f57b"; }
.bi-sort-down::before { content: "\f575"; }
.bi-table::before { content: "\f5aa"; }
.bi-columns::before { content: "\f2ce"; }
.bi-rows::before { content: "\f2ce"; }
.bi-layout-sidebar::before { content: "\f61f"; }
.bi-layout-split::before { content: "\f6d1"; }
.bi-menu-button::before { content: "\f479"; }
.bi-menu-button-wide::before { content: "\f479"; }
.bi-three-dots::before { content: "\f5d4"; }
.bi-three-dots-vertical::before { content: "\f5d3"; }
.bi-info::before { content: "\f431"; }
.bi-info-circle::before { content: "\f431"; }
.bi-question::before { content: "\f50c"; }
.bi-question-circle::before { content: "\f505"; }
.bi-exclamation::before { content: "\f33c"; }
.bi-exclamation-circle::before { content: "\f333"; }
.bi-exclamation-triangle::before { content: "\f33b"; }
.bi-check-circle::before { content: "\f26b"; }
.bi-x-circle::before { content: "\f623"; }
.bi-plus-circle::before { content: "\f4fa"; }
.bi-dash-circle::before { content: "\f2e6"; }
.bi-play::before { content: "\f4f5"; }
.bi-pause::before { content: "\f4c3"; }
.bi-stop::before { content: "\f593"; }
.bi-skip-backward::before { content: "\f552"; }
.bi-skip-forward::before { content: "\f55e"; }
.bi-volume-up::before { content: "\f611"; }
.bi-volume-down::before { content: "\f60b"; }
.bi-volume-mute::before { content: "\f60d"; }
.bi-brightness-high::before { content: "\f1d2"; }
.bi-brightness-low::before { content: "\f1d4"; }
.bi-contrast::before { content: "\f288"; }
.bi-display::before { content: "\f302"; }
.bi-laptop::before { content: "\f45a"; }
.bi-tablet::before { content: "\f5ae"; }
.bi-phone-landscape::before { content: "\f4e4"; }
.bi-wifi::before { content: "\f61c"; }
.bi-wifi-off::before { content: "\f61b"; }
.bi-bluetooth::before { content: "\f682"; }
.bi-battery::before { content: "\f188"; }
.bi-battery-full::before { content: "\f186"; }
.bi-battery-half::before { content: "\f187"; }
.bi-power::before { content: "\f4ff"; }
.bi-geo-alt::before { content: "\f3e5"; }
.bi-geo::before { content: "\f3e4"; }
.bi-map::before { content: "\f48b"; }
.bi-compass::before { content: "\f2d1"; }
.bi-globe::before { content: "\f3ee"; }
.bi-cloud::before { content: "\f2c1"; }
.bi-cloud-download::before { content: "\f29b"; }
.bi-cloud-upload::before { content: "\f2c0"; }
.bi-shield::before { content: "\f53f"; }
.bi-shield-check::before { content: "\f52f"; }
.bi-shield-x::before { content: "\f53e"; }
.bi-lock::before { content: "\f47d"; }
.bi-unlock::before { content: "\f600"; }
.bi-key::before { content: "\f45c"; }
.bi-credit-card::before { content: "\f2dc"; }
.bi-wallet::before { content: "\f614"; }
.bi-cash::before { content: "\f247"; }
.bi-currency-dollar::before { content: "\f636"; }
.bi-cart::before { content: "\f242"; }
.bi-bag::before { content: "\f179"; }
.bi-gift::before { content: "\f3eb"; }
.bi-award::before { content: "\f154"; }
.bi-trophy::before { content: "\f5e7"; }
.bi-flag::before { content: "\f3d1"; }
.bi-bookmark-fill::before { content: "\f199"; }
.bi-heart-fill::before { content: "\f415"; }
.bi-star-fill::before { content: "\f586"; }
.bi-check-circle-fill::before { content: "\f26a"; }
.bi-x-circle-fill::before { content: "\f622"; }
.bi-plus-circle-fill::before { content: "\f4f9"; }
.bi-dash-circle-fill::before { content: "\f2e5"; }
.bi-exclamation-circle-fill::before { content: "\f332"; }
.bi-exclamation-triangle-fill::before { content: "\f33a"; }
.bi-info-circle-fill::before { content: "\f431"; }
.bi-question-circle-fill::before { content: "\f504"; }
.bi-play-fill::before { content: "\f4f4"; }
.bi-pause-fill::before { content: "\f4c2"; }
.bi-stop-fill::before { content: "\f592"; }
.bi-volume-up-fill::before { content: "\f610"; }
.bi-volume-down-fill::before { content: "\f60a"; }
.bi-volume-mute-fill::before { content: "\f60c"; }
.bi-brightness-high-fill::before { content: "\f1d1"; }
.bi-brightness-low-fill::before { content: "\f1d3"; }
.bi-shield-fill::before { content: "\f536"; }
.bi-shield-fill-check::before { content: "\f531"; }
.bi-shield-fill-x::before { content: "\f535"; }
.bi-lock-fill::before { content: "\f47c"; }
.bi-unlock-fill::before { content: "\f5ff"; }
.bi-credit-card-fill::before { content: "\f2db"; }
.bi-wallet-fill::before { content: "\f613"; }
.bi-cart-fill::before { content: "\f23d"; }
.bi-bag-fill::before { content: "\f174"; }
.bi-award-fill::before { content: "\f153"; }
.bi-trophy-fill::before { content: "\f5e6"; }
.bi-flag-fill::before { content: "\f3d0"; }