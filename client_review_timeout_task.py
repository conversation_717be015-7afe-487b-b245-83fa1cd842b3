#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户审核超时检查定时任务
可以通过cron或Windows任务计划程序定期执行
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.client_review_timeout import ClientReviewTimeoutService
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/client_review_timeout.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def run_client_review_timeout_check():
    """运行客户审核超时检查"""
    logger.info("开始执行客户审核超时检查定时任务...")
    
    app = create_app()
    
    with app.app_context():
        try:
            processed_count = ClientReviewTimeoutService.check_and_process_timeouts()
            
            if processed_count > 0:
                logger.info(f"✅ 客户审核超时检查完成，处理了 {processed_count} 篇文案")
            else:
                logger.info("✅ 客户审核超时检查完成，没有需要处理的超时文案")
                
            return processed_count
            
        except Exception as e:
            logger.error(f"❌ 客户审核超时检查失败: {e}")
            import traceback
            traceback.print_exc()
            return 0

if __name__ == '__main__':
    run_client_review_timeout_check()
