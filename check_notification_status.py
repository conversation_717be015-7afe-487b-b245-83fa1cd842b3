#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查通知功能的当前状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting
from sqlalchemy import text

def check_notification_status():
    """检查通知功能的当前状态"""
    print("🔍 检查通知功能的当前状态...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查通知相关的系统设置
            print("1. 检查通知相关的系统设置:")
            print("-" * 40)
            
            notification_settings = SystemSetting.query.filter(
                SystemSetting.key.like('%notification%')
            ).all()
            
            if notification_settings:
                for setting in notification_settings:
                    print(f"✅ {setting.key}: {setting.value}")
                    print(f"   描述: {setting.description}")
                    print()
            else:
                print("❌ 没有找到通知相关的系统设置")
            
            # 2. 检查通知表是否存在
            print("2. 检查通知表是否存在:")
            print("-" * 40)
            
            try:
                # 尝试查询通知表
                result = db.session.execute(text("SHOW TABLES LIKE 'notifications'"))
                table_exists = result.fetchone() is not None
                
                if table_exists:
                    print("✅ notifications 表存在")
                    
                    # 检查表结构
                    result = db.session.execute(text("DESCRIBE notifications"))
                    columns = result.fetchall()
                    
                    print("   表结构:")
                    for col in columns:
                        print(f"     {col[0]}: {col[1]} {col[2]}")
                    
                    # 检查表中的数据
                    result = db.session.execute(text("SELECT COUNT(*) FROM notifications"))
                    count = result.fetchone()[0]
                    print(f"   数据条数: {count}")
                    
                else:
                    print("❌ notifications 表不存在")
                    
            except Exception as e:
                print(f"❌ 检查通知表失败: {e}")
            
            # 3. 检查通知模型是否可用
            print(f"\n3. 检查通知模型是否可用:")
            print("-" * 40)
            
            try:
                from app.models.notification import Notification
                print("✅ 通知模型导入成功")
                
                # 尝试查询通知
                notifications = Notification.query.limit(5).all()
                print(f"✅ 查询通知成功，找到 {len(notifications)} 条通知")
                
                for notification in notifications:
                    print(f"   - {notification.title} ({notification.type})")
                    
            except Exception as e:
                print(f"❌ 通知模型不可用: {e}")
            
            # 4. 检查通知视图是否存在
            print(f"\n4. 检查通知视图是否存在:")
            print("-" * 40)
            
            notification_view_path = "app/views/notification.py"
            if os.path.exists(notification_view_path):
                print("✅ 通知视图文件存在")
            else:
                print("❌ 通知视图文件不存在")
            
            # 5. 检查通知模板是否存在
            print(f"\n5. 检查通知模板是否存在:")
            print("-" * 40)
            
            notification_template_dir = "app/templates/notification"
            if os.path.exists(notification_template_dir):
                print("✅ 通知模板目录存在")
                
                # 列出模板文件
                template_files = os.listdir(notification_template_dir)
                for file in template_files:
                    print(f"   - {file}")
            else:
                print("❌ 通知模板目录不存在")
            
            # 6. 检查通知功能的实际使用情况
            print(f"\n6. 通知功能使用情况分析:")
            print("-" * 40)
            
            # 检查是否有创建通知的代码
            notification_usage_files = []
            
            # 搜索可能使用通知的文件
            search_dirs = ['app/views', 'app/services', 'app/utils']
            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    for root, dirs, files in os.walk(search_dir):
                        for file in files:
                            if file.endswith('.py'):
                                file_path = os.path.join(root, file)
                                try:
                                    with open(file_path, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                        if 'Notification' in content or 'notification' in content.lower():
                                            notification_usage_files.append(file_path)
                                except:
                                    pass
            
            if notification_usage_files:
                print("✅ 找到使用通知功能的文件:")
                for file in notification_usage_files:
                    print(f"   - {file}")
            else:
                print("❌ 没有找到使用通知功能的文件")
            
            # 7. 总结分析
            print(f"\n7. 通知功能状态总结:")
            print("-" * 40)
            
            has_setting = len(notification_settings) > 0
            has_table = table_exists if 'table_exists' in locals() else False
            has_model = True
            try:
                from app.models.notification import Notification
            except:
                has_model = False
            has_view = os.path.exists("app/views/notification.py")
            has_template = os.path.exists("app/templates/notification")
            
            print(f"📊 功能完整性评估:")
            print(f"   系统设置: {'✅' if has_setting else '❌'}")
            print(f"   数据库表: {'✅' if has_table else '❌'}")
            print(f"   数据模型: {'✅' if has_model else '❌'}")
            print(f"   视图控制器: {'✅' if has_view else '❌'}")
            print(f"   页面模板: {'✅' if has_template else '❌'}")
            
            # 判断功能状态
            if has_setting and has_table and has_model:
                if has_view and has_template:
                    print(f"\n🎉 通知功能: 完全实现且可用")
                    print(f"   - 系统设置中的开关是有效的")
                    print(f"   - 可以正常开启/关闭通知功能")
                else:
                    print(f"\n⚠️ 通知功能: 部分实现")
                    print(f"   - 数据层已实现，但缺少界面")
                    print(f"   - 系统设置中的开关可能无效")
            else:
                print(f"\n❌ 通知功能: 未实现或不完整")
                print(f"   - 系统设置中的开关是无效的")
                print(f"   - 建议删除或完善该功能")
            
        except Exception as e:
            print(f"❌ 检查过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🔍 通知功能状态检查完成！")

if __name__ == '__main__':
    check_notification_status()
