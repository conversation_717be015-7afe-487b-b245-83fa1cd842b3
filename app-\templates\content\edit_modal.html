<!-- 编辑文案模态框内容 -->
<form id="editContentForm" data-content-id="{{ content.id }}" onsubmit="return submitEditFormDirectly(event, {{ content.id }})" action="javascript:void(0)">
    <div class="row">
        <div class="col-md-8">
            <!-- 基本信息 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0"><i class="bi bi-pencil"></i> 编辑文案</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="editTitle" class="form-label">标题</label>
                        <input type="text" class="form-control" id="editTitle" name="title"
                               value="{{ content.title }}" required>
                        <div class="form-text">
                            字符统计: <span id="titleLength">0</span>/20 
                            <span id="titleStatus" class="text-success">✓ 符合要求</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editContent" class="form-label">文案内容</label>
                        <textarea class="form-control" id="editContent" name="content"
                                  rows="10" required>{{ content.content }}</textarea>
                        <div class="form-text">
                            字符统计: <span id="contentLength">0</span>/1000 
                            <span id="contentStatus" class="text-success">✓ 符合要求</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 话题标签编辑 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="card-title mb-0 small"><i class="bi bi-tags"></i> 话题标签</h6>
                </div>
                <div class="card-body py-2">
                    <div class="input-group input-group-sm mb-2">
                        <input type="text" class="form-control form-control-sm" id="topicInput"
                               placeholder="输入话题后按回车添加" autocomplete="off">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditTopic()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    <div id="topicsContainer" class="tags-container">
                        <!-- 话题标签将在这里显示 -->
                    </div>
                    <textarea class="form-control d-none" id="editTopics" name="topics">{{ content.topics_list|join('\n') if content.topics_list else '' }}</textarea>
                    <small class="text-muted">按回车或点击+号添加话题</small>
                </div>
            </div>

            <!-- @用户编辑 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="card-title mb-0 small"><i class="bi bi-at"></i> @用户</h6>
                </div>
                <div class="card-body py-2">
                    <div class="input-group input-group-sm mb-2">
                        <input type="text" class="form-control form-control-sm" id="atUserInput"
                               placeholder="输入用户名后按回车添加" autocomplete="off">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditAtUser()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    <div id="atUsersContainer" class="tags-container">
                        <!-- @用户标签将在这里显示 -->
                    </div>
                    <textarea class="form-control d-none" id="editAtUsers" name="at_users">{{ content.at_users_list|join('\n') if content.at_users_list else '' }}</textarea>
                    <small class="text-muted">按回车或点击+号添加@用户</small>
                </div>
            </div>

            <!-- 位置信息编辑 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="card-title mb-0 small"><i class="bi bi-geo-alt"></i> 位置信息</h6>
                </div>
                <div class="card-body py-2">
                    <input type="text" class="form-control form-control-sm" id="editLocation" name="location"
                           value="{{ content.location or '' }}" placeholder="输入位置信息">
                    <small class="text-muted">输入单个位置信息</small>
                </div>
            </div>
            <!-- 发布设置 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="card-title mb-0 small"><i class="bi bi-calendar"></i> 发布设置</h6>
                </div>
                <div class="card-body py-2">
                    <div class="mb-0">
                        <label for="editPriority" class="form-label small">发布优先级</label>
                        <select class="form-select form-select-sm" id="editPriority" name="publish_priority">
                            <option value="low" {% if content.publish_priority == 'low' %}selected{% endif %}>低</option>
                            <option value="normal" {% if content.publish_priority == 'normal' or not content.publish_priority %}selected{% endif %}>普通</option>
                            <option value="high" {% if content.publish_priority == 'high' %}selected{% endif %}>高</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 状态信息 -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="card-title mb-0 small"><i class="bi bi-info-circle"></i> 状态信息</h6>
                </div>
                <div class="card-body py-2">
                    <p class="mb-1 small"><strong>当前状态：</strong>
                        {% if content.workflow_status == 'draft' %}
                            <span class="badge bg-secondary" style="font-size: 0.7rem;">草稿</span>
                        {% elif content.workflow_status == 'pending_review' %}
                            <span class="badge bg-warning" style="font-size: 0.7rem;">待初审</span>
                        {% elif content.workflow_status == 'first_approved' %}
                            <span class="badge bg-info" style="font-size: 0.7rem;">初审通过</span>
                        {% elif content.workflow_status == 'image_uploaded' %}
                            <span class="badge bg-primary" style="font-size: 0.7rem;">已上传图片</span>
                        {% else %}
                            <span class="badge bg-secondary" style="font-size: 0.7rem;">{{ content.workflow_status }}</span>
                        {% endif %}
                    </p>
                    <p class="mb-1 small"><strong>客户：</strong>{{ content.client.name if content.client else '未知' }}</p>
                    <p class="mb-1 small"><strong>创建时间：</strong>{{ content.created_at.strftime('%m-%d %H:%M') if content.created_at else '未知' }}</p>

                    <!-- 驳回理由显示 - 只在驳回状态时显示 -->
                    {% set is_rejected = (content.client_review_status == 'rejected') or
                                        (content.internal_review_status in ['rejected', 'final_rej_text', 'final_rej_img', 'final_rej_both', 'final_rej_text_ok', 'final_rej_img_ok', 'client_rej_text', 'client_rej_img', 'client_rej_both']) %}
                    {% if content.rejection_reasons and is_rejected %}
                        <hr class="my-2">
                        <div class="mb-0">
                            <strong class="text-danger small"><i class="bi bi-exclamation-triangle"></i> 驳回理由：</strong>
                            {% for reason in content.rejection_reasons %}
                                <div class="mt-1 p-2 bg-light border-start border-danger border-3 rounded-end">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <span class="badge bg-danger me-1" style="font-size: 0.6rem;">{{ reason.get_type_display() }}</span>
                                            <small class="text-muted">{{ reason.created_at.strftime('%m-%d %H:%M') }}</small>
                                        </div>
                                    </div>
                                    <div class="mt-1">
                                        <small class="text-dark">{{ reason.reason }}</small>
                                    </div>
                                    {% if reason.creator %}
                                        <div class="mt-1">
                                            <small class="text-muted">审核人：{{ reason.creator.username }}</small>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 关闭
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check"></i> 保存修改
                </button>
            </div>
        </div>
    </div>
</form>

<style>
.tags-container {
    min-height: 30px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.25rem;
    background-color: #f8f9fa;
}

.tag {
    display: inline-block;
    background-color: #0d6efd;
    color: white;
    padding: 0.2rem 0.5rem;
    margin: 0.1rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
}

.tag.location {
    background-color: #dc3545;
}

.tag.at-user {
    background-color: #17a2b8;
}

.tag:hover {
    opacity: 0.8;
}

.tag .remove {
    margin-left: 0.3rem;
    cursor: pointer;
    font-weight: bold;
}
</style>

<script>
// 字符统计函数
function calculateTitleLength(text) {
    let length = 0;
    for (let char of text) {
        // 检查是否是emoji
        if (char.codePointAt(0) > 127) {
            // 检查是否是中文字符
            if (/[\u4e00-\u9fff]/.test(char)) {
                length += 1; // 中文字符算1个
            } else {
                length += 1; // emoji算1个
            }
        } else {
            // 字母数字算0.5个
            length += 0.5;
        }
    }
    return Math.ceil(length); // 向上取整
}

function calculateContentLength(text) {
    let length = 0;
    for (let char of text) {
        // 检查是否是中文字符
        if (/[\u4e00-\u9fff]/.test(char)) {
            length += 2; // 中文字符算2个
        } else {
            length += 1; // 其他字符算1个
        }
    }
    return length;
}

// 更新标题字符统计
function updateTitleLength() {
    console.log('=== updateTitleLength 被调用 ===');
    const titleInput = document.getElementById('editTitle');
    const titleLength = document.getElementById('titleLength');
    const titleStatus = document.getElementById('titleStatus');
    
    console.log('标题元素检查:', {
        titleInput: titleInput ? '找到' : '未找到',
        titleLength: titleLength ? '找到' : '未找到',
        titleStatus: titleStatus ? '找到' : '未找到'
    });
    
    if (titleInput && titleLength && titleStatus) {
        const text = titleInput.value;
        const length = calculateTitleLength(text);
        
        console.log('编辑模态框标题统计:', { text, length, originalLength: text.length });
        
        titleLength.textContent = length;
        
        if (length > 20) {
            titleLength.className = 'text-danger';
            titleStatus.className = 'text-danger';
            titleStatus.textContent = '✗ 超出限制';
        } else {
            titleLength.className = 'text-success';
            titleStatus.className = 'text-success';
            titleStatus.textContent = '✓ 符合要求';
        }
    } else {
        console.error('编辑模态框找不到标题相关元素:', { titleInput, titleLength, titleStatus });
    }
}

// 更新内容字符统计
function updateContentLength() {
    console.log('=== updateContentLength 被调用 ===');
    const contentInput = document.getElementById('editContent');
    const contentLength = document.getElementById('contentLength');
    const contentStatus = document.getElementById('contentStatus');
    
    console.log('内容元素检查:', {
        contentInput: contentInput ? '找到' : '未找到',
        contentLength: contentLength ? '找到' : '未找到',
        contentStatus: contentStatus ? '找到' : '未找到'
    });
    
    if (contentInput && contentLength && contentStatus) {
        const text = contentInput.value;
        const length = calculateContentLength(text);
        
        console.log('编辑模态框内容统计:', { text, length, originalLength: text.length });
        
        contentLength.textContent = length;
        
        if (length > 1000) {
            contentLength.className = 'text-danger';
            contentStatus.className = 'text-danger';
            contentStatus.textContent = '✗ 超出限制';
        } else {
            contentLength.className = 'text-success';
            contentStatus.className = 'text-success';
            contentStatus.textContent = '✓ 符合要求';
        }
    } else {
        console.error('编辑模态框找不到内容相关元素:', { contentInput, contentLength, contentStatus });
    }
}

// 字数统计
document.getElementById('editContent').addEventListener('input', function() {
    updateContentLength();
});

// 标题字符统计
document.getElementById('editTitle').addEventListener('input', function() {
    updateTitleLength();
});

// 初始化标签
document.addEventListener('DOMContentLoaded', function() {
    initializeTags();
    setupInputEvents();
});

// 立即执行初始化（用于AJAX加载的情况）
setTimeout(function() {
    console.log('延迟初始化标签...');
    initializeTags();
    setupInputEvents();
    
    // 确保字符统计正确初始化
    setTimeout(() => {
        console.log('延迟初始化字符统计...');
        updateTitleLength();
        updateContentLength();
    }, 50);
}, 100);

// 全局初始化函数，供父页面调用
window.initializeEditModal = function() {
    console.log('全局初始化编辑模态框...');
    initializeTags();
    setupInputEvents();
    setupFormSubmit();
    
    // 确保字符统计正确初始化
    setTimeout(() => {
        console.log('全局初始化字符统计...');
        updateTitleLength();
        updateContentLength();
    }, 100);
};

// submitEditForm函数将在后面定义后设为全局函数

// 设置表单提交事件
function setupFormSubmit() {
    const form = document.getElementById('editContentForm');
    if (form) {
        console.log('设置表单提交事件监听器...');

        // 移除现有的事件监听器
        form.removeEventListener('submit', handleFormSubmit);

        // 添加新的事件监听器
        form.addEventListener('submit', handleFormSubmit);

        console.log('表单提交事件监听器已设置');
    } else {
        console.error('找不到编辑表单');
    }
}

// 处理表单提交
function handleFormSubmit(event) {
    console.log('表单提交事件被触发');
    const contentId = event.target.getAttribute('data-content-id') ||
                     document.querySelector('[data-content-id]')?.getAttribute('data-content-id');

    if (contentId) {
        return submitEditForm(event, contentId);
    } else {
        console.error('找不到content ID');
        event.preventDefault();
        return false;
    }
}

// 初始化现有标签
function initializeTags() {
    console.log('开始初始化标签...');

    // 清空现有标签容器
    const topicsContainer = document.getElementById('topicsContainer');
    const atUsersContainer = document.getElementById('atUsersContainer');

    if (topicsContainer) {
        topicsContainer.innerHTML = '';
    }
    if (atUsersContainer) {
        atUsersContainer.innerHTML = '';
    }

    // 初始化话题标签
    const topicsField = document.getElementById('editTopics');
    if (topicsField) {
        const topicsText = topicsField.value;
        console.log('话题数据:', topicsText);
        if (topicsText && topicsText.trim()) {
            const topics = topicsText.split('\n').filter(t => t.trim());
            console.log('解析的话题:', topics);
            topics.forEach(topic => {
                if (topic.trim()) {
                    addTagToContainer('topicsContainer', topic.trim(), 'topic');
                }
            });
        }
    }

    // 初始化@用户标签
    const atUsersField = document.getElementById('editAtUsers');
    if (atUsersField) {
        const atUsersText = atUsersField.value;
        console.log('@用户数据:', atUsersText);
        if (atUsersText && atUsersText.trim()) {
            const atUsers = atUsersText.split('\n').filter(u => u.trim());
            console.log('解析的@用户:', atUsers);
            atUsers.forEach(user => {
                if (user.trim()) {
                    addTagToContainer('atUsersContainer', user.trim(), 'atUser');
                }
            });
        }
    }

    console.log('编辑标签初始化完成');
    
    // 立即初始化字符统计
    console.log('立即初始化字符统计...');
    updateTitleLength();
    updateContentLength();
    
    // 延迟再次初始化字符统计，确保DOM元素已完全加载
    setTimeout(() => {
        console.log('延迟初始化字符统计...');
        updateTitleLength();
        updateContentLength();
    }, 100);
}

// 设置输入框事件
function setupInputEvents() {
    // 话题输入框
    const topicInput = document.getElementById('topicInput');
    if (topicInput) {
        topicInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addTopic();
            }
        });

        // 失去焦点时自动添加标签
        topicInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                addTopic();
            }
        });
    }

    // @用户输入框
    const atUserInput = document.getElementById('atUserInput');
    if (atUserInput) {
        atUserInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addAtUser();
            }
        });

        // 失去焦点时自动添加标签
        atUserInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                addAtUser();
            }
        });
    }
}

// 添加话题
window.addTopic = function() {
    const input = document.getElementById('topicInput');
    const value = input.value.trim();
    if (value) {
        addTagToContainer('topicsContainer', value, 'topic');
        input.value = '';
        updateHiddenField('topicsContainer', 'editTopics');
    }
}

// 添加@用户
window.addAtUser = function() {
    const input = document.getElementById('atUserInput');
    const value = input.value.trim();
    if (value) {
        addTagToContainer('atUsersContainer', value, 'at-user');
        input.value = '';
        updateHiddenField('atUsersContainer', 'editAtUsers');
    }
}

// 添加标签到容器
window.addTagToContainer = function(containerId, text, type) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('找不到容器:', containerId);
        return;
    }

    // 检查是否已存在相同标签
    const existingTags = container.querySelectorAll('.tag');
    for (let existingTag of existingTags) {
        const existingText = existingTag.textContent.replace('×', '').trim();
        let compareText = text;
        if (type === 'topic' && !text.startsWith('#')) {
            compareText = '#' + text;
        } else if (type === 'at-user' && !text.startsWith('@')) {
            compareText = '@' + text;
        }
        if (existingText === compareText) {
            console.log('标签已存在:', compareText);
            return;
        }
    }

    const tag = document.createElement('span');
    tag.className = `tag ${type}`;

    let displayText = text;
    if (type === 'topic' && !text.startsWith('#')) {
        displayText = '#' + text;
    } else if (type === 'at-user' && !text.startsWith('@')) {
        displayText = '@' + text;
    }

    tag.innerHTML = `${displayText}<span class="remove">×</span>`;

    // 为删除按钮添加事件监听器
    const removeBtn = tag.querySelector('.remove');
    removeBtn.addEventListener('click', function() {
        removeTag(tag);
    });

    container.appendChild(tag);
    console.log('添加标签:', displayText, '到容器:', containerId);
}

// 删除标签
window.removeTag = function(tagElement) {
    const container = tagElement.parentElement;
    const tagText = tagElement.textContent.replace('×', '').trim();
    console.log('删除标签:', tagText, '从容器:', container.id);

    tagElement.remove();

    // 更新对应的隐藏字段
    if (container.id === 'topicsContainer') {
        updateHiddenField('topicsContainer', 'editTopics');
    } else if (container.id === 'atUsersContainer') {
        updateHiddenField('atUsersContainer', 'editAtUsers');
    }
}

// 更新隐藏字段
window.updateHiddenField = function(containerId, hiddenFieldId) {
    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);
    const tags = container.querySelectorAll('.tag');

    const values = [];
    tags.forEach(tag => {
        let text = tag.textContent.replace('×', '').trim();
        // 移除前缀符号
        if (text.startsWith('#') || text.startsWith('@')) {
            text = text.substring(1);
        }
        values.push(text);
    });

    hiddenField.value = values.join('\n');
}

// 提交编辑表单
window.submitEditForm = function(event, contentId) {
    console.log('=== submitEditForm 函数被调用 ===');
    console.log('Event:', event);
    console.log('Content ID:', contentId);

    // 立即阻止默认表单提交
    if (event) {
        event.preventDefault();
        event.stopPropagation();
        console.log('已阻止默认表单提交');
    }

    // 防止重复提交
    const submitBtn = document.querySelector('#editContentForm button[type="submit"]');
    if (submitBtn && submitBtn.disabled) {
        console.log('表单正在提交中，忽略重复请求');
        return false;
    }

    // 字符限制验证
    const titleInput = document.getElementById('editTitle');
    const contentInput = document.getElementById('editContent');
    
    if (titleInput && contentInput) {
        const titleText = titleInput.value.trim();
        const contentText = contentInput.value.trim();
        
        // 验证标题长度
        const titleLength = calculateTitleLength(titleText);
        if (titleLength > 20) {
            if (typeof showToast === 'function') {
                showToast(`标题超出限制！当前${titleLength}个字符，最多20个字符`, 'error');
            } else {
                alert(`标题超出限制！当前${titleLength}个字符，最多20个字符`);
            }
            return false;
        }
        
        // 验证内容长度
        const contentLength = calculateContentLength(contentText);
        if (contentLength > 1000) {
            if (typeof showToast === 'function') {
                showToast(`内容超出限制！当前${contentLength}个字符，最多1000个字符`, 'error');
            } else {
                alert(`内容超出限制！当前${contentLength}个字符，最多1000个字符`);
            }
            return false;
        }
    }

    console.log('=== 开始提交编辑表单 ===');

    // 在提交前更新所有隐藏字段
    updateHiddenField('topicsContainer', 'editTopics');
    updateHiddenField('atUsersContainer', 'editAtUsers');

    const form = event.target;
    const formData = new FormData(form);

    // 打印表单数据
    console.log('=== 表单数据 ===');
    for (let [key, value] of formData.entries()) {
        console.log(key + ':', value);
    }
    console.log('=== 表单数据结束 ===');
    
    // 显示加载状态
    let submitBtn = form.querySelector('button[type="submit"]');
    let originalText = submitBtn ? submitBtn.innerHTML : '保存修改';
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
        submitBtn.disabled = true;
    }
    
    const apiUrl = `/simple/api/contents/${contentId}/update`;
    console.log('发送请求到:', apiUrl);

    fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('收到响应，状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('响应数据:', data);
        if (data.success) {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast(data.message || '保存成功', 'success');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast(data.message || '保存成功', 'success');
            } else {
                // 创建简单的toast提示
                createSimpleToast(data.message || '保存成功', 'success');
            }
            closeEditModal();
            refreshReviewPage(); // 刷新页面
        } else {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast(data.message || '保存失败', 'error');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast(data.message || '保存失败', 'error');
            } else {
                // 创建简单的toast提示
                createSimpleToast(data.message || '保存失败', 'error');
            }
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        // 使用toast提示替换alert
        if (typeof showToast === 'function') {
            showToast('保存失败，请重试', 'error');
        } else if (typeof showCustomToast === 'function') {
            showCustomToast('保存失败，请重试', 'error');
        } else {
            // 创建简单的toast提示
            createSimpleToast('保存失败，请重试', 'error');
        }
    })
    .finally(() => {
        // 恢复按钮状态
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });

    // 阻止默认表单提交
    return false;
}

// 创建简单的toast提示函数
function createSimpleToast(message, type = 'success') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
    const iconClass = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle';

    const toastHtml = `
        <div class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${iconClass} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        toast.show();

        // 监听隐藏事件，移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // 如果Bootstrap不可用，使用简单的显示/隐藏
        toastElement.style.display = 'block';
        setTimeout(() => {
            toastElement.remove();
        }, 3000);
    }
}


</script>
