#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户分享链接工具类
"""

import secrets
import hashlib
from datetime import datetime, timedelta
from flask import current_app, url_for
from app.models import db
from app.models.client import Client

class ShareLinkGenerator:
    """分享链接生成器"""
    
    @staticmethod
    def generate_share_key(client_id):
        """
        生成分享密钥
        
        Args:
            client_id: 客户ID
            
        Returns:
            str: 生成的分享密钥
        """
        # 生成随机字符串
        random_str = secrets.token_urlsafe(32)
        
        # 组合客户ID和时间戳
        timestamp = str(int(datetime.now().timestamp()))
        raw_data = f"{client_id}:{timestamp}:{random_str}"
        
        # 生成最终密钥
        share_key = hashlib.sha256(raw_data.encode()).hexdigest()[:32]
        
        return share_key
    
    @staticmethod
    def create_share_link(client_id, expires_days=None, access_key=None, task_id=None):
        """
        为客户创建分享链接

        Args:
            client_id: 客户ID
            expires_days: 有效期天数，None或0表示永久有效
            access_key: 访问密钥
            task_id: 任务ID，限制访问范围

        Returns:
            ClientShareLink: 分享链接对象
        """
        from app.models.client import ClientShareLink

        # 计算过期时间
        expires_at = None
        if expires_days is not None and expires_days > 0:
            expires_at = datetime.now() + timedelta(days=expires_days)

        # 生成分享密钥
        share_key = ShareLinkGenerator.generate_share_key(client_id)

        # 创建分享链接记录
        share_link = ClientShareLink(
            client_id=client_id,
            share_key=share_key,
            expires_at=expires_at,
            access_key=access_key,
            task_id=task_id,
            is_active=True,
            created_at=datetime.now()
        )

        db.session.add(share_link)
        db.session.commit()

        return share_link
    
    @staticmethod
    def get_share_url(share_key, _external=True):
        """
        获取完整的分享URL
        
        Args:
            share_key: 分享密钥
            _external: 是否生成完整URL
            
        Returns:
            str: 分享URL
        """
        return url_for('client_review.review_page', share_key=share_key, _external=_external)
    
    @staticmethod
    def validate_share_key(share_key, access_key=None):
        """
        验证分享密钥

        Args:
            share_key: 分享密钥
            access_key: 访问密钥（可选）

        Returns:
            tuple: (is_valid, client_id, error_message)
        """
        from app.models.client import ClientShareLink

        if not share_key:
            return False, None, "分享密钥不能为空"

        # 查找分享链接
        share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
        if not share_link:
            return False, None, "分享链接不存在"

        # 检查是否激活
        if not share_link.is_active:
            return False, None, "分享链接已被禁用"

        # 检查是否过期
        if share_link.expires_at and share_link.expires_at < datetime.now():
            return False, None, "分享链接已过期"

        # 检查访问密钥（如果设置了访问密钥）
        if share_link.access_key:
            if not access_key:
                return False, None, "需要访问密钥"
            if access_key != share_link.access_key:
                return False, None, "访问密钥错误"

        # 检查客户是否存在
        client = Client.query.get(share_link.client_id)
        if not client:
            return False, None, "客户不存在"

        return True, share_link.client_id, None
    
    @staticmethod
    def deactivate_share_link(share_key):
        """
        禁用分享链接
        
        Args:
            share_key: 分享密钥
            
        Returns:
            bool: 是否成功
        """
        from app.models.client import ClientShareLink
        
        share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
        if share_link:
            share_link.is_active = False
            share_link.updated_at = datetime.now()
            db.session.commit()
            return True
        
        return False
    
    @staticmethod
    def refresh_share_link(client_id, expires_days=None):
        """
        刷新客户的分享链接（禁用旧的，创建新的）
        
        Args:
            client_id: 客户ID
            expires_days: 有效期天数
            
        Returns:
            ClientShareLink: 新的分享链接对象
        """
        from app.models.client import ClientShareLink
        
        # 禁用该客户的所有旧链接
        old_links = ClientShareLink.query.filter_by(client_id=client_id, is_active=True).all()
        for link in old_links:
            link.is_active = False
            link.updated_at = datetime.now()
        
        # 创建新链接
        new_link = ShareLinkGenerator.create_share_link(client_id, expires_days)
        
        return new_link
    
    @staticmethod
    def get_client_share_links(client_id, include_inactive=False):
        """
        获取客户的分享链接列表
        
        Args:
            client_id: 客户ID
            include_inactive: 是否包含已禁用的链接
            
        Returns:
            list: 分享链接列表
        """
        from app.models.client import ClientShareLink
        
        query = ClientShareLink.query.filter_by(client_id=client_id)
        if not include_inactive:
            query = query.filter_by(is_active=True)
        
        return query.order_by(ClientShareLink.created_at.desc()).all()
    
    @staticmethod
    def get_share_link_stats(share_key):
        """
        获取分享链接的使用统计
        
        Args:
            share_key: 分享密钥
            
        Returns:
            dict: 统计信息
        """
        from app.models.client import ClientShareLink
        from app.models.content import Content
        
        share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
        if not share_link:
            return None
        
        # 获取客户的文案统计
        client_id = share_link.client_id
        
        # 待审核文案数量（必须是终审通过且客户审核状态为pending）
        pending_count = Content.query.filter(
            Content.client_id == client_id,
            Content.is_deleted == False,
            Content.workflow_status == 'pending_client_review',
            Content.client_review_status == 'pending'
        ).count()

        # 已通过文案数量（只计算未删除的）
        approved_count = Content.query.filter(
            Content.client_id == client_id,
            Content.is_deleted == False,
            Content.client_review_status == 'approved'
        ).count()

        # 已驳回文案数量（只计算未删除的，但驳回的文章对客户不可见）
        rejected_count = Content.query.filter(
            Content.client_id == client_id,
            Content.is_deleted == False,
            Content.client_review_status == 'rejected'
        ).count()

        # 已审核文案数量（只计算通过的，驳回的文章对客户不可见，所以不算已审核）
        # 注意：这里只计算未删除且已通过的文章
        reviewed_count = approved_count

        # 总文案数量（只计算终审通过的文案，不包括驳回的）
        total_count = Content.query.filter(
            Content.client_id == client_id,
            Content.is_deleted == False,
            (Content.workflow_status == 'pending_client_review') |
            (Content.client_review_status == 'approved')
        ).count()
        
        return {
            'share_link': share_link,
            'pending_count': pending_count,
            'approved_count': approved_count,
            'rejected_count': rejected_count,
            'reviewed_count': reviewed_count,
            'total_count': total_count,
            'is_expired': share_link.expires_at and share_link.expires_at < datetime.now(),
            'days_remaining': (share_link.expires_at - datetime.now()).days if share_link.expires_at else None
        }
