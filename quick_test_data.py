#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速创建测试数据
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db

def create_quick_test_data():
    """快速创建测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 直接执行SQL插入测试数据
            sql_commands = [
                """
                INSERT IGNORE INTO contents (
                    id, title, content, topics, location, client_id, task_id, batch_id, template_id,
                    workflow_status, publish_status, publish_priority, created_by, created_at
                ) VALUES 
                (101, '小红书种草神器推荐', 
                 '最近发现了一个超好用的产品！真的是太惊喜了～\\n\\n用了一段时间，效果真的很棒，强烈推荐给大家！\\n\\n#种草分享 #好物推荐', 
                 '[\"种草分享\", \"好物推荐\", \"生活分享\"]', 
                 '上海·静安区', 
                 1, 1, 1, 1, 
                 'pending_publish', 'unpublished', 'high', 
                 1, NOW())
                """,
                """
                INSERT IGNORE INTO contents (
                    id, title, content, topics, location, client_id, task_id, batch_id, template_id,
                    workflow_status, publish_status, publish_priority, created_by, created_at
                ) VALUES 
                (102, '今日穿搭分享', 
                 '今天的穿搭分享来啦～\\n\\n这套搭配简约又时尚，很适合日常出街！\\n\\n大家觉得怎么样呢？\\n\\n#穿搭分享 #时尚搭配', 
                 '[\"穿搭分享\", \"时尚搭配\", \"日常穿搭\"]', 
                 '北京·朝阳区', 
                 1, 1, 1, 1, 
                 'pending_publish', 'unpublished', 'normal', 
                 1, NOW())
                """,
                """
                INSERT IGNORE INTO contents (
                    id, title, content, topics, location, client_id, task_id, batch_id, template_id,
                    workflow_status, publish_status, publish_priority, created_by, created_at
                ) VALUES 
                (103, '护肤心得分享', 
                 '分享一下最近的护肤心得～\\n\\n坚持使用了一个月，皮肤状态真的有明显改善！\\n\\n姐妹们可以试试看！\\n\\n#护肤分享 #美容心得', 
                 '[\"护肤分享\", \"美容心得\", \"生活分享\"]', 
                 '深圳·南山区', 
                 1, 1, 1, 1, 
                 'pending_publish', 'unpublished', 'high', 
                 1, NOW())
                """
            ]
            
            for sql in sql_commands:
                try:
                    db.session.execute(sql)
                    print(f"执行SQL成功")
                except Exception as e:
                    print(f"执行SQL失败: {e}")
            
            db.session.commit()
            
            # 查询插入的数据
            result = db.session.execute("""
                SELECT 
                    id, title, publish_priority, workflow_status, publish_status, location, created_at
                FROM contents 
                WHERE workflow_status = 'pending_publish' 
                ORDER BY 
                    CASE publish_priority 
                        WHEN 'high' THEN 1 
                        WHEN 'normal' THEN 2 
                        WHEN 'low' THEN 3 
                        ELSE 4 
                    END,
                    created_at
            """)
            
            print("\n插入的测试数据:")
            print("ID\t标题\t\t\t优先级\t状态\t\t位置")
            print("-" * 80)
            for row in result:
                print(f"{row[0]}\t{row[1][:15]}...\t{row[2]}\t{row[3]}\t{row[5]}")
            
            print(f"\n✅ 测试数据创建成功！现在可以测试API了。")
            
        except Exception as e:
            print(f"创建测试数据失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_quick_test_data()
