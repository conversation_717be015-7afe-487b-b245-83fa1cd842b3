#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试筛选条件保持功能
"""

import requests
from urllib.parse import urlparse, parse_qs

def test_filter_persistence():
    """测试筛选条件保持功能"""
    print("🔄 测试筛选条件保持功能...")
    print("=" * 60)
    
    try:
        # 测试基础页面
        print("1. 测试基础页面加载...")
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 基础页面加载失败")
            return
        
        # 测试带客户筛选的页面
        print("\n2. 测试带客户筛选的页面...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?client_id=1'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查状态按钮是否保持客户筛选
            if 'client_id=1' in response.text:
                print("✅ 状态按钮保持客户筛选条件")
            else:
                print("❌ 状态按钮未保持客户筛选条件")
        
        # 测试带优先级筛选的页面
        print("\n3. 测试带优先级筛选的页面...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?priority=high'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查状态按钮是否保持优先级筛选
            if 'priority=high' in response.text:
                print("✅ 状态按钮保持优先级筛选条件")
            else:
                print("❌ 状态按钮未保持优先级筛选条件")
        
        # 测试组合筛选的页面
        print("\n4. 测试组合筛选的页面...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?client_id=1&priority=high'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查状态按钮是否保持所有筛选条件
            if 'client_id=1' in response.text and 'priority=high' in response.text:
                print("✅ 状态按钮保持所有筛选条件")
            else:
                print("❌ 状态按钮未保持所有筛选条件")
        
        # 测试状态+筛选组合
        print("\n5. 测试状态+筛选组合...")
        test_url = 'http://127.0.0.1:5000/simple/publish-status-manage?status=pending&client_id=1&priority=high'
        response = requests.get(test_url, timeout=10)
        print(f"   页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查其他状态按钮是否保持筛选条件
            if 'status=published' in response.text and 'client_id=1' in response.text and 'priority=high' in response.text:
                print("✅ 其他状态按钮保持筛选条件")
            else:
                print("❌ 其他状态按钮未保持筛选条件")
        
        print("\n6. 检查筛选功能组件...")
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        if response.status_code == 200:
            # 检查筛选下拉框
            if 'client-filter' in response.text:
                print("✅ 客户筛选下拉框存在")
            else:
                print("❌ 客户筛选下拉框缺失")
            
            if 'priority-filter' in response.text:
                print("✅ 优先级筛选下拉框存在")
            else:
                print("❌ 优先级筛选下拉框缺失")
            
            if 'applyFilters' in response.text:
                print("✅ 筛选应用函数存在")
            else:
                print("❌ 筛选应用函数缺失")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 筛选条件保持功能测试完成！")
    print("\n修改后的特点：")
    print("1. ✅ 状态切换保持筛选 - 切换状态时保持客户和优先级筛选")
    print("2. ✅ 组合筛选支持 - 状态+客户+优先级可以同时使用")
    print("3. ✅ URL参数传递 - 通过URL参数保持筛选状态")
    print("4. ✅ 用户体验优化 - 不需要重新选择筛选条件")
    print("\n使用场景：")
    print("- 选择客户A，然后在不同状态间切换查看")
    print("- 选择高优先级，然后查看不同状态的高优先级文案")
    print("- 选择客户A+高优先级，然后查看该客户的高优先级文案在各状态的分布")
    print("\n技术实现：")
    print("- 状态按钮链接包含当前的client_id和priority参数")
    print("- 下拉框选择时保持当前的status参数")
    print("- URL参数在页面间传递，保持筛选状态")

if __name__ == '__main__':
    test_filter_persistence()
