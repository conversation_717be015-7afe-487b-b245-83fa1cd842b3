<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试页面</title>
    
    <!-- Bootstrap Icons CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .icon-test {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .icon-test i {
            font-size: 18px;
            width: 24px;
            text-align: center;
            margin-right: 10px;
            color: #495057;
        }
        
        /* 备用字体定义 */
        @font-face {
            font-family: "bootstrap-icons";
            src: url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2") format("woff2"),
                 url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff") format("woff");
        }
        
        /* 菜单专用图标定义 */
        .bi-speedometer2::before { content: "\f58c"; }
        .bi-layer-group::before { content: "\f46a"; }
        .bi-people::before { content: "\f4dc"; }
        .bi-pencil-square::before { content: "\f4d0"; }
        .bi-clipboard-check::before { content: "\f28f"; }
        .bi-image::before { content: "\f3f5"; }
        .bi-check2-square::before { content: "\f26f"; }
        .bi-person-check::before { content: "\f4d6"; }
        .bi-send::before { content: "\f52c"; }
        .bi-list-check::before { content: "\f477"; }
        .bi-person-gear::before { content: "\f4d8"; }
        .bi-gear::before { content: "\f3e2"; }
        .bi-box-arrow-right::before { content: "\f1c4"; }
        
        /* 确保图标正确显示 */
        [class*="bi-"]::before {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            display: inline-block !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>🧪 菜单图标测试</h2>
        <p>这个页面用于测试所有菜单图标是否能正确显示</p>
    </div>
    
    <div class="test-container">
        <h3>📋 菜单图标列表</h3>
        
        <div class="icon-test">
            <i class="bi bi-speedometer2"></i>
            <span>控制台 (bi-speedometer2)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-layer-group"></i>
            <span>模板管理 (bi-layer-group)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-people"></i>
            <span>客户管理 (bi-people)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-pencil-square"></i>
            <span>内容生成 (bi-pencil-square)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-clipboard-check"></i>
            <span>初审文案 (bi-clipboard-check)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-image"></i>
            <span>图片上传 (bi-image)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-check2-square"></i>
            <span>最终审核 (bi-check2-square)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-person-check"></i>
            <span>客户审核 (bi-person-check)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-send"></i>
            <span>发布管理 (bi-send)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-list-check"></i>
            <span>发布状态 (bi-list-check)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-person-gear"></i>
            <span>用户管理 (bi-person-gear)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-gear"></i>
            <span>系统设置 (bi-gear)</span>
        </div>
        
        <div class="icon-test">
            <i class="bi bi-box-arrow-right"></i>
            <span>退出登录 (bi-box-arrow-right)</span>
        </div>
    </div>
    
    <div class="test-container">
        <h3>🔍 诊断信息</h3>
        <div id="diagnostic-info">
            <p>正在检查图标加载状态...</p>
        </div>
    </div>
    
    <script>
        // 检查图标是否正确加载
        window.addEventListener('load', function() {
            const diagnosticDiv = document.getElementById('diagnostic-info');
            let info = '<h4>图标加载诊断：</h4>';
            
            // 检查字体是否加载
            if (document.fonts) {
                document.fonts.ready.then(function() {
                    const bootstrapIconsFont = Array.from(document.fonts).find(font => 
                        font.family.includes('bootstrap-icons')
                    );
                    
                    if (bootstrapIconsFont) {
                        info += '<p>✅ Bootstrap Icons 字体已加载</p>';
                    } else {
                        info += '<p>❌ Bootstrap Icons 字体未找到</p>';
                    }
                    
                    diagnosticDiv.innerHTML = info;
                });
            }
            
            // 检查CSS样式
            const testIcon = document.querySelector('.bi-person-gear');
            if (testIcon) {
                const computedStyle = window.getComputedStyle(testIcon, '::before');
                const content = computedStyle.getPropertyValue('content');
                
                if (content && content !== 'none' && content !== '""') {
                    info += '<p>✅ 图标样式已应用</p>';
                } else {
                    info += '<p>❌ 图标样式未应用</p>';
                }
            }
            
            info += '<p><strong>如果图标不显示，请检查：</strong></p>';
            info += '<ul>';
            info += '<li>网络连接是否正常</li>';
            info += '<li>CDN是否可访问</li>';
            info += '<li>浏览器是否支持woff2字体</li>';
            info += '<li>是否有广告拦截器阻止了字体加载</li>';
            info += '</ul>';
            
            diagnosticDiv.innerHTML = info;
        });
    </script>
</body>
</html>
