<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ content.title or '文案详情' }} - {{ client.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .content-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .content-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 15px 15px 0 0;
        }
        
        .content-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .content-body {
            padding: 2rem;
        }
        
        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            white-space: pre-wrap;
            word-wrap: break-word;
            margin-bottom: 2rem;
        }
        
        .images-section {
            margin-bottom: 2rem;
        }
        
        .images-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .image-item {
            aspect-ratio: 1;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-item:hover {
            transform: scale(1.02);
        }
        
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .details-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .details-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1.5rem;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #666;
        }
        
        .detail-value {
            color: #333;
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .action-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            min-width: 150px;
            transition: all 0.2s;
        }
        
        .btn-approve {
            background: #28a745;
            color: white;
        }
        
        .btn-approve:hover {
            background: #218838;
            color: white;
        }
        
        .btn-reject {
            background: #dc3545;
            color: white;
        }
        
        .btn-reject:hover {
            background: #c82333;
            color: white;
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.2s;
        }
        
        .back-btn:hover {
            background: #5a6268;
            color: white;
        }
        
        /* 图片预览模态框 */
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .image-modal img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
        
        .image-modal-close {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">{{ client.name }}</h1>
                    <p class="mb-0 opacity-75">文案详情</p>
                </div>
                <a href="/client-review/{{ share_key }}" class="back-btn">
                    <i class="bi bi-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- 文章内容 -->
                <div class="content-card">
                    <div class="content-header">
                        <h2 class="content-title">{{ content.title or '无标题' }}</h2>
                        <div class="content-meta">
                            <span>创建时间：{{ content.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            {% if content.workflow_status == 'pending_client_review' %}
                                <span class="status-badge status-pending">待审核</span>
                            {% elif content.client_review_status == 'approved' %}
                                <span class="status-badge status-approved">已通过</span>
                            {% elif content.client_review_status == 'rejected' %}
                                <span class="status-badge status-rejected">已驳回</span>
                            {% else %}
                                <span class="status-badge status-pending">待审核</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="content-body">
                        {% if content.content %}
                        <div class="content-text">{{ content.content }}</div>
                        {% endif %}
                        
                        {% set image_list = content.images.all() if content.images else [] %}
                        {% set url_list = content.image_urls_list if content.image_urls_list else [] %}
                        {% if image_list or url_list %}
                        <div class="images-section">
                            <div class="images-title">配图 ({{ (image_list|length) + (url_list|length) }}张)</div>
                            <div class="images-grid">
                                {% for image in image_list %}
                                <div class="image-item" onclick="showImageModal('{{ image.image_path }}')">
                                    <img src="{{ image.image_path }}" alt="文案配图" loading="lazy">
                                </div>
                                {% endfor %}
                                {% for url in url_list %}
                                <div class="image-item" onclick="showImageModal('{{ url }}')">
                                    <img src="{{ url }}" alt="文案配图" loading="lazy">
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- 详细信息 -->
                <div class="details-card">
                    <div class="details-title">详细信息</div>
                    <div class="detail-row">
                        <span class="detail-label">任务名称</span>
                        <span class="detail-value">{{ content.task.name if content.task else '默认任务' }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">创建时间</span>
                        <span class="detail-value">{{ content.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                    </div>
                    {% if content.display_date %}
                    <div class="detail-row">
                        <span class="detail-label">发布日期</span>
                        <span class="detail-value">{{ content.display_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% endif %}
                    {% if content.topics %}
                    <div class="detail-row">
                        <span class="detail-label">话题标签</span>
                        <span class="detail-value">{{ content.topics }}</span>
                    </div>
                    {% endif %}
                    {% if content.location %}
                    <div class="detail-row">
                        <span class="detail-label">位置信息</span>
                        <span class="detail-value">{{ content.location }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 操作按钮 -->
                {% if content.client_review_status == 'pending_client_review' %}
                <div class="action-card">
                    <h5 class="mb-3">审核操作</h5>
                    <div class="action-buttons">
                        <button class="action-btn btn-approve" onclick="approveContent()">
                            <i class="bi bi-check-lg me-2"></i>通过审核
                        </button>
                        <button class="action-btn btn-reject" onclick="showRejectModal()">
                            <i class="bi bi-x-lg me-2"></i>驳回修改
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">驳回理由</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">请选择或输入驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('内容质量不符合要求，需要重新编写')">内容质量不达标</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('标题不够吸引人，建议重新设计')">标题需要优化</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('图片质量需要提升，建议更换高质量图片')">图片质量问题</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('文案风格与品牌调性不符')">风格不符</button>
                        </div>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请输入具体的驳回理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">确认驳回</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="image-modal" id="imageModal" onclick="hideImageModal()">
        <button class="image-modal-close" onclick="hideImageModal()">
            <i class="bi bi-x"></i>
        </button>
        <img id="modalImage" src="" alt="图片预览">
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const shareKey = '{{ share_key }}';
        const contentId = {{ content.id }};

        // 显示图片预览
        function showImageModal(imageUrl) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageUrl;
            modal.style.display = 'flex';

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 隐藏图片预览
        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // 显示驳回模态框
        function showRejectModal() {
            document.getElementById('rejectReason').value = '';
            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 通过审核
        function approveContent() {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent('approve', '');
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            reviewContent('reject', reason);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 审核内容
        function reviewContent(action, comment) {
            // 禁用按钮防止重复提交
            const approveBtn = document.querySelector('.btn-approve');
            const rejectBtn = document.querySelector('.btn-reject');
            if (approveBtn) approveBtn.disabled = true;
            if (rejectBtn) rejectBtn.disabled = true;

            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');

                    // 更新页面状态
                    setTimeout(() => {
                        updatePageStatus(action);
                    }, 1000);
                } else {
                    showToast(data.message, 'error');
                    // 重新启用按钮
                    if (approveBtn) approveBtn.disabled = false;
                    if (rejectBtn) rejectBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                // 重新启用按钮
                if (approveBtn) approveBtn.disabled = false;
                if (rejectBtn) rejectBtn.disabled = false;
            });
        }

        // 更新页面状态
        function updatePageStatus(action) {
            const statusBadge = document.querySelector('.status-badge');
            const actionCard = document.querySelector('.action-card');

            if (action === 'approve') {
                statusBadge.className = 'status-badge status-approved';
                statusBadge.textContent = '已通过';
            } else if (action === 'reject') {
                statusBadge.className = 'status-badge status-rejected';
                statusBadge.textContent = '已驳回';
            }

            // 隐藏操作按钮
            if (actionCard) {
                actionCard.style.display = 'none';
            }
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 阻止图片模态框的事件冒泡
        document.getElementById('modalImage').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideImageModal();
            }
        });
    </script>
</body>
</html>
