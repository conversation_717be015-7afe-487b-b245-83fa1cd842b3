#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试状态更新API
"""

import requests
import json

def test_status_update():
    """测试状态更新API"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🔄 测试状态更新API...")
    print("=" * 50)
    
    # 1. 先获取一篇文案
    print("\n1. 获取待发布文案...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers)
        print(f"获取文案状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                content_id = content.get('id')
                print(f"✅ 获取成功！文案ID: {content_id}")
                print(f"标题: {content.get('title')}")
                print(f"当前状态: {content.get('workflow_status')}")
                
                # 2. 测试状态更新
                print(f"\n2. 测试状态更新 (ID: {content_id})...")
                
                update_data = {
                    'status': 'success',
                    'publish_url': 'https://xiaohongshu.com/test/123',
                    'platform': '小红书',
                    'account': '测试账号',
                    'error_message': '',
                    'ext_info': json.dumps({
                        'test_source': 'status_update_test',
                        'timestamp': '2025-07-28T00:25:00'
                    })
                }
                
                print(f"发送数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
                
                update_response = requests.post(
                    f'{base_url}/content/{content_id}/status',
                    headers=headers,
                    json=update_data
                )
                
                print(f"状态更新状态码: {update_response.status_code}")
                print(f"响应头: {dict(update_response.headers)}")
                
                if update_response.status_code == 200:
                    update_result = update_response.json()
                    print("✅ 状态更新成功！")
                    print(f"响应: {json.dumps(update_result, indent=2, ensure_ascii=False)}")
                else:
                    print("❌ 状态更新失败！")
                    print(f"错误响应: {update_response.text}")
                    
                    # 尝试解析JSON错误信息
                    try:
                        error_data = update_response.json()
                        print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                    except:
                        print("无法解析错误响应为JSON")
                
            else:
                print(f"❌ 获取文案失败: {data.get('error')}")
        else:
            print(f"❌ 获取文案请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 状态更新API测试完成！")

if __name__ == '__main__':
    test_status_update()
