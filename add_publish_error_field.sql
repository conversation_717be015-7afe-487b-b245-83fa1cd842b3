-- 添加publish_error字段到contents表

-- 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'contents' 
AND COLUMN_NAME = 'publish_error';

-- 如果字段不存在，则添加字段
ALTER TABLE contents 
ADD COLUMN publish_error TEXT COMMENT '发布提示信息（成功或失败的详细信息）';

-- 验证字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'contents' 
AND COLUMN_NAME = 'publish_error';
