<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>发布管理 - 小红书文案管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons - 使用最新版本确保图标完整 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 确保Bootstrap Icons正确加载 - 备用字体定义 */
        @font-face {
            font-family: "bootstrap-icons";
            src: url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2") format("woff2"),
                 url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff") format("woff");
        }

        /* 菜单图标样式强化 */
        .sidebar .nav-link i[class*="bi-"] {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            display: inline-block !important;
            width: 1.2em !important;
            text-align: center !important;
        }

        /* 备用方案：如果Bootstrap Icons字体加载失败，使用Unicode符号 */
        .sidebar .nav-link i.bi-speedometer2::before { content: "📊" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-layer-group::before { content: "📋" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-people::before { content: "👥" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-pencil-square::before { content: "✏️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-clipboard-check::before { content: "✅" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-image::before { content: "🖼️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-check2-square::before { content: "✔️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-check::before { content: "👤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-send::before { content: "📤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-list-check::before { content: "📝" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-gear::before { content: "⚙️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-gear::before { content: "🔧" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-box-arrow-right::before { content: "🚪" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .main-content {
            min-height: 100vh;
            padding: 0;
        }

        .top-bar {
            background-color: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin-bottom: 0;
        }

        .breadcrumb {
            margin-bottom: 0;
            background-color: transparent;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧菜单 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>功能菜单</span>
                    </h6>
                    <ul class="nav flex-column">
                        {% if current_user.is_authenticated and current_user.has_permission('dashboard_access') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.dashboard') }}">
                                <i class="bi bi-speedometer2"></i> 控制台
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('template_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.templates') }}">
                                <i class="bi bi-file-text"></i> 模板管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('client_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.clients') }}">
                                <i class="bi bi-people"></i> 客户管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('content_generate') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.content') }}">
                                <i class="bi bi-pencil-square"></i> 内容生成
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('content_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.review_content_page') }}">
                                <i class="bi bi-clipboard-check"></i> 初审文案
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('image_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.image_upload') }}">
                                <i class="bi bi-image"></i> 图片上传
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('review.final') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.final_review') }}">
                                <i class="bi bi-check2-square"></i> 最终审核
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('client_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.client_review') }}">
                                <i class="bi bi-person-check"></i> 客户审核
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('publish.manage') %}
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('main_simple.publish_manage') }}">
                                <i class="bi bi-send"></i> 发布管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.publish_status_manage') }}">
                                <i class="bi bi-list-check"></i> 发布状态
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('user_manage') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('user_management.user_list') }}">
                                <i class="bi bi-person-gear"></i> 用户管理
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.is_authenticated and current_user.has_permission('system_settings') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main_simple.system') }}">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统设置</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <!-- 顶部面包屑 -->
                <div class="top-bar">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main_simple.dashboard') }}">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page">发布管理</li>
                        </ol>
                    </nav>
                </div>

                <!-- 页面内容 -->
                <div class="container mt-3">
                    <h3>发布管理</h3>
                    
                    <!-- 筛选功能 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-funnel"></i> 筛选条件</h6>
                        </div>
                        <div class="card-body">
                            <form id="filterForm" method="GET" action="{{ url_for('main_simple.publish_manage') }}">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="client_id" class="form-label">客户筛选</label>
                                        <select class="form-select" id="client_id" name="client_id">
                                            <option value="">全部客户</option>
                                            {% for client in clients %}
                                            <option value="{{ client.id }}" 
                                                    {% if request.args.get('client_id')|int == client.id %}selected{% endif %}>
                                                {{ client.name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="priority" class="form-label">优先级筛选</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="">全部优先级</option>
                                            <option value="high" {% if request.args.get('priority') == 'high' %}selected{% endif %}>高</option>
                                            <option value="normal" {% if request.args.get('priority') == 'normal' %}selected{% endif %}>普通</option>
                                            <option value="low" {% if request.args.get('priority') == 'low' %}selected{% endif %}>低</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="title" class="form-label">标题搜索</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="{{ request.args.get('title', '') }}" 
                                               placeholder="输入标题关键词">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <div class="d-grid gap-2 w-100">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-search"></i> 筛选
                                            </button>
                                            <a href="{{ url_for('main_simple.publish_manage') }}" class="btn btn-outline-secondary">
                                                <i class="bi bi-arrow-clockwise"></i> 重置
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <button class="btn btn-primary btn-sm" id="batch-submit-btn">批量提交发布</button>
                        <button class="btn btn-danger btn-sm" id="batch-delete-btn">批量删除</button>
                        <button class="btn btn-secondary btn-sm" id="batch-priority-btn">批量设置优先级</button>
                    </div>
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>标题</th>
                                <th>图片</th>
                                <th>客户</th>
                                <th>优先级</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            <tr>
                                <td><input type="checkbox" class="content-checkbox" value="{{ item.content.id }}"></td>
                                <td>{{ item.content.title }}</td>
                                <td>
                                    {% if item.images %}
                                        <span class="badge bg-primary">{{ item.image_count }} 张图片</span>
                                    {% else %}
                                        <span class="text-muted">无图片</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.content.client.name if item.content.client else '未知' }}</td>
                                <td>
                                    {% if item.content.publish_priority == 'high' %}
                                        <span class="badge bg-danger">高</span>
                                    {% elif item.content.publish_priority == 'normal' %}
                                        <span class="badge bg-warning">普通</span>
                                    {% elif item.content.publish_priority == 'low' %}
                                        <span class="badge bg-secondary">低</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">未设置</span>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-primary ms-1" onclick="setPriority({{ item.content.id }})" title="设置优先级">
                                        <i class="bi bi-gear"></i>
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-primary btn-sm" onclick="submitForPublish({{ item.content.id }})">提交发布</button>
                                    <button class="btn btn-danger btn-sm ms-1" onclick="deleteContent({{ item.content.id }})">删除</button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav>
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=pagination.prev_num, title=request.args.get('title', ''), client_id=request.args.get('client_id', ''), priority=request.args.get('priority', '')) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=page_num, title=request.args.get('title', ''), client_id=request.args.get('client_id', ''), priority=request.args.get('priority', '')) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main_simple.publish_manage', page=pagination.next_num, title=request.args.get('title', ''), client_id=request.args.get('client_id', ''), priority=request.args.get('priority', '')) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </main>
        </div>
    </div>

    <!-- 优先级设置模态框 -->
    <div class="modal fade" id="priorityModal" tabindex="-1" aria-labelledby="priorityModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="priorityModalLabel">设置发布优先级</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>请选择发布优先级：</p>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-danger priority-tag" data-priority="high">
                            <span class="badge bg-danger">高</span> 优先发布
                        </button>
                        <button type="button" class="btn btn-outline-warning priority-tag" data-priority="normal">
                            <span class="badge bg-warning">普通</span> 正常发布
                        </button>
                        <button type="button" class="btn btn-outline-secondary priority-tag" data-priority="low">
                            <span class="badge bg-secondary">低</span> 延后发布
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // 当前选中的文案ID和优先级
    let currentContentId = null;
    let currentPriority = null;
    
    // 全选功能
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.content-checkbox');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });

    // 提交发布单个文案
    function submitForPublish(contentId) {
        // 直接提交，不显示确认弹窗
        fetch(`/simple/api/submit-for-publish/${contentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('提交发布成功！文案已进入发布队列。', 'success');
                // 延迟刷新页面，让用户看到Toast提示
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToast('提交发布失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('提交发布失败，请重试', 'error');
        });
    }

    // 批量提交发布
    document.getElementById('batch-submit-btn').addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('请选择要提交发布的文案', 'warning');
            return;
        }

        // 直接提交，不显示确认弹窗
        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        
        fetch('/simple/api/batch-submit-for-publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ content_ids: contentIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`批量提交发布成功！${data.submitted_count} 篇文案已进入发布队列。`, 'success');
                // 延迟刷新页面，让用户看到Toast提示
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToast('批量提交发布失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('批量提交发布失败，请重试', 'error');
        });
    });

    // 批量删除
    document.getElementById('batch-delete-btn').addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('请选择要删除的文案', 'warning');
            return;
        }

        if (!confirm('确定要批量删除选中的文案吗？此操作不可逆。')) {
            return;
        }

        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        
        fetch('/simple/api/publish-manage/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ content_ids: contentIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`批量删除成功！${data.deleted_count} 篇文案已删除。`, 'success');
                // 延迟刷新页面，让用户看到Toast提示
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToast('批量删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('批量删除失败，请重试', 'error');
        });
    });

    // 批量设置优先级
    document.getElementById('batch-priority-btn').addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            showToast('请选择要设置优先级的文案', 'warning');
            return;
        }

        // 显示批量优先级设置模态框
        showBatchPriorityModal(selectedCheckboxes);
    });

    // 显示批量优先级设置模态框
    function showBatchPriorityModal(selectedCheckboxes) {
        // 重置所有标签状态
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
            const priority = tag.dataset.priority;
            if (priority === 'high') {
                tag.classList.add('btn-outline-danger');
            } else if (priority === 'normal') {
                tag.classList.add('btn-outline-warning');
            } else if (priority === 'low') {
                tag.classList.add('btn-outline-secondary');
            }
        });

        // 设置默认选中普通优先级
        const defaultTag = document.querySelector('[data-priority="normal"]');
        if (defaultTag) {
            defaultTag.classList.remove('btn-outline-warning');
            defaultTag.classList.add('btn-warning', 'active');
        }

        // 更新模态框标题
        const modalTitle = document.getElementById('priorityModalLabel');
        modalTitle.textContent = `批量设置优先级 (${selectedCheckboxes.length} 篇文案)`;

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
        modal.show();

        // 移除之前的事件监听器，避免重复绑定
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.replaceWith(tag.cloneNode(true));
        });

        // 重新绑定事件监听器
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                const priority = this.dataset.priority;
                
                // 更新标签样式
                document.querySelectorAll('.priority-tag').forEach(t => {
                    t.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
                    const p = t.dataset.priority;
                    if (p === 'high') {
                        t.classList.add('btn-outline-danger');
                    } else if (p === 'normal') {
                        t.classList.add('btn-outline-warning');
                    } else if (p === 'low') {
                        t.classList.add('btn-outline-secondary');
                    }
                });
                
                this.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
                if (priority === 'high') {
                    this.classList.add('btn-danger');
                } else if (priority === 'normal') {
                    this.classList.add('btn-warning');
                } else if (priority === 'low') {
                    this.classList.add('btn-secondary');
                }
                this.classList.add('active');

                // 批量提交优先级设置
                submitBatchPriority(priority, selectedCheckboxes);
            });
        });
    }

    // 批量提交优先级设置
    function submitBatchPriority(priority, selectedCheckboxes) {
        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        
        fetch('/simple/api/publish/batch-priority', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ 
                content_ids: contentIds,
                priority: priority 
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`批量设置优先级成功！${data.updated_count} 篇文案已更新。`, 'success');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
                modal.hide();
                // 延迟刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToast('批量设置优先级失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('批量设置优先级失败，请重试', 'error');
        });
    }

    // 显示单个优先级设置模态框
    function setPriority(contentId) {
        currentContentId = contentId;
        
        // 获取当前文案的优先级
        const row = document.querySelector(`input[value="${contentId}"]`).closest('tr');
        const priorityBadge = row.querySelector('td:nth-child(5) .badge');
        if (priorityBadge.textContent.includes('高')) {
            currentPriority = 'high';
        } else if (priorityBadge.textContent.includes('普通')) {
            currentPriority = 'normal';
        } else if (priorityBadge.textContent.includes('低')) {
            currentPriority = 'low';
        } else {
            currentPriority = 'normal'; // 默认普通
        }
        
        // 重置所有标签状态
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
            const priority = tag.dataset.priority;
            if (priority === 'high') {
                tag.classList.add('btn-outline-danger');
            } else if (priority === 'normal') {
                tag.classList.add('btn-outline-warning');
            } else if (priority === 'low') {
                tag.classList.add('btn-outline-secondary');
            }
        });
        
        // 设置当前优先级为选中状态
        const currentTag = document.querySelector(`[data-priority="${currentPriority}"]`);
        if (currentTag) {
            currentTag.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
            if (currentPriority === 'high') {
                currentTag.classList.add('btn-danger');
            } else if (currentPriority === 'normal') {
                currentTag.classList.add('btn-warning');
            } else if (currentPriority === 'low') {
                currentTag.classList.add('btn-secondary');
            }
            currentTag.classList.add('active');
        }
        
        // 更新模态框标题
        const modalTitle = document.getElementById('priorityModalLabel');
        modalTitle.textContent = '设置发布优先级';
        
        const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
        modal.show();
        
        // 移除之前的事件监听器，避免重复绑定
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.replaceWith(tag.cloneNode(true));
        });
        
        // 重新绑定事件监听器
        document.querySelectorAll('.priority-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                const priority = this.dataset.priority;
                
                // 更新标签样式
                document.querySelectorAll('.priority-tag').forEach(t => {
                    t.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
                    const p = t.dataset.priority;
                    if (p === 'high') {
                        t.classList.add('btn-outline-danger');
                    } else if (p === 'normal') {
                        t.classList.add('btn-outline-warning');
                    } else if (p === 'low') {
                        t.classList.add('btn-outline-secondary');
                    }
                });
                
                this.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
                if (priority === 'high') {
                    this.classList.add('btn-danger');
                } else if (priority === 'normal') {
                    this.classList.add('btn-warning');
                } else if (priority === 'low') {
                    this.classList.add('btn-secondary');
                }
                this.classList.add('active');
                
                // 提交优先级设置
                submitPriority(priority);
            });
        });
    }

    // 提交单个优先级设置
    function submitPriority(priority) {
        if (!currentContentId) {
            return;
        }
        
        fetch(`/simple/api/publish/${currentContentId}/priority`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ priority: priority })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('优先级设置成功！', 'success');
                // 直接更新页面上的优先级显示，不刷新页面
                updatePriorityDisplay(currentContentId, priority);
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
                modal.hide();
            } else {
                showToast('设置失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('设置失败，请重试', 'error');
        });
    }

    // 更新页面上的优先级显示
    function updatePriorityDisplay(contentId, priority) {
        const row = document.querySelector(`input[value="${contentId}"]`).closest('tr');
        const priorityCell = row.querySelector('td:nth-child(5)'); // 优先级列
        
        let badgeHtml = '';
        if (priority === 'high') {
            badgeHtml = '<span class="badge bg-danger">高</span>';
        } else if (priority === 'normal') {
            badgeHtml = '<span class="badge bg-warning">普通</span>';
        } else if (priority === 'low') {
            badgeHtml = '<span class="badge bg-secondary">低</span>';
        } else {
            badgeHtml = '<span class="badge bg-light text-dark">未设置</span>';
        }
        
        // 保持设置按钮
        const settingButton = priorityCell.querySelector('button');
        priorityCell.innerHTML = badgeHtml + settingButton.outerHTML;
    }

    // 显示Toast提示
    function showToast(message, type = 'info') {
        // 创建Toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // 创建Toast元素
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : type === 'warning' ? 'bg-warning' : 'bg-primary';
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示Toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
        toast.show();

        // 自动清理
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }

    // 删除单个文案
    function deleteContent(contentId) {
        if (!confirm('确定要删除此文案吗？此操作不可逆。')) {
            return;
        }

        fetch(`/simple/api/publish-manage/delete/${contentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('文案删除成功！', 'success');
                // 延迟刷新页面，让用户看到Toast提示
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToast('文案删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('文案删除失败，请重试', 'error');
        });
    }

    // 筛选功能增强
    document.addEventListener('DOMContentLoaded', function() {
        // 标题搜索框回车提交
        document.getElementById('title').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('filterForm').submit();
            }
        });

        // 客户选择框变化时自动提交
        document.getElementById('client_id').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });

        // 优先级选择框变化时自动提交
        document.getElementById('priority').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
    </script>
</body>
</html>
