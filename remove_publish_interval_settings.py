#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
删除系统设置中的默认发布间隔设置
这些设置在客户添加/编辑页面已经可以直接设置，系统设置中的默认值是冗余的
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from app.models.system_setting import SystemSetting

def remove_publish_interval_settings():
    """删除系统设置中的默认发布间隔设置"""
    print("🗑️ 删除系统设置中的默认发布间隔设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 查找要删除的设置
            print("1. 查找要删除的设置:")
            print("-" * 40)
            
            settings_to_delete = [
                'default_publish_interval_min',
                'default_publish_interval_max'
            ]
            
            found_settings = []
            for key in settings_to_delete:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    found_settings.append(setting)
                    print(f"🔍 找到设置: {setting.key}")
                    print(f"  当前值: {setting.value}")
                    print(f"  描述: {setting.description}")
                    print(f"  ID: {setting.id}")
                    print()
            
            if not found_settings:
                print("✅ 未找到需要删除的设置，无需操作")
                return
            
            # 2. 确认删除原因
            print(f"\n2. 删除原因:")
            print("-" * 40)
            print("删除默认发布间隔设置的原因:")
            print("  ✅ 每个客户都有自己的间隔设置，在客户添加/编辑页面直接设置")
            print("  ✅ 实际使用的值与默认值不同（客户使用10-30分钟，默认值是30-120分钟）")
            print("  ✅ 简化系统设置页面，减少冗余选项")
            print("  ✅ 提高用户体验，设置更直观")
            
            # 3. 检查代码影响
            print(f"\n3. 代码影响分析:")
            print("-" * 40)
            print("删除这些设置后的影响:")
            print("  ✅ 客户创建时会使用表单中的默认值（10分钟和30分钟）")
            print("  ✅ 不会影响现有客户的设置")
            print("  ✅ 系统设置页面更加简洁")
            print("  ✅ 用户不会困惑于多处设置")
            
            # 4. 执行删除操作
            print(f"\n4. 执行删除操作:")
            print("-" * 40)
            
            deleted_count = 0
            for setting in found_settings:
                try:
                    print(f"🗑️ 删除设置: {setting.key} (ID: {setting.id})")
                    db.session.delete(setting)
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除 {setting.key} 失败: {e}")
            
            # 提交更改
            if deleted_count > 0:
                db.session.commit()
                print(f"\n✅ 成功删除 {deleted_count} 个默认发布间隔设置")
            else:
                print(f"\n⚠️ 没有设置被删除")
            
            # 5. 验证删除结果
            print(f"\n5. 验证删除结果:")
            print("-" * 40)
            
            for key in settings_to_delete:
                setting = SystemSetting.query.filter_by(key=key).first()
                if setting:
                    print(f"❌ {key} 仍然存在，删除失败")
                else:
                    print(f"✅ {key} 已成功删除")
            
            # 6. 更新系统默认值
            print(f"\n6. 更新系统默认值:")
            print("-" * 40)
            
            # 检查重置系统设置的代码中是否包含这些设置
            from app.views.main_simple import reset_system_settings
            
            print("注意: 需要检查系统重置函数中是否包含这些设置")
            print("如果包含，建议手动修改 app/views/main_simple.py 文件")
            print("删除 reset_system_settings 函数中的相关设置")
            
            # 7. 使用指南
            print(f"\n7. 使用指南:")
            print("-" * 40)
            print("删除这些设置后，用户应该:")
            print("  1. 🔧 在客户添加/编辑页面直接设置发布间隔")
            print("  2. 📋 根据客户需求选择合适的间隔时间")
            print("  3. 🎯 不再依赖系统默认值")
            
        except Exception as e:
            print(f"❌ 删除过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 默认发布间隔设置删除完成！")
    print("\n总结:")
    print("1. 🗑️ 删除了冗余的默认发布间隔设置")
    print("2. 🎯 客户添加/编辑页面已经包含了间隔设置")
    print("3. 🧹 系统设置页面更加简洁")
    print("4. 👍 用户体验得到优化")
    print("\n现在系统设置页面不再显示默认发布间隔选项！")

if __name__ == '__main__':
    remove_publish_interval_settings()
