#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试API脚本
"""

import requests
import json

def test_api():
    """测试API"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("🚀 开始测试API...")
    
    # 测试获取文案
    print("\n1. 测试获取待发布文案...")
    try:
        response = requests.get(f'{base_url}/content', headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                content = data.get('content')
                print("✅ 获取成功！")
                print(f"文案ID: {content.get('id')}")
                print(f"标题: {content.get('title')}")
                print(f"优先级: {content.get('priority_display', content.get('priority'))}")
                print(f"内容: {content.get('content')[:50]}...")
                print(f"话题: {content.get('topics')}")
                print(f"位置: {content.get('location')}")
                
                # 测试更新状态
                content_id = content.get('id')
                if content_id:
                    print(f"\n2. 测试更新发布状态 (ID: {content_id})...")
                    update_data = {
                        'status': 'success',
                        'publish_url': 'https://xiaohongshu.com/test/123',
                        'platform': '小红书',
                        'account': '测试账号'
                    }
                    
                    update_response = requests.post(
                        f'{base_url}/content/{content_id}/status',
                        headers=headers,
                        json=update_data
                    )
                    
                    print(f"状态码: {update_response.status_code}")
                    if update_response.status_code == 200:
                        update_result = update_response.json()
                        if update_result.get('success'):
                            print("✅ 状态更新成功！")
                        else:
                            print(f"❌ 状态更新失败: {update_result.get('error')}")
                    else:
                        print(f"❌ 状态更新失败: {update_response.text}")
            else:
                print(f"❌ 获取失败: {data.get('error')}")
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_api()
