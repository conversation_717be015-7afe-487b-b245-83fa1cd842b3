#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试提示信息显示
"""

import requests
import json

def test_message_display():
    """测试提示信息显示"""
    api_key = 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
    base_url = 'http://127.0.0.1:5000/api/v1'
    
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }
    
    print("💬 测试提示信息显示...")
    print("=" * 60)
    
    # 测试不同类型的提示信息
    test_cases = [
        {
            'name': '详细成功信息',
            'data': {
                'status': 'success',
                'publish_url': 'https://xiaohongshu.com/discovery/12345',
                'platform': '小红书',
                'account': '美食博主@小红',
                'message': '🎉 发布成功！获得了2.5K点赞，800评论，转发率达到12.3%，预计曝光量50万+'
            }
        },
        {
            'name': '简单成功信息',
            'data': {
                'status': 'success',
                'publish_url': 'https://xiaohongshu.com/discovery/67890',
                'platform': '小红书',
                'account': '测试账号',
                'message': '发布成功'
            }
        },
        {
            'name': '详细失败信息',
            'data': {
                'status': 'failed',
                'platform': '小红书',
                'account': '测试账号',
                'message': '❌ 发布失败：内容包含违规词汇"限时特价"，建议修改为"限时优惠"后重新发布'
            }
        },
        {
            'name': '网络错误信息',
            'data': {
                'status': 'failed',
                'platform': '小红书',
                'account': '测试账号',
                'message': '网络连接超时，请检查网络设置后重试'
            }
        },
        {
            'name': '无提示信息（空消息）',
            'data': {
                'status': 'success',
                'publish_url': 'https://xiaohongshu.com/discovery/empty',
                'platform': '小红书',
                'account': '测试账号'
                # 故意不包含message字段
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}...")
        
        try:
            # 先获取一篇文案
            response = requests.get(f'{base_url}/content', headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    content = data.get('content')
                    content_id = content.get('id')
                    
                    print(f"   获取文案ID: {content_id}")
                    print(f"   标题: {content.get('title')[:30]}...")
                    
                    # 发送测试数据
                    message = test_case['data'].get('message', '')
                    print(f"   发送提示: {message if message else '(无提示信息)'}")
                    
                    update_response = requests.post(
                        f'{base_url}/content/{content_id}/status',
                        headers=headers,
                        json=test_case['data'],
                        timeout=10
                    )
                    
                    if update_response.status_code == 200:
                        result = update_response.json()
                        if result.get('success'):
                            print(f"   ✅ 状态更新成功！")
                            print(f"   预期页面显示: 时间戳 + {message if message else '(空白)'}")
                        else:
                            print(f"   ❌ 状态更新失败: {result.get('error')}")
                    else:
                        print(f"   ❌ 请求失败: {update_response.status_code}")
                else:
                    print(f"   ❌ 获取文案失败: {data.get('error')}")
                    break
            else:
                print(f"   ❌ 获取文案请求失败: {response.status_code}")
                break
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 提示信息测试完成！")
    print("\n请查看以下页面验证显示效果：")
    print("📋 已发布: http://127.0.0.1:5000/simple/publish-status-manage?status=published")
    print("📋 发布失败: http://127.0.0.1:5000/simple/publish-status-manage?status=failed")
    print("\n预期显示效果：")
    print("✅ 有提示信息: 时间戳 + 具体提示内容")
    print("✅ 无提示信息: 只显示时间戳")
    print("✅ 不再显示固定的'发布成功'/'发布失败'文字")
    print("✅ 状态通过彩色标签区分")

if __name__ == '__main__':
    test_message_display()
