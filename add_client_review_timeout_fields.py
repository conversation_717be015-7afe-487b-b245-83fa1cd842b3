#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为客户表添加审核超时相关字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
from sqlalchemy import text

def add_client_review_timeout_fields():
    """为客户表添加审核超时相关字段"""
    print("🔧 为客户表添加审核超时相关字段...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查字段是否已存在
            print("1. 检查现有字段:")
            print("-" * 40)
            
            # 获取当前表结构
            result = db.session.execute(text("DESCRIBE clients"))
            existing_columns = [row[0] for row in result.fetchall()]
            print(f"现有字段: {existing_columns}")
            
            # 需要添加的字段
            new_fields = [
                {
                    'name': 'review_timeout_hours',
                    'sql': 'ADD COLUMN review_timeout_hours INT DEFAULT 24 COMMENT "审核超时小时数"',
                    'description': '审核超时小时数（默认24小时）'
                },
                {
                    'name': 'review_deadline_time',
                    'sql': 'ADD COLUMN review_deadline_time TIME DEFAULT "20:00:00" COMMENT "每日审核截止时间"',
                    'description': '每日审核截止时间（默认20:00）'
                },
                {
                    'name': 'auto_approve_enabled',
                    'sql': 'ADD COLUMN auto_approve_enabled TINYINT(1) DEFAULT 1 COMMENT "是否启用自动通过"',
                    'description': '是否启用自动通过（默认启用）'
                }
            ]
            
            # 添加字段
            print(f"\n2. 添加新字段:")
            print("-" * 40)
            
            for field in new_fields:
                if field['name'] not in existing_columns:
                    try:
                        sql = f"ALTER TABLE clients {field['sql']}"
                        db.session.execute(text(sql))
                        print(f"✅ 添加字段: {field['name']} - {field['description']}")
                    except Exception as e:
                        print(f"❌ 添加字段 {field['name']} 失败: {e}")
                else:
                    print(f"⚪ 字段 {field['name']} 已存在，跳过")
            
            # 提交更改
            db.session.commit()
            print(f"\n✅ 数据库结构更新完成")
            
            # 验证字段添加结果
            print(f"\n3. 验证字段添加结果:")
            print("-" * 40)
            
            result = db.session.execute(text("DESCRIBE clients"))
            all_columns = result.fetchall()
            
            for field in new_fields:
                field_info = next((col for col in all_columns if col[0] == field['name']), None)
                if field_info:
                    print(f"✅ {field['name']}: {field_info[1]} {field_info[2]} {field_info[4]}")
                else:
                    print(f"❌ {field['name']}: 未找到")
            
            # 显示完整的表结构
            print(f"\n4. 完整的客户表结构:")
            print("-" * 40)
            
            for col in all_columns:
                print(f"  {col[0]}: {col[1]} {col[2]} {col[4]} {col[5] or ''}")
            
        except Exception as e:
            print(f"❌ 添加字段过程中发生错误: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 客户表字段扩展完成！")
    print("\n新增字段说明:")
    print("1. ⏰ review_timeout_hours: 审核超时小时数")
    print("   - 从文案进入客户审核状态开始计算")
    print("   - 超过此时间自动通过审核")
    print("   - 默认值：24小时")
    print()
    print("2. 🕐 review_deadline_time: 每日审核截止时间")
    print("   - 每天到达此时间自动通过所有待审核文案")
    print("   - 避免发布时间过晚")
    print("   - 默认值：20:00")
    print()
    print("3. 🔘 auto_approve_enabled: 是否启用自动通过")
    print("   - 控制是否启用自动审核通过功能")
    print("   - 可以单独为某个客户关闭此功能")
    print("   - 默认值：启用")

if __name__ == '__main__':
    add_client_review_timeout_fields()
