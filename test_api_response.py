#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试API返回的统计数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
import requests
import json

def test_api_response():
    """测试API返回的统计数据"""
    print("🧪 测试API返回的统计数据...")
    print("=" * 60)
    
    app = create_app()
    
    with app.test_client() as client:
        try:
            # 1. 测试统计API
            print("1. 测试统计API:")
            print("-" * 40)
            
            share_key = '4dbc790d5015faeca985cb74da6f43fb'
            access_key = 'DWYR'
            
            # 调用统计API
            response = client.get(f'/client-review/api/{share_key}/stats?key={access_key}')
            
            print(f"📡 API请求: GET /client-review/api/{share_key}/stats?key={access_key}")
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"📋 响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                if data.get('success'):
                    stats = data.get('stats', {})
                    print(f"\n✅ 统计数据解析:")
                    print(f"  - total_count: {stats.get('total_count', 'N/A')}")
                    print(f"  - pending_count: {stats.get('pending_count', 'N/A')}")
                    print(f"  - approved_count: {stats.get('approved_count', 'N/A')}")
                    print(f"  - reviewed_count: {stats.get('reviewed_count', 'N/A')}")
                    print(f"  - published_count: {stats.get('published_count', 'N/A')}")
                    print(f"  - rejected_count: {stats.get('rejected_count', 'N/A')}")
                    
                    # 检查是否包含published_count
                    if 'published_count' in stats:
                        print(f"  ✅ published_count 字段存在: {stats['published_count']}")
                    else:
                        print(f"  ❌ published_count 字段缺失")
                        print(f"  📋 可用字段: {list(stats.keys())}")
                else:
                    print(f"❌ API返回失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"📋 响应内容: {response.get_data(as_text=True)}")
            
            # 2. 模拟前端JavaScript处理
            print(f"\n2. 模拟前端JavaScript处理:")
            print("-" * 40)
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    stats = data.get('stats', {})
                    
                    print("📱 前端JavaScript会这样处理:")
                    print("```javascript")
                    print("fetch(buildApiUrl('/stats'))")
                    print("  .then(response => response.json())")
                    print("  .then(data => {")
                    print("    if (data.success) {")
                    print("      const stats = data.stats;")
                    print(f"      document.getElementById('totalCount').textContent = {stats.get('total_count', 'undefined')};")
                    print(f"      document.getElementById('pendingCount').textContent = {stats.get('pending_count', 'undefined')};")
                    print(f"      document.getElementById('reviewedCount').textContent = {stats.get('reviewed_count', 'undefined')};")
                    print(f"      document.getElementById('publishedCount').textContent = {stats.get('published_count', 'undefined')};")
                    print("    }")
                    print("  });")
                    print("```")
                    
                    print(f"\n📊 预期的DOM更新:")
                    print(f"  - #totalCount: '{stats.get('total_count', 'undefined')}'")
                    print(f"  - #pendingCount: '{stats.get('pending_count', 'undefined')}'")
                    print(f"  - #reviewedCount: '{stats.get('reviewed_count', 'undefined')}'")
                    print(f"  - #publishedCount: '{stats.get('published_count', 'undefined')}'")
            
            # 3. 检查可能的问题
            print(f"\n3. 检查可能的问题:")
            print("-" * 40)
            
            print("🔍 可能的问题:")
            print("  1. 浏览器缓存：前端可能使用了旧的JavaScript代码")
            print("  2. API缓存：API响应可能被缓存")
            print("  3. JavaScript错误：前端可能有JavaScript错误阻止更新")
            print("  4. DOM元素问题：publishedCount元素可能不存在")
            
            print(f"\n🔧 调试建议:")
            print("  1. 清除浏览器缓存并强制刷新 (Ctrl+Shift+R)")
            print("  2. 打开浏览器开发者工具 (F12)")
            print("  3. 查看Console标签是否有JavaScript错误")
            print("  4. 查看Network标签确认API请求和响应")
            print("  5. 在Console中执行: document.getElementById('publishedCount')")
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 API响应测试完成！")

if __name__ == '__main__':
    test_api_response()
