#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整清理数据库记录性数据
处理外键约束问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app import db
import shutil

def clean_database_complete():
    """完整清理数据库记录性数据"""
    print("🧹 开始完整清理数据库记录性数据...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 清理图片文件
            print("1. 清理图片文件:")
            print("-" * 40)
            
            upload_dirs = [
                'app/static/uploads',
                'static/uploads',
                'uploads'
            ]
            
            for upload_dir in upload_dirs:
                if os.path.exists(upload_dir):
                    try:
                        for root, dirs, files in os.walk(upload_dir):
                            for file in files:
                                file_path = os.path.join(root, file)
                                os.remove(file_path)
                        print(f"✅ 清理目录: {upload_dir}")
                    except Exception as e:
                        print(f"❌ 清理目录失败 {upload_dir}: {e}")
                else:
                    print(f"⚠️ 目录不存在: {upload_dir}")
            
            # 2. 禁用外键检查
            print(f"\n2. 禁用外键检查:")
            print("-" * 40)
            from sqlalchemy import text
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            print("✅ 已禁用外键检查")
            
            # 3. 清理数据库表（按正确顺序）
            print(f"\n3. 清理数据库表:")
            print("-" * 40)
            
            # 需要清理的表（不考虑外键约束）
            tables_to_clean = [
                ('content_images', '文案图片'),
                ('content_history', '文案历史'),
                ('rejection_reasons', '驳回原因'),
                ('publish_records', '发布记录'),
                ('publish_timeouts', '发布超时'),
                ('display_schedules', '显示计划'),
                ('contents', '文案内容'),
                ('client_shares', '客户分享（旧版）'),
                ('client_share_links', '客户分享链接'),
                ('batches', '批次'),
                ('tasks', '任务'),
                ('topic_relations', '话题关系'),
            ]
            
            # 执行清理
            total_deleted = 0
            for table_name, description in tables_to_clean:
                try:
                    # 检查表是否存在
                    result = db.session.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                    if result.fetchone():
                        # 获取记录数
                        count_result = db.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = count_result.fetchone()[0]
                        
                        if count > 0:
                            # 删除数据
                            db.session.execute(text(f"DELETE FROM {table_name}"))
                            # 重置自增ID
                            db.session.execute(text(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1"))
                            print(f"✅ 清理 {description} ({table_name}): {count} 条记录")
                            total_deleted += count
                        else:
                            print(f"⚪ {description} ({table_name}): 无数据")
                    else:
                        print(f"⚠️ 表不存在: {table_name}")
                        
                except Exception as e:
                    print(f"❌ 清理 {description} ({table_name}) 失败: {e}")
            
            # 4. 重新启用外键检查
            print(f"\n4. 重新启用外键检查:")
            print("-" * 40)
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            print("✅ 已重新启用外键检查")
            
            # 提交事务
            db.session.commit()
            print(f"\n✅ 数据库清理完成，共删除 {total_deleted} 条记录")
            
            # 5. 保留的数据统计
            print(f"\n5. 保留的数据统计:")
            print("-" * 40)
            
            preserved_tables = [
                ('users', '用户'),
                ('roles', '角色'),
                ('permissions', '权限'),
                ('clients', '客户'),
                ('template_categories', '模板分类'),
                ('templates', '模板'),
                ('topics', '话题'),
                ('quick_reasons', '快捷驳回原因'),
                ('display_settings', '显示设置'),
                ('system_settings', '系统设置'),
            ]
            
            for table_name, description in preserved_tables:
                try:
                    result = db.session.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                    if result.fetchone():
                        count_result = db.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = count_result.fetchone()[0]
                        print(f"📊 {description} ({table_name}): {count} 条记录")
                    else:
                        print(f"⚠️ 表不存在: {table_name}")
                except Exception as e:
                    print(f"❌ 统计 {description} ({table_name}) 失败: {e}")
            
            # 6. 清理总结
            print(f"\n6. 清理总结:")
            print("-" * 40)
            print("✅ 已清理的内容:")
            print("  🗑️ 所有文案内容和历史记录")
            print("  🗑️ 所有图片文件和数据库记录")
            print("  🗑️ 所有分享链接")
            print("  🗑️ 所有任务和批次")
            print("  🗑️ 所有发布记录")
            print("  🗑️ 所有显示计划")
            print("  🗑️ 话题关系（但保留话题本身）")
            
            print("\n✅ 保留的内容:")
            print("  📊 用户账户和权限")
            print("  📊 客户信息")
            print("  📊 模板和模板分类")
            print("  📊 话题库")
            print("  📊 系统设置")
            print("  📊 快捷驳回原因")
            print("  📊 显示设置")
            
        except Exception as e:
            print(f"❌ 清理过程中发生错误: {e}")
            # 确保重新启用外键检查
            try:
                db.session.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            except:
                pass
            db.session.rollback()
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 数据库完整清理完成！")
    print("\n清理效果:")
    print("1. ✅ 数据库大小显著减小")
    print("2. ✅ 系统恢复到初始状态")
    print("3. ✅ 保留了核心配置数据")
    print("4. ✅ 可以重新开始内容创建")
    print("\n下一步操作:")
    print("- 🔄 重新创建任务和批次")
    print("- 📝 重新生成文案内容")
    print("- 🖼️ 重新上传图片")
    print("- 🔗 重新生成分享链接")

if __name__ == '__main__':
    # 确认操作
    print("⚠️ 警告：此操作将清空所有记录性数据！")
    print("保留：客户、模板、设置")
    print("清空：文案、图片、分享链接、任务等")
    
    confirm = input("\n确认执行完整清理操作？(输入 'YES' 确认): ")
    if confirm == 'YES':
        clean_database_complete()
    else:
        print("❌ 操作已取消")
