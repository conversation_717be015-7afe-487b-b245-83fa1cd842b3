#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试简化后的批量优先级设置功能
"""

import requests

def test_simplified_batch_priority():
    """测试简化后的批量优先级设置功能"""
    print("🎯 测试简化后的批量优先级设置功能...")
    print("=" * 60)
    
    try:
        # 测试页面是否正常加载
        response = requests.get('http://127.0.0.1:5000/simple/publish-status-manage', timeout=10)
        
        print(f"页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查批量设置优先级按钮
            if '批量设置优先级' in response.text:
                print("✅ 找到批量设置优先级按钮")
            else:
                print("❌ 未找到批量设置优先级按钮")
            
            # 检查按钮样式是否与其他批量按钮一致
            if 'btn-warning btn-sm' in response.text and 'batch-set-priority-btn' in response.text:
                print("✅ 批量按钮样式一致")
            else:
                print("❌ 批量按钮样式不一致")
            
            # 检查复选框功能
            if 'content-checkbox' in response.text:
                print("✅ 找到文案选择复选框")
            else:
                print("❌ 未找到文案选择复选框")
            
            # 检查全选功能
            if 'select-all' in response.text:
                print("✅ 找到全选功能")
            else:
                print("❌ 未找到全选功能")
            
            # 检查getSelectedContentIds函数
            if 'getSelectedContentIds' in response.text:
                print("✅ 找到获取选中ID函数")
            else:
                print("❌ 未找到获取选中ID函数")
            
            # 检查批量优先级弹窗
            if 'batchPriorityModal' in response.text:
                print("✅ 找到批量优先级弹窗")
            else:
                print("❌ 未找到批量优先级弹窗")
            
            # 检查showBatchPriorityModal函数
            if 'showBatchPriorityModal' in response.text:
                print("✅ 找到弹窗显示函数")
            else:
                print("❌ 未找到弹窗显示函数")
            
            # 检查批量设置函数
            if 'batchSetPriority' in response.text:
                print("✅ 找到批量设置函数")
            else:
                print("❌ 未找到批量设置函数")
            
            # 检查其他批量按钮是否正常
            batch_buttons = ['批量重置为待发布', '批量标记为已发布', '批量标记为失败', '批量删除']
            found_buttons = 0
            for button_text in batch_buttons:
                if button_text in response.text:
                    found_buttons += 1
            
            print(f"✅ 找到 {found_buttons}/{len(batch_buttons)} 个其他批量按钮")
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 简化后的批量优先级设置功能测试完成！")
    print("\n简化后的特点：")
    print("1. ✅ 统一样式 - 与其他批量按钮保持一致的样式")
    print("2. ✅ 简洁布局 - 移除了额外的标题和复杂布局")
    print("3. ✅ 功能完整 - 保留了弹窗选择和批量设置功能")
    print("4. ✅ 用户友好 - 显示选中文案数量和确认对话框")
    print("\n使用方法：")
    print("1. 勾选要设置的文案（使用复选框）")
    print("2. 点击'批量设置优先级'按钮")
    print("3. 在弹窗中查看选中数量并选择优先级")
    print("4. 确认后系统批量更新并刷新页面")
    print("\n按钮布局：")
    print("[批量重置为待发布] [批量标记为已发布] [批量标记为失败] [批量删除] [批量设置优先级]")

if __name__ == '__main__':
    test_simplified_batch_priority()
