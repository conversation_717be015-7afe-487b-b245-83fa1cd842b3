<!-- 简化版客户管理页面内容 -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>客户管理</h2>
        </div>
        <div class="col-md-4 text-end">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                <i class="bi bi-plus-lg"></i> 添加客户
            </button>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
        <form id="filterForm" method="GET">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="status" class="form-label">状态筛选</label>
                        <select class="form-select" id="status" name="status" onchange="filterClients()">
                            <option value="">全部状态</option>
                            <option value="1" {% if current_status == '1' %}selected{% endif %}>启用</option>
                            <option value="0" {% if current_status == '0' %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ current_search or '' }}"
                               placeholder="客户名称或联系人"
                               onkeypress="if(event.key==='Enter') filterClients()">
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="button" class="btn btn-primary me-2" onclick="filterClients()">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilter()">
                            <i class="bi bi-x-circle"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 客户列表 -->
    <div class="card" id="client-list-container">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>客户名称</th>
                            <th>联系人</th>
                            <th>需要审核</th>
                            <th>文章数</th>
                            <th>发布数</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>{{ client.id }}</td>
                            <td>{{ client.name }}</td>
                            <td>{{ client.contact or '-' }}</td>
                            <td>
                                <span class="badge bg-{{ 'primary' if client.need_review else 'secondary' }} review-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleClientReview({{ client.id }})"
                                      title="点击切换审核状态">
                                    {{ '需要审核' if client.need_review else '无需审核' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'info' if client.total_content_count > 0 else 'light' }} text-{{ 'white' if client.total_content_count > 0 else 'dark' }}"
                                      title="文章总数（包括所有状态）">
                                    {{ client.total_content_count }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if client.published_count > 0 else 'light' }} text-{{ 'white' if client.published_count > 0 else 'dark' }}"
                                      title="已发布的文章数量">
                                    {{ client.published_count }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if client.status else 'secondary' }} status-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleClientStatus({{ client.id }})"
                                      title="点击切换状态">
                                    {{ '启用' if client.status else '禁用' }}
                                </span>
                            </td>
                            <td>{{ client.created_at.strftime('%Y-%m-%d %H:%M') if client.created_at else '-' }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <a href="javascript:void(0)" class="text-info text-decoration-none text-action-link"
                                       onclick="manageClientContent({{ client.id }}, '{{ client.name }}')"
                                       title="文章管理">
                                        文章管理
                                    </a>
                                    <span class="action-separator">|</span>
                                    <a href="javascript:void(0)" class="text-primary text-decoration-none text-action-link"
                                       onclick="editClient({{ client.id }})"
                                       title="编辑客户">
                                        编辑
                                    </a>
                                    <span class="action-separator">|</span>
                                    <a href="javascript:void(0)" class="text-danger text-decoration-none text-action-link"
                                       onclick="deleteClient({{ client.id }}, '{{ client.name }}')"
                                       title="删除客户">
                                        删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">暂无客户数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            {% if pagination %}
                {% include 'components/pagination.html' %}
            {% endif %}
        </div>
    </div>
</div>

<!-- 新增客户模态框 -->
<div class="modal fade" id="addClientModal" tabindex="-1" aria-labelledby="addClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addClientModalLabel">
                    <i class="bi bi-plus-circle"></i> 添加客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="addClientContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 客户文章管理模态框 -->
<div class="modal fade" id="clientContentModal" tabindex="-1" aria-labelledby="clientContentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clientContentModalLabel">
                    <i class="bi bi-file-text"></i> 客户文章管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="clientContentBody">
                <!-- 动态加载内容 -->
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑客户模态框 -->
<div class="modal fade" id="editClientModal" tabindex="-1" aria-labelledby="editClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editClientModalLabel">
                    <i class="bi bi-pencil"></i> 编辑客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="editClientContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>

<style>
.filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

/* 文字按钮样式 */
.text-action-link {
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.text-action-link:hover {
    text-decoration: underline !important;
    opacity: 0.8;
}

.text-info.text-action-link:hover {
    color: #0dcaf0 !important;
}

.text-primary.text-action-link:hover {
    color: #0d6efd !important;
}

.text-danger.text-action-link:hover {
    color: #dc3545 !important;
}

/* 操作列分隔符 */
.action-separator {
    color: #dee2e6;
    font-weight: 300;
    margin: 0 0.25rem;
}
</style>

<script>
console.log('客户管理页面已加载');

// 检查CSRF令牌
const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
console.log('🔐 CSRF令牌检查:', csrfToken ? '✅ 存在' : '❌ 不存在', csrfToken);

// 新增客户模态框事件
document.getElementById('addClientModal').addEventListener('show.bs.modal', function() {
    console.log('显示添加客户模态框');
    console.log('正在加载:', '/clients/create');

    // 简化的加载逻辑
    fetch('/simple/clients/create', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('响应状态:', response.status);
        return response.text();
    })
    .then(html => {
        console.log('✅ 新增客户表单加载完成');
        console.log('📄 HTML内容长度:', html.length);
        document.getElementById('addClientContent').innerHTML = html;

        // 检查表单是否正确加载并初始化
        setTimeout(() => {
            const form = document.getElementById('client-form');
            const submitBtn = document.getElementById('submit-btn');
            console.log('🔍 表单加载检查:', {
                form: form,
                submitBtn: submitBtn,
                formHTML: form ? form.outerHTML.substring(0, 200) + '...' : 'null'
            });

            // 手动初始化表单
            if (form && submitBtn) {
                initializeClientFormManually(form, submitBtn, false);
            }
        }, 200);
    })
    .catch(error => {
        console.error('❌ 新增客户表单加载失败:', error);
        document.getElementById('addClientContent').innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
    });
});

// 编辑客户模态框事件
// 编辑客户模态框事件
document.getElementById('editClientModal').addEventListener('show.bs.modal', function() {
    console.log('显示编辑客户模态框');
});

// 编辑客户函数 - 使用AJAX和模态框
window.editClient = function(clientId) {
    console.log('🔧 编辑客户:', clientId);
    const modalElement = document.getElementById('editClientModal');
    modalElement.dataset.clientId = clientId;

    console.log('正在加载:', `/simple/clients/${clientId}/edit`);

    // 使用AJAX加载编辑表单
    fetch(`/simple/clients/${clientId}/edit`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('✅ 编辑客户表单加载完成');
        console.log('📄 HTML内容长度:', html.length);
        document.getElementById('editClientContent').innerHTML = html;

        // 初始化表单
        setTimeout(() => {
            const form = document.getElementById('client-form');
            const submitBtn = document.getElementById('submit-btn');
            console.log('🔍 编辑表单加载检查:', {
                form: form,
                submitBtn: submitBtn,
                formHTML: form ? form.outerHTML.substring(0, 200) + '...' : 'null'
            });

            // 手动初始化表单
            if (form && submitBtn) {
                initializeClientFormManually(form, submitBtn, true);
            }
        }, 200);

        // 显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    })
    .catch(error => {
        console.error('❌ 编辑客户表单加载失败:', error);
        document.getElementById('editClientContent').innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';

        // 仍然显示模态框，让用户看到错误信息
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    });
};

// 切换客户状态函数
window.toggleClientStatus = function(clientId) {
    console.log('切换客户状态:', clientId);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('CSRF令牌:', csrfToken);

    // 直接切换，不需要确认对话框
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/clients/${clientId}/toggle_status`, {
        method: 'POST',
        headers: headers
    })
    .then(response => {
        console.log('状态切换响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('状态切换响应数据:', data);
        if (data.success) {
            // 更新页面上的状态显示，不刷新页面
            updateClientStatusDisplay(clientId, data.status);
            showToast(data.message, 'success');
        } else {
            showToast(data.message || '操作失败', 'danger');
        }
    })
    .catch(error => {
        console.error('切换状态失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
};

// 切换客户审核状态函数
window.toggleClientReview = function(clientId) {
    console.log('切换客户审核状态:', clientId);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('CSRF令牌:', csrfToken);

    // 直接切换，不需要确认对话框
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/clients/${clientId}/toggle_review`, {
        method: 'POST',
        headers: headers
    })
    .then(response => {
        console.log('审核状态切换响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('审核状态切换响应数据:', data);
        if (data.success) {
            // 更新页面上的审核状态显示，不刷新页面
            updateClientReviewDisplay(clientId, data.need_review);
            showToast(data.message, 'success');
        } else {
            showToast(data.message || '操作失败', 'danger');
        }
    })
    .catch(error => {
        console.error('切换审核状态失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
};

// 更新客户状态显示函数
function updateClientStatusDisplay(clientId, newStatus) {
    console.log('更新客户状态显示:', clientId, newStatus);

    // 查找对应的状态标签
    const statusBadge = document.querySelector(`span[onclick="toggleClientStatus(${clientId})"]`);

    if (statusBadge) {
        // 更新文本
        statusBadge.textContent = newStatus ? '启用' : '禁用';

        // 更新样式
        statusBadge.className = `badge ${newStatus ? 'bg-success' : 'bg-secondary'} status-badge`;

        console.log('✅ 状态显示已更新');
    } else {
        console.error('❌ 找不到状态标签');
    }
}

// 更新客户审核状态显示函数
function updateClientReviewDisplay(clientId, needReview) {
    console.log('更新客户审核状态显示:', clientId, needReview);

    // 查找对应的审核状态标签
    const reviewBadge = document.querySelector(`span[onclick="toggleClientReview(${clientId})"]`);

    if (reviewBadge) {
        // 更新文本
        reviewBadge.textContent = needReview ? '需要审核' : '无需审核';

        // 更新样式
        reviewBadge.className = `badge ${needReview ? 'bg-primary' : 'bg-secondary'} review-badge`;

        console.log('✅ 审核状态显示已更新');
    } else {
        console.error('❌ 找不到审核状态标签');
    }
}

// 客户文章管理函数
window.manageClientContent = function(clientId, clientName) {
    console.log('管理客户文章:', clientId, clientName);

    // 更新模态框标题
    document.getElementById('clientContentModalLabel').innerHTML =
        `<i class="bi bi-file-text"></i> ${clientName} - 文章管理`;

    // 保存客户ID到模态框
    const modalElement = document.getElementById('clientContentModal');
    modalElement.dataset.clientId = clientId;

    // 显示模态框
    const modal = new bootstrap.Modal(modalElement);
    modal.show();

    // 加载客户文章数据
    loadClientContent(clientId);
};

// 加载客户文章数据
function loadClientContent(clientId) {
    const contentBody = document.getElementById('clientContentBody');

    // 显示加载状态
    contentBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载客户文章数据...</p>
        </div>
    `;

    // 获取客户文章数据
    fetch(`/simple/api/clients/${clientId}/content-management`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderClientContentManagement(data.data, clientId);
            } else {
                contentBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        加载失败: ${data.message || '未知错误'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载客户文章失败:', error);
            contentBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    网络错误，请稍后重试
                </div>
            `;
        });
}

// 渲染客户文章管理界面
function renderClientContentManagement(data, clientId) {
    const contentBody = document.getElementById('clientContentBody');

    let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h6><i class="bi bi-info-circle"></i> 统计信息</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${data.stats.total_tasks}</h5>
                                <p class="card-text">总任务数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">${data.stats.total_content}</h5>
                                <p class="card-text">总文章数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">${data.stats.published_content}</h5>
                                <p class="card-text">已发布</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="bi bi-tools"></i> 批量操作</h6>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-warning" onclick="batchDeleteContent(${clientId})">
                        <i class="bi bi-trash"></i> 批量删除文章
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteAllTasks(${clientId})">
                        <i class="bi bi-folder-x"></i> 删除所有任务
                    </button>
                </div>
            </div>
        </div>

        <hr>

        <h6><i class="bi bi-folder"></i> 任务列表</h6>
    `;

    if (data.tasks && data.tasks.length > 0) {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllTasks" onchange="toggleAllTasks()">
                            </th>
                            <th>任务名称</th>
                            <th>文章数量</th>
                            <th>已发布</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.tasks.forEach(task => {
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="task-checkbox" value="${task.id}">
                    </td>
                    <td>${task.name}</td>
                    <td>
                        <span class="badge bg-info">${task.content_count}</span>
                    </td>
                    <td>
                        <span class="badge bg-success">${task.published_count}</span>
                    </td>
                    <td>${task.created_at}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary"
                                    onclick="viewTaskContent(${task.id}, '${task.name}')" title="查看文章">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="deleteTask(${task.id}, '${task.name}')" title="删除任务">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                <button type="button" class="btn btn-warning" onclick="batchDeleteSelectedTasks()">
                    <i class="bi bi-trash"></i> 删除选中任务
                </button>
            </div>
        `;
    } else {
        html += `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> 该客户暂无任务数据
            </div>
        `;
    }

    contentBody.innerHTML = html;
}

// 删除客户函数
window.deleteClient = function(clientId, clientName) {
    console.log('删除客户:', clientId, clientName);

    if (confirm(`确定要删除客户"${clientName}"吗？此操作不可恢复！`)) {
        // 获取CSRF令牌
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
        console.log('🔐 删除操作CSRF令牌:', csrfToken ? '✅ 存在' : '❌ 不存在');

        const headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        fetch(`/simple/clients/${clientId}/delete`, {
            method: 'POST',
            headers: headers
        })
        .then(response => {
            console.log('📨 删除响应状态:', response.status);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('❌ 删除服务器错误响应:', text);
                    throw new Error(`HTTP ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('📨 删除响应数据:', data);
            if (data.success) {
                // 移除表格行，不刷新页面
                removeClientRow(clientId);
                showToast(data.message || '客户删除成功', 'success');
            } else {
                showToast(data.message || '删除失败', 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 删除操作失败:', error);
            showToast('删除失败，请重试', 'danger');
        });
    }
};

// 移除客户行函数
function removeClientRow(clientId) {
    console.log('移除客户行:', clientId);

    // 查找包含客户ID的表格行
    const rows = document.querySelectorAll('tbody tr');
    let targetRow = null;

    for (const row of rows) {
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
            targetRow = row;
            break;
        }
    }

    if (targetRow) {
        // 添加淡出动画
        targetRow.style.transition = 'opacity 0.3s';
        targetRow.style.opacity = '0';

        // 延迟移除元素
        setTimeout(() => {
            targetRow.remove();
            console.log('✅ 客户行已移除');
        }, 300);
    } else {
        console.error('❌ 找不到客户行:', clientId);
        // 如果找不到行，就刷新整个列表
        refreshClientList();
    }
}

// 手动初始化客户表单函数
function initializeClientFormManually(form, submitBtn, isEdit) {
    console.log('🔧 手动初始化客户表单:', { isEdit: isEdit });

    // 设置开关事件（移除之前的事件监听器）
    const needReviewSwitch = document.getElementById('need_review_switch');
    const needReviewText = document.getElementById('need_review_text');
    if (needReviewSwitch && needReviewText) {
        // 移除之前的事件监听器
        if (needReviewSwitch._changeHandler) {
            needReviewSwitch.removeEventListener('change', needReviewSwitch._changeHandler);
        }

        const reviewChangeHandler = function() {
            needReviewText.textContent = this.checked ? '需要审核' : '无需审核';
        };

        needReviewSwitch.addEventListener('change', reviewChangeHandler);
        needReviewSwitch._changeHandler = reviewChangeHandler;
        console.log('✅ 审核开关事件已绑定');
    }

    const statusSwitch = document.getElementById('status_switch');
    const statusText = document.getElementById('status_text');
    if (statusSwitch && statusText) {
        // 移除之前的事件监听器
        if (statusSwitch._changeHandler) {
            statusSwitch.removeEventListener('change', statusSwitch._changeHandler);
        }

        const statusChangeHandler = function() {
            statusText.textContent = this.checked ? '启用' : '禁用';
        };

        statusSwitch.addEventListener('change', statusChangeHandler);
        statusSwitch._changeHandler = statusChangeHandler;
        console.log('✅ 状态开关事件已绑定');
    }

    // 移除之前的事件监听器（如果存在）
    const existingHandler = submitBtn._clientFormHandler;
    if (existingHandler) {
        submitBtn.removeEventListener('click', existingHandler);
        console.log('🗑️ 移除了之前的事件处理器');
    }

    // 创建新的事件处理器
    const clickHandler = function(e) {
        e.preventDefault();
        console.log('🔄 提交客户表单');
        console.log('🔍 表单元素:', form);
        console.log('🔍 提交按钮:', submitBtn);

        // 创建FormData
        const formData = new FormData(form);

        // 获取提交URL
        let clientId = form.dataset.clientId;

        // 如果表单中没有clientId，尝试从模态框中获取
        if (!clientId && isEdit) {
            const modalElement = document.getElementById('editClientModal');
            clientId = modalElement?.dataset?.clientId;
        }

        const submitUrl = isEdit ? `/simple/clients/${clientId}/edit` : '/simple/clients/create';

        // 获取CSRF令牌
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        // 确保FormData包含CSRF令牌
        if (csrfToken) {
            formData.set('csrf_token', csrfToken);
        }

        console.log('📝 表单提交信息:', {
            isEdit: isEdit,
            clientId: clientId,
            submitUrl: submitUrl,
            csrfToken: csrfToken,
            formData: Array.from(formData.entries())
        });

        // 验证编辑模式下的客户ID
        if (isEdit && !clientId) {
            console.error('❌ 编辑模式下缺少客户ID');
            alert('错误：无法获取客户ID，请重新打开编辑窗口');
            return;
        }

        // 设置请求头
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        // 提交表单
        fetch(submitUrl, {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => {
            console.log('📨 响应状态:', response.status);
            console.log('📨 响应头:', response.headers);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('❌ 服务器错误响应:', text);
                    throw new Error(`HTTP ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('📨 服务器响应:', data);

            if (data.success) {
                showToast(data.message || '保存成功！', 'success');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
                if (modal) {
                    modal.hide();
                }

                // 根据操作类型选择更新方式
                if (isEdit) {
                    // 编辑模式：只更新当前客户行的信息
                    setTimeout(() => {
                        updateClientRowData(clientId);
                    }, 300);
                } else {
                    // 新增模式：刷新整个列表并高亮新客户
                    setTimeout(() => {
                        refreshClientList(data.client_id || null);
                    }, 500);
                }
            } else {
                let errorMessage = data.message || '保存失败';
                if (data.errors) {
                    console.log('📝 表单验证错误:', data.errors);
                    // 显示具体的验证错误
                    const errorDetails = Object.values(data.errors).flat().join(', ');
                    errorMessage += ': ' + errorDetails;
                }
                showToast(errorMessage, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 提交失败:', error);
            showToast('提交失败：' + error.message, 'danger');
        });
    };

    // 绑定新的事件处理器并保存引用
    submitBtn.addEventListener('click', clickHandler);
    submitBtn._clientFormHandler = clickHandler;
    console.log('✅ 新的事件处理器已绑定');

    console.log('✅ 客户表单手动初始化完成');
}

// 移除了重复的loadModalContent函数，使用内联fetch

// Toast提示函数
function showToast(message, type = 'info') {
    // 创建toast元素
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'danger' ? 'danger' : 'info'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    // 添加到页面
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 刷新客户列表函数
function refreshClientList(highlightClientId = null) {
    console.log('🔄 刷新客户列表', highlightClientId ? `高亮客户ID: ${highlightClientId}` : '');

    fetch('/simple/clients', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('✅ 客户列表刷新成功');

        // 解析HTML并更新客户表格
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 找到新的客户表格
        const newTable = tempDiv.querySelector('.table-responsive');
        const currentTable = document.querySelector('.table-responsive');

        if (newTable && currentTable) {
            // 更平滑的更新效果
            currentTable.style.transition = 'opacity 0.2s ease';
            currentTable.style.opacity = '0.8';

            // 替换表格内容
            setTimeout(() => {
                currentTable.innerHTML = newTable.innerHTML;
                currentTable.style.opacity = '1';
                console.log('✅ 客户表格已更新');

                // 高亮显示指定的客户行
                if (highlightClientId) {
                    setTimeout(() => {
                        highlightClientRow(highlightClientId);
                    }, 100);
                }

                // 只在新增客户时显示提示
                if (highlightClientId) {
                    showToast('新客户已添加', 'success');
                }
            }, 150);
        } else {
            console.error('❌ 找不到客户表格');
        }
    })
    .catch(error => {
        console.error('❌ 刷新客户列表失败:', error);
        showToast('刷新列表失败，请手动刷新页面', 'warning');
    });
}

// 高亮显示客户行函数
function highlightClientRow(clientId) {
    console.log('🎯 高亮显示客户行:', clientId);

    // 查找包含客户ID的表格行
    const rows = document.querySelectorAll('tbody tr');
    let targetRow = null;

    for (const row of rows) {
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
            targetRow = row;
            break;
        }
    }

    if (targetRow) {
        // 添加高亮效果
        targetRow.style.backgroundColor = '#d4edda';
        targetRow.style.transition = 'background-color 0.5s ease';

        // 滚动到该行
        targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 3秒后移除高亮效果
        setTimeout(() => {
            targetRow.style.backgroundColor = '';
        }, 3000);

        console.log('✅ 客户行已高亮');
    } else {
        console.log('❌ 找不到要高亮的客户行:', clientId);
    }
}

// 更新单个客户行数据函数
function updateClientRowData(clientId) {
    console.log('🔄 更新客户行数据:', clientId);

    // 获取最新的客户数据
    fetch(`/simple/clients/${clientId}/data`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(response => {
        console.log('✅ 获取客户数据成功:', response);

        // 检查响应格式
        if (!response.success || !response.client) {
            throw new Error('无效的响应格式');
        }

        const clientData = response.client;

        // 查找对应的表格行
        const rows = document.querySelectorAll('tbody tr');
        let targetRow = null;

        for (const row of rows) {
            const firstCell = row.querySelector('td:first-child');
            if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
                targetRow = row;
                break;
            }
        }

        if (targetRow) {
            // 更新行数据
            const cells = targetRow.querySelectorAll('td');
            if (cells.length >= 6) {
                // 更新客户名称 (第2列)
                cells[1].textContent = clientData.name || '-';

                // 更新联系人 (第3列)
                cells[2].textContent = clientData.contact || '-';

                // 更新审核状态 (第4列)
                const reviewBadge = cells[3].querySelector('span');
                if (reviewBadge) {
                    reviewBadge.textContent = clientData.need_review ? '需要审核' : '无需审核';
                    reviewBadge.className = `badge ${clientData.need_review ? 'bg-primary' : 'bg-secondary'} review-badge`;
                }

                // 更新启用状态 (第5列)
                const statusBadge = cells[4].querySelector('span');
                if (statusBadge) {
                    statusBadge.textContent = clientData.status ? '启用' : '禁用';
                    statusBadge.className = `badge ${clientData.status ? 'bg-success' : 'bg-secondary'} status-badge`;
                }

                // 添加轻微的高亮效果表示更新
                targetRow.style.backgroundColor = '#e3f2fd';
                targetRow.style.transition = 'background-color 0.3s ease';

                setTimeout(() => {
                    targetRow.style.backgroundColor = '';
                }, 1500);

                console.log('✅ 客户行数据已更新');
                showToast('客户信息已更新', 'success');
            }
        } else {
            console.error('❌ 找不到要更新的客户行:', clientId);
            // 如果找不到行，降级到刷新整个列表
            refreshClientList(clientId);
        }
    })
    .catch(error => {
        console.error('❌ 获取客户数据失败:', error);
        // 出错时降级到刷新整个列表
        refreshClientList(clientId);
    });
}

// 简化版客户管理分页功能
console.log('客户管理页面已加载');

// 筛选客户 - 全局函数，确保按钮可以调用
window.filterClients = function() {
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    // 获取当前的每页显示数量，保持用户设置
    // 优先从分页组件获取，如果没有则从URL参数获取，最后默认20
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    let currentPerPage = perPageSelect?.value;

    // 如果分页组件中没有找到，尝试从URL参数获取
    if (!currentPerPage) {
        const urlParams = new URLSearchParams(window.location.search);
        currentPerPage = urlParams.get('per_page') || '20';
    }

    console.log('筛选客户详细信息:', {
        status: status,
        statusEmpty: status === '',
        search: search,
        currentPerPage: currentPerPage,
        statusElement: !!statusElement,
        searchElement: !!searchElement
    });

    // 构建查询参数，包含每页显示数量
    const params = new URLSearchParams();
    params.set('page', '1'); // 筛选时重置到第一页
    params.set('per_page', currentPerPage); // 保持当前每页显示数量

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
        console.log('添加状态筛选:', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
        console.log('添加搜索筛选:', search);
    }

    console.log('发送筛选请求:', `/simple/clients?${params.toString()}`);

    // 重新加载客户列表
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
            console.log('筛选完成，页面已更新');
        }
    })
    .catch(error => {
        console.error('筛选失败:', error);
        alert('筛选失败，请重试');
    });
};

// 重置筛选 - 全局函数
window.resetFilter = function() {
    document.getElementById('status').value = '';
    document.getElementById('search').value = '';

    // 获取当前的每页显示数量，保持用户设置
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const currentPerPage = perPageSelect?.value || 20;

    console.log('重置筛选，保持每页显示数量:', currentPerPage);

    // 构建查询参数，只包含每页显示数量
    const params = new URLSearchParams();
    params.set('page', '1');
    params.set('per_page', currentPerPage);

    // 重新加载客户列表
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
            console.log('重置完成，页面已更新');
        }
    })
    .catch(error => {
        console.error('重置失败:', error);
        alert('重置失败，请重试');
    });
};

// 定义简化版本的分页函数
function simpleClientChangePageSize(newSize) {
    // 获取当前的筛选参数
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    console.log('改变每页显示数量:', { newSize, status, search });

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('per_page', newSize);
    params.set('page', '1'); // 重置到第一页

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
    }

    console.log('改变每页数量请求:', `/simple/clients?${params.toString()}`);

    // 使用AJAX重新加载
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请重试');
    });
}

function simpleClientChangePage(pageNum) {
    // 获取当前的筛选参数和每页显示数量
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    // 更精确地获取分页组件中的每页显示数量选择器
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const perPage = perPageSelect?.value || 20;

    console.log('翻页操作:', { pageNum, perPage, status, search });

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('page', pageNum);
    params.set('per_page', perPage);

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
    }

    console.log('翻页请求:', `/simple/clients?${params.toString()}`);

    // 使用AJAX重新加载
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
        }
    })
    .catch(error => {
        console.error('翻页失败:', error);
        alert('翻页失败，请重试');
    });
}

// 设置全局函数供分页组件使用
window.changePageForSimple = simpleClientChangePage;

// 保存原来的分页函数（如果存在）
window.originalChangePageSize = window.changePageSize;
window.originalChangePage = window.changePage;

// 设置当前页面的分页函数
window.changePageSize = simpleClientChangePageSize;
window.changePage = simpleClientChangePage;

// 批量操作相关函数
function toggleAllTasks() {
    const selectAll = document.getElementById('selectAllTasks');
    const checkboxes = document.querySelectorAll('.task-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function batchDeleteContent(clientId) {
    if (confirm('确定要删除该客户的所有文章内容吗？此操作不可恢复！')) {
        performBatchOperation('delete-content', clientId, []);
    }
}

function deleteAllTasks(clientId) {
    if (confirm('确定要删除该客户的所有任务吗？这将删除任务及其下的所有文章！此操作不可恢复！')) {
        performBatchOperation('delete-all-tasks', clientId, []);
    }
}

function batchDeleteSelectedTasks() {
    const selectedTasks = Array.from(document.querySelectorAll('.task-checkbox:checked'))
                               .map(cb => cb.value);

    if (selectedTasks.length === 0) {
        alert('请先选择要删除的任务');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedTasks.length} 个任务吗？此操作不可恢复！`)) {
        const clientId = getCurrentClientId();
        performBatchOperation('delete-selected-tasks', clientId, selectedTasks);
    }
}

function deleteTask(taskId, taskName) {
    if (confirm(`确定要删除任务"${taskName}"吗？此操作将删除任务及其下的所有文章！`)) {
        const clientId = getCurrentClientId();
        performBatchOperation('delete-task', clientId, [taskId]);
    }
}

function viewTaskContent(taskId, taskName) {
    // 这里可以实现查看任务内容的功能
    alert(`查看任务"${taskName}"的内容功能待实现`);
}

function performBatchOperation(operation, clientId, taskIds) {
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    // 显示加载状态
    const contentBody = document.getElementById('clientContentBody');
    const originalContent = contentBody.innerHTML;

    contentBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">处理中...</span>
            </div>
            <p class="mt-2">正在执行操作，请稍候...</p>
        </div>
    `;

    fetch(`/simple/api/clients/${clientId}/batch-operation`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
            operation: operation,
            task_ids: taskIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message || '操作成功');
            // 重新加载客户文章数据
            loadClientContent(clientId);
        } else {
            alert('操作失败: ' + (data.message || '未知错误'));
            contentBody.innerHTML = originalContent;
        }
    })
    .catch(error => {
        console.error('批量操作失败:', error);
        alert('网络错误，请稍后重试');
        contentBody.innerHTML = originalContent;
    });
}

function getCurrentClientId() {
    // 从模态框获取当前客户ID
    const modal = document.getElementById('clientContentModal');
    return modal.dataset.clientId || null;
}

</script>
