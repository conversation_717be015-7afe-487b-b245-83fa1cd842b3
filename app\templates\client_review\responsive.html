<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ client.name }} - 文案审核 v4</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* 响应式头部 */
        .responsive-header {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(255, 36, 66, 0.3);
        }
        
        /* 桌面版头部 */
        @media (min-width: 768px) {
            .responsive-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 2rem 0;
                position: relative;
                margin-bottom: 2rem;
            }
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        
        @media (min-width: 768px) {
            .header-title {
                font-size: 2rem;
                text-align: left;
            }
        }
        
        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
            margin-top: 0.25rem;
        }
        
        @media (min-width: 768px) {
            .header-subtitle {
                font-size: 1rem;
                margin-top: 0.5rem;
            }
        }
        
        /* 统计卡片 */
        .stats-container {
            padding: 1rem;
            background: white;
            margin-bottom: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .stats-container {
                margin: 0 auto 2rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            text-align: center;
        }
        
        .stat-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        @media (min-width: 768px) {
            .stat-item {
                padding: 1.5rem;
                border-radius: 15px;
                transition: transform 0.2s;
            }
            
            .stat-item:hover {
                transform: translateY(-2px);
            }
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff2442;
            display: block;
        }
        
        @media (min-width: 768px) {
            .stat-number {
                font-size: 2rem;
                color: #667eea;
            }
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        @media (min-width: 768px) {
            .stat-label {
                font-size: 0.9rem;
                margin-top: 0.5rem;
            }
        }
        
        /* 筛选栏 */
        .filter-bar {
            background: white;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        @media (min-width: 768px) {
            .filter-bar {
                margin: 0 auto 2rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-bottom: none;
            }
        }
        
        .filter-tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .filter-tabs {
                justify-content: center;
                overflow-x: visible;
                padding-bottom: 0;
            }
        }
        
        .filter-tab {
            background: #f8f9fa;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            white-space: nowrap;
            color: #666;
            transition: all 0.2s;
        }
        
        @media (min-width: 768px) {
            .filter-tab {
                padding: 0.75rem 1.5rem;
                font-size: 0.9rem;
            }
        }
        
        .filter-tab.active {
            background: #ff2442;
            color: white;
        }
        
        @media (min-width: 768px) {
            .filter-tab.active {
                background: #667eea;
            }
        }
        
        /* 内容列表 */
        .content-list {
            padding: 0 1rem;
        }
        
        @media (min-width: 768px) {
            .content-list {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0;
            }
        }
        
        /* 内容卡片 */
        .content-card {
            background: white;
            border-radius: 12px;
            margin: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.2s ease;
        }
        
        @media (min-width: 768px) {
            .content-card {
                margin: 0 auto 1rem;
                max-width: 1200px;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
        }
        
        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        /* 图片样式 */
        .content-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .content-image {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .content-image:hover {
            transform: scale(1.05);
        }
        
        .content-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        @media (min-width: 768px) {
            .content-images {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 0.75rem;
            }
        }
        
        /* 内容元信息 */
        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: #999;
            margin-bottom: 1rem;
        }
        
        @media (min-width: 768px) {
            .content-meta {
                font-size: 0.85rem;
            }
        }
        
        /* 移动端内容布局 */
        .content-header {
            padding: 1rem 1rem 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .content-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin: 0;
            flex: 1;
            margin-right: 0.5rem;
        }
        
        /* 桌面端内容布局 */
        @media (min-width: 768px) {
            .content-header {
                padding: 1.5rem 1.5rem 1rem;
            }
            
            .content-title {
                font-size: 1.2rem;
                margin-right: 1rem;
            }
        }
        
        .content-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .content-body {
            padding: 0 1rem;
        }
        
        @media (min-width: 768px) {
            .content-body {
                padding: 0 1.5rem;
            }
        }
        
        .content-text {
            font-size: 0.9rem;
            line-height: 1.5;
            color: #333;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        @media (min-width: 768px) {
            .content-text {
                font-size: 1rem;
                line-height: 1.6;
                -webkit-line-clamp: 3;
            }
        }
        
        /* 操作按钮 */
        .content-actions {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            border-top: 1px solid #f0f0f0;
        }
        
        @media (min-width: 768px) {
            .content-actions {
                padding: 1.5rem;
                gap: 0.75rem;
            }
        }
        
        .action-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        @media (min-width: 768px) {
            .action-btn {
                flex: none;
                min-width: 120px;
                padding: 0.875rem 1.5rem;
                font-size: 1rem;
            }
        }
        
        .btn-view {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-view:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .btn-approve {
            background: #ff2442;
            color: white;
        }
        
        .btn-approve:hover {
            background: #e01e3a;
            color: white;
        }
        
        @media (min-width: 768px) {
            .btn-approve {
                background: #28a745;
            }
            
            .btn-approve:hover {
                background: #218838;
            }
        }
        
        .btn-reject {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-reject:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        @media (min-width: 768px) {
            .btn-reject {
                background: #dc3545;
                color: white;
                border: none;
            }
            
            .btn-reject:hover {
                background: #c82333;
            }
        }
        
        /* 加载更多 */
        .load-more {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .load-more-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #666;
            padding: 0.75rem 2rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #999;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: #f5f5f5;
        }
        
        @media (min-width: 768px) {
            .safe-area-bottom {
                display: none;
            }
        }
        
        /* 驳回类型卡片样式 */
        .rejection-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }

        .rejection-type-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.1);
        }

        .rejection-type-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .rejection-type-card .form-check-input {
            transform: scale(1.2);
        }

        .quick-reason-btn {
            transition: all 0.2s ease;
        }

        .quick-reason-btn:hover {
            transform: translateY(-1px);
        }

        .quick-reason-btn.selected {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <!-- 响应式头部 -->
    <div class="responsive-header">
        <div class="container-fluid">
            <div class="header-content">
                <div>
                    <h1 class="header-title">{{ client.name }}</h1>
                    <div class="header-subtitle">文案审核</div>
                    <!-- 调试信息 -->
                    <div class="d-none" id="debug-info">
                        ShareKey: {{ share_key }}
                        Client: {{ client.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number" id="totalCount">-</span>
                <div class="stat-label">总文案</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="pendingCount">-</span>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="reviewedCount">-</span>
                <div class="stat-label">已审核</div>
            </div>
        </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
        <div class="filter-tabs">
            <button class="filter-tab active" data-status="">全部</button>
            <button class="filter-tab" data-status="pending">待审核</button>
            <button class="filter-tab" data-status="approved">待发布</button>
            <button class="filter-tab" data-status="published">已发布</button>
        </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list" id="contentList">
        <!-- 内容将通过JavaScript动态加载 -->
    </div>

    <!-- 加载更多 -->
    <div class="load-more" id="loadMore" style="display: none;">
        <button class="load-more-btn" onclick="loadMoreContent()">
            <i class="bi bi-arrow-clockwise me-2"></i>加载更多
        </button>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" id="emptyState" style="display: none;">
        <i class="bi bi-inbox"></i>
        <div>暂无文案内容</div>
    </div>

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle"></i> 驳回文案
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 驳回类型选择 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="bi bi-list-check"></i> 请选择问题类型：
                        </label>
                        <div class="row g-2">
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="content">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="content" id="client_type_content" checked>
                                            <label class="form-check-label w-100" for="client_type_content">
                                                <i class="bi bi-file-text fs-4 text-primary d-block mb-1"></i>
                                                <strong class="small">文案问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    标题、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="image">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="image" id="client_type_image">
                                            <label class="form-check-label w-100" for="client_type_image">
                                                <i class="bi bi-image fs-4 text-success d-block mb-1"></i>
                                                <strong class="small">图片问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    质量、内容等
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card rejection-type-card" data-type="both">
                                    <div class="card-body text-center p-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="rejection_type"
                                                   value="both" id="client_type_both">
                                            <label class="form-check-label w-100" for="client_type_both">
                                                <i class="bi bi-exclamation-circle fs-4 text-danger d-block mb-1"></i>
                                                <strong class="small">两者都有问题</strong>
                                                <small class="d-block text-muted" style="font-size: 0.7rem;">
                                                    全部重做
                                                </small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流转说明 -->
                    <div class="alert alert-info mb-3" id="clientFlowDescription">
                        <i class="bi bi-info-circle"></i>
                        <strong>流转说明：</strong>
                        <span id="clientFlowText">选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容</span>
                    </div>

                    <!-- 快捷理由选择 - 动态更新 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">常用驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3" id="clientQuickReasons">
                            <!-- 快捷理由按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 详细理由输入 -->
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-bold">详细说明：</label>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请详细说明需要修改的地方，以便创作者更好地理解和改进..." required></textarea>
                        <div class="form-text">
                            <i class="bi bi-lightbulb"></i>
                            建议提供具体的修改建议，这样可以提高修改效率
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i> 取消
                    </button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">
                        <i class="bi bi-check"></i> 确认驳回
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片查看模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片查看</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="文案图片" class="img-fluid" style="max-height: 70vh;">
                </div>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 清除可能的缓存问题
        console.clear();
        console.log('=== 响应式客户审核页面加载 ===');
        console.log('当前时间:', new Date().toISOString());
        console.log('页面URL:', window.location.href);
    </script>
    <script>
        // 全局变量
        let currentPage = 1;
        let currentStatus = '';
        let currentContentId = null;
        let isLoading = false;
        let hasMore = true;

        // 从模板获取shareKey，添加更多调试信息
        const templateShareKey = '{{ share_key }}';
        console.log('=== ShareKey 调试信息 ===');
        console.log('原始模板变量:', templateShareKey);
        console.log('变量类型:', typeof templateShareKey);
        console.log('变量长度:', templateShareKey ? templateShareKey.length : 'N/A');
        console.log('是否为空字符串:', templateShareKey === '');
        console.log('是否为undefined字符串:', templateShareKey === 'undefined');

        // 验证shareKey
        if (!templateShareKey || templateShareKey === 'undefined' || templateShareKey.trim() === '') {
            console.error('❌ ShareKey验证失败!');
            console.error('模板变量值:', templateShareKey);
            alert('页面加载错误：分享密钥未找到\n请检查链接是否正确');
            throw new Error('ShareKey验证失败'); // 阻止进一步执行
        }

        const shareKey = templateShareKey;
        console.log('✅ ShareKey验证成功:', shareKey);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadContent();
            initFilterTabs();
        });

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 初始化筛选标签
        function initFilterTabs() {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 更新激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 重置并加载新内容
                    currentStatus = this.dataset.status;
                    currentPage = 1;
                    hasMore = true;
                    document.getElementById('contentList').innerHTML = '';
                    loadContent();
                });
            });
        }

        // 加载统计信息
        function loadStats() {
            fetch(buildApiUrl('/stats'))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('totalCount').textContent = stats.total_count;
                        document.getElementById('pendingCount').textContent = stats.pending_count;
                        document.getElementById('reviewedCount').textContent = stats.reviewed_count;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 加载内容列表
        function loadContent() {
            if (isLoading || !hasMore) return;

            isLoading = true;
            const params = new URLSearchParams({
                page: currentPage,
                per_page: 10,
                status: currentStatus
            });

            fetch(buildApiUrl(`/contents?${params}`))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const contents = data.contents;
                        if (contents.length === 0) {
                            if (currentPage === 1) {
                                showEmptyState();
                            } else {
                                hasMore = false;
                                hideLoadMore();
                            }
                        } else {
                            renderContents(contents);
                            currentPage++;

                            // 检查是否还有更多内容
                            if (contents.length < 10) {
                                hasMore = false;
                                hideLoadMore();
                            } else {
                                showLoadMore();
                            }
                        }
                    } else {
                        showError('加载内容失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载内容失败:', error);
                    showError('网络错误，请稍后重试');
                })
                .finally(() => {
                    isLoading = false;
                });
        }

        // 渲染内容列表
        function renderContents(contents) {
            const container = document.getElementById('contentList');

            contents.forEach(content => {
                const contentCard = createContentCard(content);
                container.appendChild(contentCard);
            });

            hideEmptyState();
        }

        // 创建内容卡片
        function createContentCard(content) {
            const card = document.createElement('div');
            card.className = 'content-card';

            // 检测屏幕宽度决定点击行为
            const isMobile = window.innerWidth < 768;

            if (isMobile) {
                // 移动端：点击卡片跳转到详情页
                card.addEventListener('click', function(e) {
                    // 如果点击的是按钮，不跳转
                    if (e.target.closest('.action-btn')) {
                        return;
                    }
                    const accessKey = getAccessKey();
                    let detailUrl = `/client-review/${shareKey}/content/${content.id}`;
                    if (accessKey) {
                        detailUrl += `?key=${accessKey}`;
                    }
                    window.location.href = detailUrl;
                });
            }

            // 根据工作流状态和客户审核状态确定显示状态
            function getDisplayStatus(content) {
                // 已发布
                if (content.publish_status === 'published') {
                    return { class: 'status-published', text: '已发布' };
                }

                // 客户审核通过的情况
                if (content.client_review_status === 'approved') {
                    // 根据工作流状态判断具体状态
                    if (content.workflow_status === 'ready_to_publish') {
                        return { class: 'status-approved', text: '待发布' };
                    } else if (content.workflow_status === 'pending_publish') {
                        return { class: 'status-approved', text: '待发布' };
                    } else if (content.workflow_status === 'publishing') {
                        return { class: 'status-approved', text: '发布中' };
                    } else {
                        return { class: 'status-approved', text: '已通过' };
                    }
                }

                // 待审核：客户审核状态为pending
                if (content.client_review_status === 'pending' && content.workflow_status === 'pending_client_review') {
                    return { class: 'status-pending', text: '待审核' };
                }

                // 其他情况
                return { class: 'status-pending', text: '待审核' };
            }

            const status = getDisplayStatus(content);

            // 构建图片网格
            let imagesHtml = '';
            if (content.images && content.images.length > 0) {
                imagesHtml = '<div class="content-images">';
                content.images.slice(0, 6).forEach(image => {
                    // ContentImage.to_dict() 返回的字段名是 image_path
                    let imageUrl = image.image_path || image.url || image;

                    // 如果是相对路径，转换为正确的静态文件路径
                    if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                        // 图片实际存储在 /static/uploads/images/ 目录中
                        imageUrl = `/static/uploads/${imageUrl}`;
                    }

                    console.log('图片数据:', image, '使用URL:', imageUrl);
                    imagesHtml += `
                        <div class="content-image" onclick="showImageModal('${imageUrl}')">
                            <img src="${imageUrl}" alt="文案图片" loading="lazy" onerror="console.error('图片加载失败:', '${imageUrl}')">
                        </div>
                    `;
                });
                imagesHtml += '</div>';
            }

            // 构建操作按钮
            let actionsHtml = '';
            if (isMobile) {
                // 移动端按钮
                if (content.workflow_status === 'pending_client_review' && content.client_review_status === 'pending') {
                    actionsHtml = `
                        <div class="content-actions">
                            <button class="action-btn btn-approve" onclick="approveContent(${content.id})">
                                <i class="bi bi-check-lg me-1"></i>通过
                            </button>
                            <button class="action-btn btn-reject" onclick="showRejectModal(${content.id})">
                                <i class="bi bi-x-lg me-1"></i>驳回
                            </button>
                        </div>
                    `;
                }
            } else {
                // 桌面端按钮
                actionsHtml = `
                    <div class="content-actions">
                        <button class="action-btn btn-view" onclick="viewContent(${content.id})">
                            <i class="bi bi-eye me-1"></i>查看
                        </button>
                        ${content.workflow_status === 'pending_client_review' && content.client_review_status === 'pending' ? `
                            <button class="action-btn btn-approve" onclick="approveContent(${content.id})">
                                <i class="bi bi-check me-1"></i>通过
                            </button>
                            <button class="action-btn btn-reject" onclick="showRejectModal(${content.id})">
                                <i class="bi bi-x me-1"></i>驳回
                            </button>
                        ` : ''}
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="content-title">${content.title || '无标题'}</h3>
                        <div class="d-flex align-items-center gap-2">
                            <span class="content-status ${status.class}">${status.text}</span>
                        </div>
                    </div>
                </div>
                <div class="content-body">
                    <div class="content-text">${content.content || ''}</div>
                    ${imagesHtml}
                    <div class="content-meta">
                        <span>创建时间: ${formatDate(content.created_at)}</span>
                        <span>任务: ${content.task_name || '默认任务'}</span>
                    </div>
                </div>
                ${actionsHtml}
            `;

            return card;
        }

        // 查看文案详情（桌面端）
        function viewContent(contentId) {
            const accessKey = getAccessKey();
            let detailUrl = `/client-review/${shareKey}/content/${contentId}`;
            if (accessKey) {
                detailUrl += `?key=${accessKey}`;
            }
            window.location.href = detailUrl;
        }

        // 显示驳回模态框
        function showRejectModal(contentId) {
            currentContentId = contentId;
            document.getElementById('rejectReason').value = '';

            // 重置驳回类型选择为默认值（文案问题）
            document.getElementById('client_type_content').checked = true;

            // 重置卡片选中状态
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => card.classList.remove('selected'));
            document.querySelector('#rejectModal .rejection-type-card[data-type="content"]').classList.add('selected');

            // 初始化驳回类型选择事件
            initClientRejectionTypeSelection();

            // 更新快捷理由和流转说明
            updateClientQuickReasonsAndFlow('content');

            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 初始化客户审核的驳回类型选择
        function initClientRejectionTypeSelection() {
            // 为客户审核的驳回类型卡片添加点击事件
            const typeCards = document.querySelectorAll('#rejectModal .rejection-type-card');
            typeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const type = this.dataset.type;
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                        // 更新卡片样式
                        typeCards.forEach(c => c.classList.remove('selected'));
                        this.classList.add('selected');
                        // 更新快捷理由和流转说明
                        updateClientQuickReasonsAndFlow(type);
                    }
                });
            });
        }

        // 更新客户审核的快捷理由和流转说明
        function updateClientQuickReasonsAndFlow(type) {
            const quickReasonsContainer = document.getElementById('clientQuickReasons');
            const flowText = document.getElementById('clientFlowText');

            // 清空现有的快捷理由
            quickReasonsContainer.innerHTML = '';

            // 根据驳回类型生成快捷理由
            let reasons = [];
            let flowDescription = '';

            switch(type) {
                case 'content':
                    reasons = [
                        '标题不够吸引人，建议重新设计',
                        '内容逻辑不清晰，需要重新组织',
                        '话题标签不合适，需要调整',
                        '文案长度不合适，需要调整',
                        '内容与品牌调性不符，需要调整风格',
                        '缺少关键信息，需要补充完整'
                    ];
                    flowDescription = '选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容';
                    break;
                case 'image':
                    reasons = [
                        '图片质量不符合要求，需要更换',
                        '图片内容与文案不匹配',
                        '图片数量不足，需要补充',
                        '图片尺寸或格式不合适',
                        '图片清晰度不够，影响展示效果',
                        '图片风格与品牌不符'
                    ];
                    flowDescription = '选择"图片问题"：文案将回到<strong>图片管理</strong>，需要重新上传或调整图片';
                    break;
                case 'both':
                    reasons = [
                        '文案和图片都需要重新设计',
                        '整体质量不符合标准，需要全面优化',
                        '内容和视觉效果都需要提升',
                        '文案与图片配合度不够，需要重新匹配',
                        '整体创意需要重新构思',
                        '品牌一致性问题，需要全面调整'
                    ];
                    flowDescription = '选择"两者都有问题"：文案将回到<strong>草稿状态</strong>，需要重新编写文案并上传图片';
                    break;
            }

            // 更新流转说明
            flowText.innerHTML = flowDescription;

            // 生成快捷理由按钮
            reasons.forEach(reason => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'btn btn-outline-secondary btn-sm quick-reason-btn';
                btn.textContent = reason;
                btn.onclick = function() {
                    fillRejectReason(reason);
                    // 切换按钮选中状态
                    this.classList.toggle('selected');
                };
                quickReasonsContainer.appendChild(btn);
            });
        }

        // 通过审核
        function approveContent(contentId) {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent(contentId, 'approve', '');
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            // 获取驳回类型
            const rejectionType = document.querySelector('input[name="rejection_type"]:checked').value;

            reviewContent(currentContentId, 'reject', reason, rejectionType);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 审核内容
        function reviewContent(contentId, action, comment, rejectionType = null) {
            // 禁用所有审核按钮防止重复提交
            const approveBtns = document.querySelectorAll('.btn-approve');
            const rejectBtns = document.querySelectorAll('.btn-reject');
            
            approveBtns.forEach(btn => btn.disabled = true);
            rejectBtns.forEach(btn => btn.disabled = true);

            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);
            
            // 如果是驳回操作，添加驳回类型
            if (action === 'reject' && rejectionType) {
                formData.append('rejection_type', rejectionType);
            }

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // 重新加载当前页面内容
                    refreshCurrentContent();
                    // 更新统计信息
                    loadStats();
                } else {
                    showToast(data.message, 'error');
                    // 重新启用所有按钮
                    approveBtns.forEach(btn => btn.disabled = false);
                    rejectBtns.forEach(btn => btn.disabled = false);
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showToast('网络错误，请稍后重试', 'error');
                // 重新启用所有按钮
                approveBtns.forEach(btn => btn.disabled = false);
                rejectBtns.forEach(btn => btn.disabled = false);
            });
        }

        // 刷新当前内容
        function refreshCurrentContent() {
            currentPage = 1;
            hasMore = true;
            document.getElementById('contentList').innerHTML = '';
            loadContent();
        }

        // 加载更多内容
        function loadMoreContent() {
            loadContent();
        }

        // 显示/隐藏加载更多按钮
        function showLoadMore() {
            document.getElementById('loadMore').style.display = 'block';
        }

        function hideLoadMore() {
            document.getElementById('loadMore').style.display = 'none';
        }

        // 显示/隐藏空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
            hideLoadMore();
        }

        function hideEmptyState() {
            document.getElementById('emptyState').style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            showToast(message, 'error');
        }

        // 显示图片模态框
        function showImageModal(imageUrl) {
            document.getElementById('modalImage').src = imageUrl;
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 监听窗口大小变化，重新渲染内容
        window.addEventListener('resize', function() {
            // 防抖处理
            clearTimeout(window.resizeTimeout);
            window.resizeTimeout = setTimeout(function() {
                refreshCurrentContent();
            }, 300);
        });
    </script>
</body>
</html>
