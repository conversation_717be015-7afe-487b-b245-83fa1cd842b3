#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试文案
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.content import Content
from app.models.user import User

def create_test_content():
    """创建测试文案"""
    app = create_app()
    
    with app.app_context():
        try:
            # 获取第一个用户作为创建者
            user = User.query.first()
            if not user:
                print("❌ 数据库中没有用户，无法创建文案")
                return
            
            print(f"使用用户 {user.username} (ID: {user.id}) 作为创建者")
            
            # 创建测试文案
            test_content = Content(
                title='【API测试】状态更新测试文案',
                content='这是一篇用于测试API状态更新功能的文案。\n\n包含完整的内容和话题标签。\n\n#API测试 #状态更新',
                topics='["API测试", "状态更新", "功能测试"]',
                location='北京·朝阳区',
                workflow_status='pending_publish',
                publish_status='unpublished',
                publish_priority='high',
                display_date=datetime.now().date(),
                display_time=datetime.now().time(),
                client_id=None,
                task_id=None,
                batch_id=None,
                template_id=None,
                created_by=user.id,
                created_at=datetime.now(),
                is_deleted=False
            )
            
            db.session.add(test_content)
            db.session.commit()
            
            print(f"✅ 测试文案创建成功！")
            print(f"文案ID: {test_content.id}")
            print(f"标题: {test_content.title}")
            print(f"状态: {test_content.workflow_status}")
            print(f"优先级: {test_content.publish_priority}")
            
            print(f"\n现在可以使用文案ID {test_content.id} 来测试状态更新API了！")
            
        except Exception as e:
            print(f"创建测试文案失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_test_content()
