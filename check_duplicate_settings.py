#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查重复的系统设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_setting import SystemSetting

def check_duplicate_settings():
    """检查重复的系统设置"""
    print("🔍 检查重复的系统设置...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取所有系统设置
            settings = SystemSetting.get_all_settings()
            print(f"总共有 {len(settings)} 个系统设置")
            
            # 查找图片格式相关的重复设置
            image_format_settings = []
            for setting in settings:
                if 'image' in setting.key.lower() and ('type' in setting.key.lower() or 'format' in setting.key.lower() or 'allowed' in setting.key.lower()):
                    image_format_settings.append(setting)
            
            print(f"\n图片格式相关设置:")
            print("-" * 40)
            for setting in image_format_settings:
                print(f"键名: {setting.key}")
                print(f"值: {setting.value}")
                print(f"描述: {setting.description}")
                print("-" * 20)
            
            # 查找可能重复的设置
            print(f"\n可能重复的设置:")
            print("-" * 40)
            
            # 检查图片格式设置
            image_allowed_types = None
            image_upload_allowed_types = None
            allowed_image_types = None
            
            for setting in settings:
                if setting.key == 'IMAGE_UPLOAD_ALLOWED_TYPES':
                    image_upload_allowed_types = setting
                elif setting.key == 'allowed_image_types':
                    allowed_image_types = setting
                elif setting.key == 'IMAGE_ALLOWED_TYPES':
                    image_allowed_types = setting
            
            duplicates_found = []
            
            if image_upload_allowed_types and allowed_image_types:
                print(f"❌ 发现重复的图片格式设置:")
                print(f"   1. {image_upload_allowed_types.key}: {image_upload_allowed_types.value}")
                print(f"   2. {allowed_image_types.key}: {allowed_image_types.value}")
                duplicates_found.append(('图片格式', [image_upload_allowed_types, allowed_image_types]))
            
            # 检查图片大小设置
            image_max_size = None
            max_upload_size = None
            
            for setting in settings:
                if setting.key == 'IMAGE_UPLOAD_MAX_SIZE':
                    image_max_size = setting
                elif setting.key == 'max_upload_size':
                    max_upload_size = setting
            
            if image_max_size and max_upload_size:
                print(f"❌ 发现重复的图片大小设置:")
                print(f"   1. {image_max_size.key}: {image_max_size.value}")
                print(f"   2. {max_upload_size.key}: {max_upload_size.value}")
                duplicates_found.append(('图片大小', [image_max_size, max_upload_size]))
            
            if not duplicates_found:
                print("✅ 没有发现重复设置")
            else:
                print(f"\n建议处理方案:")
                print("-" * 40)
                for category, settings_list in duplicates_found:
                    print(f"{category}重复设置处理:")
                    for i, setting in enumerate(settings_list):
                        if i == 0:
                            print(f"  保留: {setting.key} (标准命名)")
                        else:
                            print(f"  删除: {setting.key} (重复项)")
                    print()
            
            # 显示所有设置的完整列表
            print(f"\n所有系统设置列表:")
            print("-" * 40)
            for setting in settings:
                print(f"{setting.key}: {setting.value}")
            
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 重复设置检查完成！")

if __name__ == '__main__':
    check_duplicate_settings()
