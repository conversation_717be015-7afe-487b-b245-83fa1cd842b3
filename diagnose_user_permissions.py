#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断用户权限问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.user import User, Role, Permission
from app.models.menu import MenuItem

def diagnose_user_permissions():
    """诊断用户权限问题"""
    print("🔍 诊断用户权限问题...")
    print("=" * 60)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查用户信息
            print("1. 检查用户信息:")
            print("-" * 40)
            
            # 查找template_manager用户
            user = User.query.filter_by(username='template_manager').first()
            if not user:
                print("❌ 找不到template_manager用户")
                return
            
            print(f"📋 用户信息:")
            print(f"  ID: {user.id}")
            print(f"  用户名: {user.username}")
            print(f"  真实姓名: {user.real_name or '未设置'}")
            print(f"  邮箱: {user.email or '未设置'}")
            print(f"  状态: {'激活' if user.is_active else '禁用'}")
            
            # 2. 检查用户角色
            print(f"\n2. 检查用户角色:")
            print("-" * 40)
            
            roles = user.roles
            print(f"📋 用户角色 (共{len(roles)}个):")
            if roles:
                for role in roles:
                    print(f"  ✅ {role.name}: {role.description}")
            else:
                print("  ❌ 用户没有分配任何角色")
            
            # 3. 检查用户权限
            print(f"\n3. 检查用户权限:")
            print("-" * 40)
            
            # 直接权限
            direct_permissions = user.permissions
            print(f"📋 直接权限 (共{len(direct_permissions)}个):")
            if direct_permissions:
                for perm in direct_permissions:
                    print(f"  ✅ {perm.name}: {perm.description}")
            else:
                print("  ❌ 用户没有直接分配的权限")
            
            # 通过角色获得的权限
            role_permissions = []
            for role in roles:
                role_permissions.extend(role.permissions)
            
            print(f"\n📋 角色权限 (共{len(role_permissions)}个):")
            if role_permissions:
                for perm in role_permissions:
                    print(f"  ✅ {perm.name}: {perm.description}")
            else:
                print("  ❌ 用户没有通过角色获得权限")
            
            # 4. 检查关键权限
            print(f"\n4. 检查关键权限:")
            print("-" * 40)
            
            key_permissions = [
                'dashboard_access',
                'template_manage', 
                'client_manage',
                'content_generate',
                'content_manage',
                'image_manage',
                'review.final',
                'publish.manage',  # 关键权限
                'user_manage',
                'system_settings'
            ]
            
            print(f"📋 权限检查结果:")
            for perm_name in key_permissions:
                has_perm = user.has_permission(perm_name)
                status = "✅" if has_perm else "❌"
                print(f"  {status} {perm_name}")
            
            # 5. 检查菜单权限
            print(f"\n5. 检查菜单权限:")
            print("-" * 40)

            if hasattr(user, 'menu_permissions'):
                menu_items = user.menu_permissions
                print(f"📋 用户菜单权限 (共{len(menu_items)}个):")
                if menu_items:
                    for menu in menu_items:
                        print(f"  ✅ {menu.name} ({menu.icon}) - {menu.url}")
                else:
                    print("  ❌ 用户没有菜单权限")
            else:
                print("  ❓ 用户模型没有菜单权限属性")
            
            # 6. 检查系统中的权限
            print(f"\n6. 检查系统权限:")
            print("-" * 40)
            
            all_permissions = Permission.query.all()
            print(f"📋 系统权限列表 (共{len(all_permissions)}个):")
            for perm in all_permissions:
                print(f"  - {perm.name}: {perm.description}")
            
            # 7. 解决方案
            print(f"\n7. 解决方案:")
            print("-" * 40)
            
            if not user.has_permission('publish.manage'):
                print("🔧 问题: 用户缺少 publish.manage 权限")
                print("💡 解决方案:")
                print("  方案1: 直接给用户分配权限")
                print("    1. 登录管理员账号")
                print("    2. 进入用户管理")
                print("    3. 编辑template_manager用户")
                print("    4. 添加 publish.manage 权限")
                
                print(f"\n  方案2: 通过角色分配权限")
                print("    1. 创建或编辑用户角色")
                print("    2. 给角色添加 publish.manage 权限")
                print("    3. 将角色分配给用户")
                
                print(f"\n  方案3: 检查权限名称")
                print("    1. 确认权限名称是否正确")
                print("    2. 可能是 'publish_manage' 而不是 'publish.manage'")
                print("    3. 检查数据库中的实际权限名称")
            
            # 8. 快速修复命令
            print(f"\n8. 快速修复 (如果权限存在):")
            print("-" * 40)
            
            publish_perm = Permission.query.filter_by(name='publish.manage').first()
            if publish_perm:
                print("✅ publish.manage 权限存在")
                print("🔧 可以直接分配给用户")
            else:
                print("❌ publish.manage 权限不存在")
                print("🔍 查找相似权限:")
                similar_perms = Permission.query.filter(Permission.name.like('%publish%')).all()
                if similar_perms:
                    for perm in similar_perms:
                        print(f"  - {perm.name}: {perm.description}")
                else:
                    print("  没有找到包含'publish'的权限")
            
        except Exception as e:
            print(f"❌ 诊断过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 用户权限诊断完成！")
    print("\n📋 诊断总结:")
    print("1. ✅ 检查了用户基本信息")
    print("2. ✅ 检查了用户角色和权限")
    print("3. ✅ 分析了权限问题原因")
    print("4. ✅ 提供了解决方案")
    print("\n🚀 请按照解决方案修复权限问题！")

if __name__ == '__main__':
    diagnose_user_permissions()
