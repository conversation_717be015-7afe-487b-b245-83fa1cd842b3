<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f5f7fa;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 2rem;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 2rem;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.8rem;
        }
        .button-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        button {
            padding: 0.8rem 1.6rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        #fetchBtn {
            background-color: #3498db;
            color: white;
        }
        #simulateBtn {
            background-color: #2ecc71;
            color: white;
        }
        button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        .result-area {
            margin-top: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            min-height: 200px;
        }
        .result-title {
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-size: 1.2rem;
        }
        .result-content {
            white-space: pre-wrap;
            word-break: break-all;
            color: #34495e;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .simulate-form {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e1e4e8;
        }
        .form-title {
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-size: 1.2rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #34495e;
            font-weight: 500;
        }
        input, textarea, select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }
        textarea {
            min-height: 120px;
            resize: vertical;
        }
        .response-example {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #7f8c8d;
            font-style: italic;
        }
        .status-success {
            color: #27ae60;
        }
        .status-error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API测试工具 - 发布状态管理</h1>

        <div class="button-group">
            <button id="fetchBtn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                    <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                </svg>
                获取API信息
            </button>
            <button id="simulateBtn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                    <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
                </svg>
                模拟返回结果
            </button>
        </div>

        <div class="result-area">
            <div class="result-title">API响应结果:</div>
            <div id="resultContent" class="result-content">等待调用API...</div>
        </div>

        <div class="simulate-form">
            <div class="form-title">模拟响应设置:</div>
            <div class="form-group">
                <label for="responseStatus">响应状态:</label>
                <select id="responseStatus">
                    <option value="success">成功</option>
                    <option value="error">失败</option>
                </select>
            </div>
            <div class="form-group">
                <label for="responseData">响应内容(JSON格式):</label>
                <textarea id="responseData">{
    "status": "success",
    "data": {
        "id": 1,
        "title": "小红书爆款文案标题",
        "image_url": "https://example.com/images/sample.jpg",
        "publish_time": "2023-11-15 10:30:00",
        "status": "published",
        "views": 15200,
        "likes": 890,
        "comments": 120
    },
    "message": "获取数据成功"
}</textarea>
                <div class="response-example">示例: 成功状态返回包含标题、图片URL、发布时间等信息</div>
            </div>
        </div>
    </div>

    <script>
        // API调用按钮点击事件
        document.getElementById('fetchBtn').addEventListener('click', async () => {
            const resultElement = document.getElementById('resultContent');
            resultElement.textContent = '加载中...';

            try {
                // 调用API获取数据
                const response = await fetch('http://127.0.0.1:5000/api/v1/publish-status-manage', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'your_actual_api_key_here' // 请替换为系统设置中的实际API密钥
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态码: ${response.status}`);
                }

                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
                resultElement.className = 'result-content status-success';
            } catch (error) {
                resultElement.textContent = `获取失败: ${error.message}`;
                resultElement.className = 'result-content status-error';
            }
        });

        // 模拟响应按钮点击事件
        document.getElementById('simulateBtn').addEventListener('click', () => {
            const status = document.getElementById('responseStatus').value;
            const data = document.getElementById('responseData').value;
            const resultElement = document.getElementById('resultContent');

            try {
                // 尝试解析JSON
                const jsonData = JSON.parse(data);
                resultElement.textContent = JSON.stringify(jsonData, null, 2);
                resultElement.className = `result-content status-${status}`;
            } catch (error) {
                resultElement.textContent = `获取失败: ${error.message}`;
                resultElement.className = 'result-content status-error';
            }
        });
    </script>
</body>
</html>